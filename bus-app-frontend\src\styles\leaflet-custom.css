/* Custom styles for Leaflet and Leaflet Routing Machine */

/* Make the control panel more compact */
.leaflet-routing-container {
  max-width: 320px;
  background-color: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0,0,0,0.2);
  margin-top: 10px !important;
}

/* Hide some elements we don't need */
.leaflet-routing-container h2,
.leaflet-routing-container h3 {
  font-size: 14px;
  margin: 0 0 5px 0;
  font-weight: bold;
  color: #3b82f6;
}

/* Style the waypoints list */
.leaflet-routing-geocoders {
  margin-bottom: 10px;
}

.leaflet-routing-geocoder {
  position: relative;
  margin-bottom: 5px;
}

.leaflet-routing-geocoder input {
  width: 100%;
  padding: 6px 30px 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.leaflet-routing-geocoder button {
  position: absolute;
  right: 0;
  top: 0;
  width: 30px;
  height: 100%;
  background: transparent;
  border: none;
  cursor: pointer;
}

.leaflet-routing-geocoder button:after {
  content: '×';
  display: block;
  color: #999;
  font-size: 18px;
  font-weight: bold;
}

.leaflet-routing-geocoder button:hover:after {
  color: #555;
}

.leaflet-routing-remove-waypoint {
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #f43f5e;
  font-size: 14px;
  padding: 0 5px;
}

.leaflet-routing-add-waypoint {
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #3b82f6;
  font-size: 14px;
  padding: 0 5px;
}

/* Style the route summary */
.leaflet-routing-alt {
  max-height: 200px;
  overflow-y: auto;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.leaflet-routing-alt h2 {
  font-size: 14px;
  margin: 0 0 5px 0;
}

.leaflet-routing-alt-minimized {
  color: #888;
}

.leaflet-routing-alt table {
  width: 100%;
  border-collapse: collapse;
}

.leaflet-routing-alt tr:hover {
  background-color: #f5f5f5;
}

.leaflet-routing-alt tr:first-child {
  border-top: 1px solid #eee;
}

.leaflet-routing-alt td {
  padding: 4px;
  font-size: 12px;
}

/* Style the error message */
.leaflet-routing-error {
  color: #f43f5e;
  padding: 10px;
  font-size: 12px;
  background-color: #fee2e2;
  border-radius: 4px;
  margin-top: 10px;
}

/* Style the markers */
.leaflet-marker-icon {
  border: none !important;
}

/* Make the route line more visible */
.leaflet-routing-line {
  stroke-width: 6 !important;
  stroke-opacity: 0.8 !important;
}

/* Custom marker for waypoints */
.leaflet-routing-icon {
  background-color: #3b82f6;
  border-radius: 50%;
  width: 12px !important;
  height: 12px !important;
  margin-left: -6px !important;
  margin-top: -6px !important;
  border: 2px solid white !important;
  box-shadow: 0 0 4px rgba(0,0,0,0.4);
}

/* Style for the first and last waypoint */
.leaflet-routing-icon-start,
.leaflet-routing-icon-end {
  background-color: #f43f5e;
  width: 16px !important;
  height: 16px !important;
  margin-left: -8px !important;
  margin-top: -8px !important;
}
