/**
 * Test script to verify JWT token authentication
 */

const axios = require('axios');

// Configuration
const config = {
  serverUrl: 'http://localhost:5000',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MGUzNjIzYTBmN2MxMTM2MDM3ODk3YiIsImlhdCI6MTc0NTc2MTgyNywiZXhwIjoxNzQ4MzUzODI3fQ.IRsNREQZhNO0C2zz3QW-zLc1vuRmHTjulXccjBn1BJE', // Add your JWT token here
};

async function testAuthentication() {
  if (!config.token) {
    console.error('Error: JWT token is required. Please add your token to the config.');
    return;
  }

  console.log('Testing authentication...');
  console.log(`Server URL: ${config.serverUrl}`);
  console.log(`Token: ${config.token.substring(0, 20)}...`);

  try {
    // Test public endpoint
    console.log('\nTesting public endpoint...');
    const publicResponse = await axios.get(`${config.serverUrl}/api/test/public`);
    console.log('✅ Public endpoint response:', publicResponse.data);

    // Test authenticated endpoint
    console.log('\nTesting authenticated endpoint...');
    const authResponse = await axios.get(`${config.serverUrl}/api/test/auth`, {
      headers: {
        'Authorization': `Bearer ${config.token}`
      }
    });
    console.log('✅ Authentication successful!');
    console.log('User details:', authResponse.data.user);

    // Test socket authentication endpoint
    console.log('\nTesting socket authentication endpoint...');
    const socketAuthResponse = await axios.get(`${config.serverUrl}/api/socket/auth`, {
      headers: {
        'Authorization': `Bearer ${config.token}`
      }
    });
    console.log('✅ Socket authentication successful!');
    console.log('Response:', socketAuthResponse.data);

    console.log('\nAll authentication tests passed! Your token is valid.');
  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
      console.error('Status code:', error.response.status);
    }
    
    console.log('\nPossible issues:');
    console.log('1. The token may be expired or invalid');
    console.log('2. The server may not be running');
    console.log('3. The server URL may be incorrect');
    console.log('4. The authentication middleware may be misconfigured');
    
    console.log('\nTry generating a new token by logging in:');
    console.log(`POST ${config.serverUrl}/api/users/login`);
    console.log('{ "email": "<EMAIL>", "password": "your-password" }');
  }
}

// Run the test
testAuthentication();
