# OSRM Rate Limiting Fix

## Problem
The application was experiencing HTTP 429 (Too Many Requests) errors from the OSRM routing service (router.project-osrm.org), causing routing failures and console spam.

## Root Causes
1. **No rate limiting**: Requests were made without throttling
2. **`routeWhileDragging: true`**: Caused excessive requests during waypoint dragging
3. **`showAlternatives: true`**: Requested multiple route alternatives, increasing API load
4. **No request debouncing**: Multiple rapid requests could be triggered
5. **Poor error handling**: Rate limit errors weren't handled gracefully

## Solutions Implemented

### 1. Request Throttling
- Added minimum 2-second interval between OSRM requests
- Implemented request queuing with delays
- Added automatic fallback to direct routing when rate limited

### 2. Configuration Changes
- Disabled `routeWhileDragging` to prevent excessive API calls during dragging
- Disabled `useZoomParameter` to reduce request complexity
- Reduced timeout from 15s to 10s

### 3. Debounced Route Updates
- Added 1-second debounce for route updates after waypoint changes
- Separated drag events from route calculation
- Manual route calculation only after drag ends

### 4. Enhanced Error Handling
- Detect HTTP 429 errors and switch to fallback router
- 5-minute cooldown period after rate limiting
- Graceful degradation to direct routing

### 5. User Feedback
- Yellow notification banner when rate limited
- Updated help text to explain rate limiting
- Visual indicators in the help panel

### 6. Fallback Router Improvements
- Added 500ms delay to prevent rapid fallback calls
- Better error handling in fallback scenarios
- Improved logging for debugging

## Files Modified
- `bus-app-frontend/src/components/admin/map/RoutingMap.tsx`
- `bus-app-frontend/src/components/admin/map/fallback-router.ts`

## Testing
1. Create a new route with multiple waypoints
2. Drag waypoints to verify debounced updates
3. Observe rate limiting behavior in console
4. Check that fallback routing works when rate limited
5. Verify user notifications appear when rate limited

## Benefits
- Eliminates HTTP 429 errors
- Reduces API usage and respects rate limits
- Provides better user experience with clear feedback
- Maintains functionality even when rate limited
- Prevents console spam and error flooding

## Future Improvements
- Consider using a paid OSRM service for higher rate limits
- Implement local OSRM server for unlimited requests
- Add route caching to reduce API calls
- Implement exponential backoff for rate limit recovery
