'use client';

import { BusIcon, MapPinIcon, UsersIcon } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

// Define the Bus type
interface Bus {
  id: string;
  busNumber?: string;
  busName?: string;
  status?: string;
  currentLocation?: {
    latitude: number;
    longitude: number;
    heading?: number;
    speed?: number;
    timestamp?: string;
    isPassengerData?: boolean;
  };
  route?: {
    routeName: string;
    routeNumber: string;
  };
  currentPassengerCount?: number;
  totalSeats?: number;
  currentSeatsAvailable?: number;
  isCrowded?: boolean;
  isActive?: boolean;
}

interface BusCardProps {
  bus: Bus;
  isSelected: boolean;
  onSelect: () => void;
}

export function BusCard({ bus, isSelected, onSelect }: BusCardProps) {
  // Calculate occupancy percentage
  const occupancyPercentage =
    bus.currentPassengerCount && bus.totalSeats
      ? Math.round((bus.currentPassengerCount / bus.totalSeats) * 100)
      : null;

  // Determine status color
  const getStatusColor = (status?: string) => {
    if (!status) return 'bg-gray-500'; // Default color if status is undefined or null

    switch (status.toLowerCase()) {
      case 'operational':
        return 'bg-green-500';
      case 'maintenance':
        return 'bg-yellow-500';
      case 'out-of-service':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <Card
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-primary' : ''
      }`}
      onClick={onSelect}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${getStatusColor(bus.status)}`} />
            <CardTitle className="text-lg">{bus.busNumber || 'Unknown'}</CardTitle>
          </div>
          <Badge variant="outline">{bus.route?.routeNumber || 'No Route'}</Badge>
        </div>
      </CardHeader>

      <CardContent className="pb-2">
        <div className="space-y-3">
          <div className="text-sm text-muted-foreground">{bus.busName || 'No name available'}</div>

          {bus.route && (
            <div className="flex items-center gap-2 text-sm">
              <MapPinIcon className="h-4 w-4 text-muted-foreground" />
              <span className="truncate">{bus.route.routeName}</span>
            </div>
          )}

          {occupancyPercentage !== null && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="flex items-center gap-1">
                  <UsersIcon className="h-3 w-3" />
                  Occupancy
                </span>
                <span>{occupancyPercentage}%</span>
              </div>
              <Progress value={occupancyPercentage} className="h-2" />
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter>
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={(e) => {
            e.stopPropagation();
            onSelect();
          }}
        >
          <BusIcon className="h-4 w-4 mr-2" />
          {isSelected ? 'Selected' : 'View on Map'}
        </Button>
      </CardFooter>
    </Card>
  );
}
