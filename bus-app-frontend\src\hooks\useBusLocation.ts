import { useState, useEffect } from 'react';
import { getSocketService } from '@/services/socketService';
import { Bus, getNearbyBuses, NearbyBusesParams } from '@/services/busService';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthToken } from '@/lib/auth';

interface UseBusLocationProps {
  initialLocation?: {
    latitude: number;
    longitude: number;
  };
  radius?: number;
}

interface UseBusLocationResult {
  buses: Bus[];
  loading: boolean;
  error: Error | null;
  refreshBuses: (params: NearbyBusesParams) => Promise<void>;
}

export function useBusLocation({
  initialLocation,
  radius = 1000
}: UseBusLocationProps = {}): UseBusLocationResult {
  const [buses, setBuses] = useState<Bus[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { isAuthenticated } = useAuth();
  const socketService = getSocketService();

  // Function to fetch nearby buses
  const fetchNearbyBuses = async (params: NearbyBusesParams) => {
    try {
      setLoading(true);
      const nearbyBuses = await getNearbyBuses(params);
      setBuses(nearbyBuses);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch nearby buses'));
      console.error('Error fetching nearby buses:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initialize socket connection and fetch initial data
  useEffect(() => {
    const token = getAuthToken();
    if (!isAuthenticated || !token) return;

    const initializeSocketAndFetchBuses = async () => {
      try {
        // Connect to socket
        await socketService.connect(token);

        // If we have initial location, fetch nearby buses and subscribe
        if (initialLocation) {
          await fetchNearbyBuses({
            latitude: initialLocation.latitude,
            longitude: initialLocation.longitude,
            radius
          });

          // Subscribe to nearby buses
          socketService.subscribeToNearbyBuses(
            initialLocation.latitude,
            initialLocation.longitude,
            radius
          );
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to initialize socket connection'));
        console.error('Socket initialization error:', err);
      } finally {
        setLoading(false);
      }
    };

    initializeSocketAndFetchBuses();

    // Cleanup function
    return () => {
      socketService.disconnect();
    };
  }, [isAuthenticated, initialLocation]);

  // Listen for real-time bus location updates
  useEffect(() => {
    if (!isAuthenticated) return;

    const handleBusLocationUpdate = (data: any) => {
      setBuses(prevBuses => {
        // Find if the bus already exists in our list
        const busIndex = prevBuses.findIndex(bus => bus.id === data.busId);

        if (busIndex === -1) {
          // If the bus doesn't exist in our list, we need to fetch its details
          // For now, we'll just add basic info
          return [
            ...prevBuses,
            {
              id: data.busId,
              busNumber: data.bus?.busNumber || 'Unknown',
              busName: data.bus?.busName || 'Unknown Bus',
              status: 'operational',
              currentLocation: {
                latitude: data.latitude,
                longitude: data.longitude,
                heading: data.heading,
                speed: data.speed,
                timestamp: new Date(data.timestamp).toISOString(),
                isPassengerData: data.source === 'passenger'
              },
              totalSeats: 0,
              currentSeatsAvailable: 0,
              currentPassengerCount: 0,
              isCrowded: false,
              isActive: true
            }
          ];
        } else {
          // Update the existing bus with new location
          const updatedBuses = [...prevBuses];
          updatedBuses[busIndex] = {
            ...updatedBuses[busIndex],
            currentLocation: {
              latitude: data.latitude,
              longitude: data.longitude,
              heading: data.heading,
              speed: data.speed,
              timestamp: new Date(data.timestamp).toISOString(),
              isPassengerData: data.source === 'passenger'
            }
          };
          return updatedBuses;
        }
      });
    };

    // Register the event listener
    socketService.onBusLocationUpdate(handleBusLocationUpdate);

    // Cleanup function
    return () => {
      socketService.offBusLocationUpdate(handleBusLocationUpdate);
    };
  }, [isAuthenticated]);

  // Function to refresh buses data
  const refreshBuses = async (params: NearbyBusesParams) => {
    await fetchNearbyBuses(params);

    // Update socket subscription
    socketService.updateLocation(
      params.latitude,
      params.longitude,
      params.radius
    );
  };

  return {
    buses,
    loading,
    error,
    refreshBuses
  };
}
