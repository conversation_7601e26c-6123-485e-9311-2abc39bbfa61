const { prisma } = require('../config/db');
const firebaseService = require('../services/firebaseService');

/**
 * Get all notifications for the current user
 * @route GET /api/notifications
 * @access Private
 */
const getUserNotifications = async (req, res) => {
  try {
    const notifications = await prisma.notification.findMany({
      where: { userId: req.user.id },
      orderBy: { createdAt: 'desc' },
    });

    res.json(notifications);
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Mark a notification as read
 * @route PUT /api/notifications/:id/read
 * @access Private
 */
const markNotificationAsRead = async (req, res) => {
  try {
    const notification = await prisma.notification.findUnique({
      where: { id: req.params.id },
    });

    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    // Check if notification belongs to the user
    if (notification.userId !== req.user.id) {
      return res.status(403).json({ message: 'Not authorized to access this notification' });
    }

    // Update notification
    const updatedNotification = await prisma.notification.update({
      where: { id: req.params.id },
      data: { isRead: true },
    });

    res.json(updatedNotification);
  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Mark all notifications as read for the current user
 * @route PUT /api/notifications/read-all
 * @access Private
 */
const markAllNotificationsAsRead = async (req, res) => {
  try {
    await prisma.notification.updateMany({
      where: { 
        userId: req.user.id,
        isRead: false
      },
      data: { isRead: true },
    });

    res.json({ message: 'All notifications marked as read' });
  } catch (error) {
    console.error('Mark all notifications as read error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Delete a notification
 * @route DELETE /api/notifications/:id
 * @access Private
 */
const deleteNotification = async (req, res) => {
  try {
    const notification = await prisma.notification.findUnique({
      where: { id: req.params.id },
    });

    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    // Check if notification belongs to the user
    if (notification.userId !== req.user.id) {
      return res.status(403).json({ message: 'Not authorized to delete this notification' });
    }

    // Delete notification
    await prisma.notification.delete({
      where: { id: req.params.id },
    });

    res.json({ message: 'Notification deleted' });
  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get notification preferences for the current user
 * @route GET /api/notifications/preferences
 * @access Private
 */
const getNotificationPreferences = async (req, res) => {
  try {
    let preferences = await prisma.notificationPreference.findUnique({
      where: { userId: req.user.id },
    });

    // If preferences don't exist, create default preferences
    if (!preferences) {
      preferences = await prisma.notificationPreference.create({
        data: {
          userId: req.user.id,
          enabled: true,
          loginAlerts: true,
          welcomeMessage: true,
          serviceUpdates: true,
          busDelays: true,
          promotions: true,
        },
      });
    }

    res.json(preferences);
  } catch (error) {
    console.error('Get notification preferences error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update notification preferences for the current user
 * @route PUT /api/notifications/preferences
 * @access Private
 */
const updateNotificationPreferences = async (req, res) => {
  try {
    const { 
      enabled, 
      loginAlerts, 
      welcomeMessage, 
      serviceUpdates, 
      busDelays, 
      promotions 
    } = req.body;

    // Check if preferences exist
    let preferences = await prisma.notificationPreference.findUnique({
      where: { userId: req.user.id },
    });

    // If preferences don't exist, create them
    if (!preferences) {
      preferences = await prisma.notificationPreference.create({
        data: {
          userId: req.user.id,
          enabled: enabled !== undefined ? enabled : true,
          loginAlerts: loginAlerts !== undefined ? loginAlerts : true,
          welcomeMessage: welcomeMessage !== undefined ? welcomeMessage : true,
          serviceUpdates: serviceUpdates !== undefined ? serviceUpdates : true,
          busDelays: busDelays !== undefined ? busDelays : true,
          promotions: promotions !== undefined ? promotions : true,
        },
      });
    } else {
      // Update existing preferences
      preferences = await prisma.notificationPreference.update({
        where: { userId: req.user.id },
        data: {
          enabled: enabled !== undefined ? enabled : preferences.enabled,
          loginAlerts: loginAlerts !== undefined ? loginAlerts : preferences.loginAlerts,
          welcomeMessage: welcomeMessage !== undefined ? welcomeMessage : preferences.welcomeMessage,
          serviceUpdates: serviceUpdates !== undefined ? serviceUpdates : preferences.serviceUpdates,
          busDelays: busDelays !== undefined ? busDelays : preferences.busDelays,
          promotions: promotions !== undefined ? promotions : preferences.promotions,
        },
      });
    }

    res.json(preferences);
  } catch (error) {
    console.error('Update notification preferences error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Send a test notification to the current user
 * @route POST /api/notifications/test
 * @access Private
 */
const sendTestNotification = async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { id: true, name: true, deviceToken: true },
    });

    if (!user.deviceToken) {
      return res.status(400).json({ 
        message: 'No device token found. Please update your profile with a valid device token.' 
      });
    }

    const notification = {
      title: 'Test Notification',
      body: `Hello ${user.name || 'there'}! This is a test notification.`,
    };

    const data = {
      type: 'test',
      timestamp: Date.now().toString(),
    };

    const result = await firebaseService.sendNotificationToUser(
      user.id,
      notification,
      data
    );

    if (!result) {
      return res.status(400).json({ 
        message: 'Failed to send notification. Check your device token and notification preferences.' 
      });
    }

    res.json({ 
      message: 'Test notification sent successfully',
      result 
    });
  } catch (error) {
    console.error('Send test notification error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  getNotificationPreferences,
  updateNotificationPreferences,
  sendTestNotification,
};
