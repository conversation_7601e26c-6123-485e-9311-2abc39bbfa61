const socketIO = require('socket.io');
const jwt = require('jsonwebtoken');
const { prisma } = require('../config/db');

let io;

/**
 * Initialize Socket.IO server
 * @param {Object} server - HTTP server instance
 */
const initializeSocketIO = (server) => {
  io = socketIO(server, {
    cors: {
      origin: process.env.CLIENT_URL || '*',
      methods: ['GET', 'POST'],
      credentials: true
    }
  });

  // Authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.query.token;

      if (!token) {
        return next(new Error('Authentication error: Token not provided'));
      }

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret');

      // Get user from token
      const user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
        },
      });

      if (!user) {
        return next(new Error('Authentication error: User not found'));
      }

      // Attach user to socket
      socket.user = user;
      next();
    } catch (error) {
      console.error('Socket authentication error:', error);
      next(new Error('Authentication error: Invalid token'));
    }
  });

  // Set up namespaces
  setupNamespaces(io);

  console.log('Socket.IO initialized');
  return io;
};

/**
 * Set up Socket.IO namespaces
 * @param {Object} io - Socket.IO instance
 */
const setupNamespaces = (io) => {
  // Main namespace for general connections
  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user.id} (${socket.user.role})`);

    // Join user-specific room
    socket.join(`user:${socket.user.id}`);

    // Join role-based room
    socket.join(`role:${socket.user.role}`);

    // Handle disconnection
    socket.on('disconnect', () => {
      if (socket.user) {
        console.log(`User disconnected: ${socket.user.id}`);
      } else {
        console.log('User disconnected (unknown user)');
      }
    });
  });

  // Bus location namespace
  const busLocationNamespace = io.of('/bus-locations');

  // Apply authentication middleware to bus-locations namespace
  busLocationNamespace.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.query.token;

      if (!token) {
        return next(new Error('Authentication error: Token not provided'));
      }

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret');

      // Get user from token
      const user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
        },
      });

      if (!user) {
        return next(new Error('Authentication error: User not found'));
      }

      // Attach user to socket
      socket.user = user;
      next();
    } catch (error) {
      console.error('Socket authentication error:', error);
      next(new Error('Authentication error: Invalid token'));
    }
  });

  busLocationNamespace.on('connection', (socket) => {
    console.log(`User connected to bus-locations: ${socket.user.id} (${socket.user.role})`);

    // Subscribe to specific bus updates
    socket.on('subscribe-bus', (busId) => {
      socket.join(`bus:${busId}`);
      console.log(`User ${socket.user.id} subscribed to bus ${busId}`);
    });

    // Unsubscribe from specific bus updates
    socket.on('unsubscribe-bus', (busId) => {
      socket.leave(`bus:${busId}`);
      console.log(`User ${socket.user.id} unsubscribed from bus ${busId}`);
    });

    // Subscribe to route updates (all buses on a route)
    socket.on('subscribe-route', (routeId) => {
      socket.join(`route:${routeId}`);
      console.log(`User ${socket.user.id} subscribed to route ${routeId}`);
    });

    // Unsubscribe from route updates
    socket.on('unsubscribe-route', (routeId) => {
      socket.leave(`route:${routeId}`);
      console.log(`User ${socket.user.id} unsubscribed from route ${routeId}`);
    });

    // Subscribe to nearby buses (based on user location)
    socket.on('subscribe-nearby', (data) => {
      // Store user's location for nearby calculations
      socket.userData = {
        ...socket.userData,
        latitude: data.latitude,
        longitude: data.longitude,
        radius: data.radius || 1000 // Default 1km radius
      };

      socket.join('nearby-buses');
      console.log(`User ${socket.user.id} subscribed to nearby buses`);
    });

    // Update user location for nearby calculations
    socket.on('update-location', (data) => {
      socket.userData = {
        ...socket.userData,
        latitude: data.latitude,
        longitude: data.longitude,
        radius: data.radius || socket.userData?.radius || 1000
      };
    });

    // Handle ping for testing
    socket.on('ping', (data, callback) => {
      console.log(`Ping received from user ${socket.user.id}:`, data);
      if (callback && typeof callback === 'function') {
        callback({
          pong: true,
          time: Date.now(),
          user: socket.user.id,
          rooms: Array.from(socket.rooms)
        });
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      if (socket.user) {
        console.log(`User disconnected from bus-locations: ${socket.user.id}`);
      } else {
        console.log('User disconnected from bus-locations (unknown user)');
      }
    });
  });
};

/**
 * Emit a debug message to all connected clients
 * @param {String} message - Debug message
 * @param {Object} data - Additional data
 */
const emitDebugMessage = (message, data = {}) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  try {
    const busLocationNamespace = io.of('/bus-locations');
    busLocationNamespace.emit('debug', { message, data, timestamp: Date.now() });
    console.log(`Debug message emitted: ${message}`);
  } catch (error) {
    console.error('Error emitting debug message:', error);
  }
};

/**
 * Emit bus location update to all subscribed clients
 * @param {String} busId - Bus ID
 * @param {Object} locationData - Bus location data
 */
const emitBusLocationUpdate = async (busId, locationData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit debug message
  emitDebugMessage(`Processing location update for bus ${busId}`, {
    busId,
    latitude: locationData.latitude,
    longitude: locationData.longitude,
    source: locationData.source
  });

  try {
    // Get bus details
    const bus = await prisma.bus.findUnique({
      where: { id: busId },
      select: {
        id: true,
        busNumber: true,
        busName: true,
        routeId: true,
      },
    });

    if (!bus) {
      console.error(`Bus not found: ${busId}`);
      return;
    }

    const busLocationNamespace = io.of('/bus-locations');

    // Emit to bus-specific room
    busLocationNamespace.to(`bus:${busId}`).emit('bus-location-update', {
      ...locationData,
      bus
    });

    // Emit to route room if bus has a route
    if (bus.routeId) {
      busLocationNamespace.to(`route:${bus.routeId}`).emit('bus-location-update', {
        ...locationData,
        bus
      });
    }

    // Emit to nearby-buses room (filtered on the client side)
    busLocationNamespace.to('nearby-buses').emit('bus-location-update', {
      ...locationData,
      bus
    });

    // Emit to all drivers and admins
    busLocationNamespace.to('role:driver').to('role:admin').emit('bus-location-update', {
      ...locationData,
      bus
    });

    console.log(`Emitted location update for bus ${busId}`);
  } catch (error) {
    console.error('Error emitting bus location update:', error);
  }
};

/**
 * Emit passenger location contribution to relevant drivers and admins
 * @param {String} busId - Bus ID
 * @param {Object} contributionData - Passenger location contribution data
 */
const emitPassengerContribution = (busId, contributionData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  try {
    const busLocationNamespace = io.of('/bus-locations');

    // Emit to bus-specific room (for drivers of this bus)
    busLocationNamespace.to(`bus:${busId}`).emit('passenger-contribution', contributionData);

    // Emit to all admins
    busLocationNamespace.to('role:admin').emit('passenger-contribution', {
      ...contributionData,
      busId
    });
  } catch (error) {
    console.error('Error emitting passenger contribution:', error);
  }
};

/**
 * Get Socket.IO instance
 * @returns {Object} Socket.IO instance
 */
const getIO = () => {
  if (!io) {
    throw new Error('Socket.IO not initialized');
  }
  return io;
};

module.exports = {
  initializeSocketIO,
  getIO,
  emitBusLocationUpdate,
  emitPassengerContribution,
  emitDebugMessage
};
