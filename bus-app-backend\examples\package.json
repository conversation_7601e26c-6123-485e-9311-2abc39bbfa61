{"name": "bus-tracking-examples", "version": "1.0.0", "description": "Example scripts for testing the bus tracking WebSocket implementation", "scripts": {"create-users": "node create-test-users.js", "create-bus": "node create-test-bus.js", "test-auth": "node test-auth.js", "test-connection": "node test-websocket-connection.js", "test-bus-movement": "node test-bus-movement.js", "test-passenger": "node test-passenger-contributions.js", "test-notifications": "node test-notifications.js"}, "dependencies": {"axios": "^1.6.2", "socket.io-client": "^4.7.2"}}