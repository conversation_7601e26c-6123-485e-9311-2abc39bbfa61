"use client"

import { useEffect, useRef, useState } from "react"
import { MapIcon } from "lucide-react"
import { createFallbackRouter } from "./fallback-router"
import "@/styles/leaflet-custom.css"

// Add type declarations for Leaflet Routing Machine
declare global {
  namespace L {
    namespace Routing {
      function control(options: any): any
      function osrmv1(options: any): any

      interface Waypoint {
        latLng: L.LatLng
      }
    }
  }
}

// Polyline encoding/decoding utility
const PolylineUtil = {
  encode: function (points: any[], precision?: number) {
    if (!points.length) return ""

    const factor = Math.pow(10, precision !== undefined ? precision : 5)

    let output = this.encodePoint(points[0].lat || points[0][0], points[0].lng || points[0][1], factor)

    for (let i = 1; i < points.length; i++) {
      const a = points[i - 1]
      const b = points[i]
      output += this.encodePoint(b.lat || b[0], b.lng || b[1], factor, a.lat || a[0], a.lng || a[1])
    }

    return output
  },

  encodePoint: function (lat: number, lng: number, factor: number, prevLat?: number, prevLng?: number) {
    // Round to the nearest factor
    lat = Math.round(lat * factor)
    lng = Math.round(lng * factor)

    // Delta encode
    const deltaLat = lat - (prevLat !== undefined ? Math.round(prevLat * factor) : 0)
    const deltaLng = lng - (prevLng !== undefined ? Math.round(prevLng * factor) : 0)

    return this.encodeSignedNumber(deltaLat) + this.encodeSignedNumber(deltaLng)
  },

  encodeSignedNumber: function (num: number) {
    let sgn_num = num << 1
    if (num < 0) {
      sgn_num = ~sgn_num
    }
    return this.encodeNumber(sgn_num)
  },

  encodeNumber: (num: number) => {
    let encodeString = ""
    while (num >= 0x20) {
      encodeString += String.fromCharCode((0x20 | (num & 0x1f)) + 63)
      num >>= 5
    }
    encodeString += String.fromCharCode(num + 63)
    return encodeString
  },

  decode: (encoded: string, precision?: number) => {
    if (!encoded.length) return []

    const factor = Math.pow(10, precision !== undefined ? precision : 5)
    const len = encoded.length
    let index = 0
    let lat = 0
    let lng = 0
    const points = []

    while (index < len) {
      let b
      let shift = 0
      let result = 0

      do {
        b = encoded.charCodeAt(index++) - 63
        result |= (b & 0x1f) << shift
        shift += 5
      } while (b >= 0x20)

      const deltaLat = result & 1 ? ~(result >> 1) : result >> 1
      lat += deltaLat

      shift = 0
      result = 0

      do {
        b = encoded.charCodeAt(index++) - 63
        result |= (b & 0x1f) << shift
        shift += 5
      } while (b >= 0x20)

      const deltaLng = result & 1 ? ~(result >> 1) : result >> 1
      lng += deltaLng

      points.push([lat / factor, lng / factor])
    }

    return points
  },
}

interface RoutingMapProps {
  initialPolyline?: string
  onPolylineChange: (polyline: string) => void
  center?: [number, number] // [latitude, longitude]
  zoom?: number
  readOnly?: boolean
}

export function RoutingMap({
  initialPolyline,
  onPolylineChange,
  center = [19.076, 72.8777], // Default center at Mumbai
  zoom = 12,
  readOnly = false,
}: RoutingMapProps) {
  // Extend HTMLDivElement to include Leaflet properties
  interface MapDiv extends HTMLDivElement {
    _leaflet_id?: number
  }

  const mapRef = useRef<MapDiv>(null)
  const mapInstanceRef = useRef<any>(null)
  const routingControlRef = useRef<any>(null)
  const mapContainerKey = useRef<string>(Math.random().toString(36).substring(2, 11))

  // Track if component is mounted
  const isMounted = useRef(true)
  const [mapReady, setMapReady] = useState(false)
  const [routingStatus, setRoutingStatus] = useState<'normal' | 'rate-limited' | 'fallback'>('normal')

  // Effect to check if map container is ready
  useEffect(() => {
    if (typeof window === "undefined" || !mapRef.current) return

    // Check if map container has dimensions
    const checkMapContainer = () => {
      if (mapRef.current && mapRef.current.clientWidth > 0 && mapRef.current.clientHeight > 0) {
        setMapReady(true)
      } else {
        // Try again in a moment
        setTimeout(checkMapContainer, 100)
      }
    }

    checkMapContainer()

    return () => {
      setMapReady(false)
    }
  }, [])

  // Effect to handle component mount/unmount
  useEffect(() => {
    // Set mounted flag
    isMounted.current = true

    return () => {
      // Set unmounted flag
      isMounted.current = false

      // Clean up map resources
      if (mapInstanceRef.current) {
        try {
          if (routingControlRef.current) {
            mapInstanceRef.current.removeControl(routingControlRef.current)
            routingControlRef.current = null
          }
          mapInstanceRef.current.remove()
          mapInstanceRef.current = null
        } catch (e) {
          console.warn("Error cleaning up map on unmount:", e)
        }
      }
    }
  }, [])

  // Initialize the map
  useEffect(() => {
    if (typeof window === "undefined" || !mapRef.current || !mapReady) return

    let map: any = null
    let routing: any = null

    // Dynamically import Leaflet
    const initMap = async () => {
      try {
        console.log("Initializing Leaflet map with routing...")

        // Check if map is already initialized and clean it up first
        if (mapInstanceRef.current) {
          console.log("Map already exists, cleaning up first...")
          try {
            if (routingControlRef.current) {
              mapInstanceRef.current.removeControl(routingControlRef.current)
              routingControlRef.current = null
            }
            mapInstanceRef.current.remove()
            mapInstanceRef.current = null
          } catch (e) {
            console.warn("Error cleaning up existing map:", e)
          }
        }

        // Clear any previous content
        if (mapRef.current) {
          mapRef.current.innerHTML = ""
          // Remove any Leaflet-specific attributes
          if (mapRef.current._leaflet_id) {
            delete mapRef.current._leaflet_id
          }
        }

        // Import libraries
        const L = (await import("leaflet")).default
        console.log("Leaflet loaded successfully")

        // Handle CSS imports
        try {
          // @ts-ignore - CSS imports
          await import("leaflet/dist/leaflet.css")
          // @ts-ignore - CSS imports
          await import("leaflet-routing-machine/dist/leaflet-routing-machine.css")
          console.log("Leaflet CSS loaded")

          // Fix Leaflet's default icon paths
          delete (L.Icon.Default.prototype as any)._getIconUrl
          L.Icon.Default.mergeOptions({
            iconRetinaUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png",
            iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
            shadowUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png",
          })
        } catch (e) {
          console.error("CSS imports failed:", e)
          if (mapRef.current) {
            mapRef.current.innerHTML =
              '<div style="padding: 20px; color: red;">Error loading map styles. Please check console.</div>'
          }
          return
        }

        // Make sure the map container is ready and has dimensions
        if (!mapRef.current || !mapRef.current.clientWidth || !mapRef.current.clientHeight) {
          console.error("Map container not ready or has zero dimensions")
          return
        }

        // Create map with additional safeguards
        map = L.map(mapRef.current, {
          center: center,
          zoom: zoom,
          zoomControl: true,
          attributionControl: true,
          fadeAnimation: false,
          markerZoomAnimation: false,
          preferCanvas: true,
        })

        // Store map instance in ref
        mapInstanceRef.current = map
        console.log("Map created successfully")

        // Force a resize event after a short delay to ensure the map renders correctly
        setTimeout(() => {
          if (isMounted.current && map) {
            map.invalidateSize()
            console.log("Map size invalidated")
          }
        }, 300)

        // Add tile layer
        L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19,
        }).addTo(map)

        // Import Leaflet Routing Machine
        try {
          await import("leaflet-routing-machine")
          console.log("Leaflet Routing Machine loaded")
        } catch (error) {
          console.error("Error loading Leaflet Routing Machine:", error)
          if (mapRef.current) {
            mapRef.current.innerHTML =
              '<div style="padding: 20px; color: red;">Error loading routing engine. Please check console.</div>'
          }
          return
        }

        // Initialize waypoints
        let initialWaypoints: any[] = []

        // If initial polyline is provided, decode it to waypoints
        if (initialPolyline && initialPolyline.length > 0) {
          try {
            const decodedPath = PolylineUtil.decode(initialPolyline)
            // Convert to waypoints (use first, last, and some points in between)
            if (decodedPath.length > 0) {
              // Always include first and last points
              initialWaypoints.push(L.latLng(decodedPath[0][0], decodedPath[0][1]))

              // Add some intermediate points if there are many
              if (decodedPath.length > 10) {
                const step = Math.floor(decodedPath.length / 5)
                for (let i = step; i < decodedPath.length - step; i += step) {
                  initialWaypoints.push(L.latLng(decodedPath[i][0], decodedPath[i][1]))
                }
              }

              // Add last point
              if (decodedPath.length > 1) {
                initialWaypoints.push(
                  L.latLng(decodedPath[decodedPath.length - 1][0], decodedPath[decodedPath.length - 1][1]),
                )
              }
            }
          } catch (error) {
            console.error("Error decoding polyline:", error)
            // If decoding fails, start with empty waypoints
            initialWaypoints = []
          }
        }

        // If no waypoints from polyline, start with empty or default waypoints
        if (initialWaypoints.length === 0 && !readOnly) {
          // For new routes, start with two points in Mumbai
          initialWaypoints = [
            L.latLng(19.076, 72.8777), // Mumbai Central
            L.latLng(19.1136, 72.9023), // Bandra East
          ]
        }

        // Create a fallback router
        const fallbackRouter = createFallbackRouter()

        // Ensure waypoints have valid coordinates
        initialWaypoints = initialWaypoints.filter((wp) => {
          return (
            wp &&
            wp.lat &&
            wp.lng &&
            typeof wp.lat === "number" &&
            typeof wp.lng === "number" &&
            !isNaN(wp.lat) &&
            !isNaN(wp.lng)
          )
        })

        // If we lost waypoints in filtering, add default ones
        if (initialWaypoints.length < 2 && !readOnly) {
          initialWaypoints = [
            L.latLng(19.076, 72.8777), // Mumbai Central
            L.latLng(19.1136, 72.9023), // Bandra East
          ]
        }

        // Create a throttled OSRM router with rate limiting
        let router
        let lastRequestTime = 0
        const MIN_REQUEST_INTERVAL = 2000 // Minimum 2 seconds between requests
        let rateLimitHit = false

        try {
          // Create a custom router that wraps OSRM with rate limiting
          const osrmRouter = L.Routing.osrmv1({
            serviceUrl: "https://router.project-osrm.org/route/v1",
            profile: "driving",
            timeout: 10 * 1000, // Reduced timeout to 10 seconds
            suppressDemoServerWarning: true,
          })

          // Wrap the original route method with throttling
          const originalRoute = osrmRouter.route
          router = {
            ...osrmRouter,
            route: function(waypoints: any[], callback: Function, context?: any) {
              const now = Date.now()

              // If we hit rate limit recently, use fallback
              if (rateLimitHit) {
                console.log("Using fallback router due to recent rate limit")
                setRoutingStatus('rate-limited')
                return fallbackRouter.route(waypoints, callback)
              }

              // Create a wrapped callback that handles rate limiting
              const wrappedCallback = (error: any, routes: any) => {
                if (error && (error.status === 429 || error.message?.includes('429') || error.toString().includes('429'))) {
                  console.warn("Rate limit hit, switching to fallback router")
                  rateLimitHit = true
                  setRoutingStatus('rate-limited')
                  // Reset rate limit flag after 5 minutes
                  setTimeout(() => {
                    rateLimitHit = false
                    setRoutingStatus('normal')
                    console.log("Rate limit cooldown complete")
                  }, 5 * 60 * 1000)

                  return fallbackRouter.route(waypoints, callback)
                }

                if (!error) {
                  setRoutingStatus('normal')
                }

                callback(error, routes)
              }

              // Check if enough time has passed since last request
              const timeSinceLastRequest = now - lastRequestTime
              if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
                const delay = MIN_REQUEST_INTERVAL - timeSinceLastRequest
                console.log(`Throttling request, waiting ${delay}ms`)

                setTimeout(() => {
                  lastRequestTime = Date.now()
                  originalRoute.call(this, waypoints, wrappedCallback, context)
                }, delay)
                return
              }

              lastRequestTime = now
              originalRoute.call(this, waypoints, wrappedCallback, context)
            }
          }
        } catch (error) {
          console.warn("Failed to create OSRM router, using fallback:", error)
          router = fallbackRouter
        }

        // Create a simpler routing control without custom markers
        // @ts-ignore - TypeScript definitions for Leaflet Routing Machine are incomplete
        routing = L.Routing.control({
          waypoints: initialWaypoints,
          routeWhileDragging: false, // Disabled to prevent excessive API calls
          showAlternatives: false,
          fitSelectedRoutes: true,
          lineOptions: {
            styles: [{ color: "#3b82f6", weight: 5 }],
            extendToWaypoints: true,
            missingRouteTolerance: 0,
          },
          addWaypoints: !readOnly,
          draggableWaypoints: !readOnly,
          useZoomParameter: false, // Disabled to reduce request complexity
          router: router,
          createMarker: (i: number, wp: any, n: number) => {
            // Create custom markers for start, end, and intermediate points
            let icon

            if (i === 0) {
              // Start point (green)
              icon = L.divIcon({
                className: "custom-waypoint-icon start-icon",
                html: '<div style="background-color: #10b981; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);"></div>',
                iconSize: [16, 16],
                iconAnchor: [8, 8],
              })
            } else if (i === n - 1) {
              // End point (red)
              icon = L.divIcon({
                className: "custom-waypoint-icon end-icon",
                html: '<div style="background-color: #ef4444; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);"></div>',
                iconSize: [16, 16],
                iconAnchor: [8, 8],
              })
            } else {
              // Intermediate points (blue)
              icon = L.divIcon({
                className: "custom-waypoint-icon",
                html: '<div style="background-color: #3b82f6; width: 10px; height: 10px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);"></div>',
                iconSize: [14, 14],
                iconAnchor: [7, 7],
              })
            }

            return L.marker(wp.latLng, {
              draggable: !readOnly,
              icon: icon,
            })
          },
        })

        // Only add the routing control to the map if the map exists
        if (map) {
          routing.addTo(map)
          // Store routing control in ref for cleanup
          routingControlRef.current = routing
        } else {
          console.error("Cannot add routing control - map is null")
          return
        }

        // Handle route changes
        routing.on("routesfound", (e: any) => {
          if (readOnly || !isMounted.current) return

          const routes = e.routes
          if (routes && routes.length > 0) {
            const selectedRoute = routes[0] // Use the first (best) route
            const coordinates = selectedRoute.coordinates

            // Convert coordinates to format for encoding
            const points = coordinates.map((coord: any) => [coord.lat, coord.lng])

            // Encode the polyline
            const encoded = PolylineUtil.encode(points)
            onPolylineChange(encoded)
          }
        })

        // Add click handler to the map for adding waypoints manually
        if (!readOnly && map) {
          // Add custom controls to help users

          // Help button
          const helpControl = L.Control.extend({
            options: {
              position: "topright",
            },

            onAdd: () => {
              const container = L.DomUtil.create("div", "leaflet-bar leaflet-control leaflet-control-custom")
              container.style.backgroundColor = "white"
              container.style.padding = "6px 8px"
              container.style.fontSize = "14px"
              container.style.cursor = "pointer"
              container.style.borderRadius = "4px"
              container.style.boxShadow = "0 1px 5px rgba(0,0,0,0.4)"
              container.style.marginBottom = "10px"
              container.innerHTML =
                '<div style="display: flex; align-items: center;"><span style="background-color: #3b82f6; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 5px;">?</span> Help</div>'

              container.onclick = () => {
                alert(
                  "How to create a route:\n\n1. Click anywhere on the map to add waypoints\n2. Drag waypoints to adjust the route\n3. The route will automatically follow roads\n4. If road routing fails or rate limits are hit, a direct line will be shown\n5. Rate limiting prevents excessive API calls - wait a few minutes if you see the yellow warning",
                )
              }

              return container
            },
          })

          // Direct route button (for when routing fails)
          const directRouteControl = L.Control.extend({
            options: {
              position: "topright",
            },

            onAdd: () => {
              const container = L.DomUtil.create("div", "leaflet-bar leaflet-control leaflet-control-custom")
              container.style.backgroundColor = "white"
              container.style.padding = "6px 8px"
              container.style.fontSize = "14px"
              container.style.cursor = "pointer"
              container.style.borderRadius = "4px"
              container.style.boxShadow = "0 1px 5px rgba(0,0,0,0.4)"
              container.innerHTML =
                '<div style="display: flex; align-items: center;"><span style="background-color: #ef4444; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 5px;">↔</span> Direct Route</div>'

              container.onclick = () => {
                // Create a direct route between waypoints
                if (!routing) return

                const waypoints = routing
                  .getWaypoints()
                  .filter(
                    (wp: any) =>
                      wp && wp.latLng && typeof wp.latLng.lat === "number" && typeof wp.latLng.lng === "number",
                  )

                if (waypoints.length >= 2) {
                  // Create a direct polyline between waypoints
                  if (map) {
                    L.polyline(
                      waypoints.map((wp: any) => [wp.latLng.lat, wp.latLng.lng]),
                      {
                        color: "#ef4444",
                        weight: 5,
                        opacity: 0.7,
                        dashArray: "10, 10", // Make it dashed to indicate it's a direct route
                        lineCap: "round",
                      },
                    ).addTo(map)
                  }

                  // Convert waypoints to format for encoding
                  const points = waypoints.map((wp: any) => [wp.latLng.lat, wp.latLng.lng])

                  // Encode the polyline
                  const encoded = PolylineUtil.encode(points)
                  onPolylineChange(encoded)

                  alert("Created direct route between waypoints (not following roads)")
                } else {
                  alert("Please add at least 2 waypoints first")
                }
              }

              return container
            },
          })

          try {
            new helpControl().addTo(map)
            new directRouteControl().addTo(map)
          } catch (e) {
            console.warn("Could not add custom controls:", e)
          }

          // Add debounced route update mechanism
          let routeUpdateTimeout: NodeJS.Timeout | null = null
          const debouncedRouteUpdate = () => {
            if (routeUpdateTimeout) {
              clearTimeout(routeUpdateTimeout)
            }
            routeUpdateTimeout = setTimeout(() => {
              if (routing && isMounted.current) {
                try {
                  // Trigger route calculation manually
                  const waypoints = routing.getWaypoints()
                  if (waypoints.length >= 2) {
                    routing.route()
                  }
                } catch (error) {
                  console.error("Error updating route:", error)
                }
              }
            }, 1000) // Wait 1 second after last waypoint change
          }

          // Add click handler to the map
          map.on("click", (e: any) => {
            if (!isMounted.current || !routing) return

            try {
              const waypoints = routing.getWaypoints()
              const newWaypoint = L.Routing.waypoint(e.latlng, "Waypoint " + (waypoints.length + 1))

              // Add the new waypoint to the end of the route
              routing.spliceWaypoints(waypoints.length, 0, newWaypoint)

              // Trigger debounced route update
              debouncedRouteUpdate()
            } catch (error) {
              console.error("Error handling map click:", error)
            }
          })

          // Add waypoint drag end handler for debounced updates
          if (routing) {
            routing.on('waypointdrag', () => {
              // Don't update route while dragging
            })

            routing.on('waypointdragend', () => {
              // Update route after drag ends
              debouncedRouteUpdate()
            })
          }
        }

        // Handle routing errors
        if (routing) {
          routing.on("routingerror", (e: any) => {
            if (!isMounted.current || !map) return

            console.error("Routing error:", e.error)

            // Create a simple direct route between waypoints
            try {
              // Get current waypoints
              if (!routing) return

              const currentWaypoints = routing
                .getWaypoints()
                .filter(
                  (wp: any) =>
                    wp && wp.latLng && typeof wp.latLng.lat === "number" && typeof wp.latLng.lng === "number",
                )

              if (currentWaypoints.length >= 2) {
                // Create a direct polyline between waypoints
                L.polyline(
                  currentWaypoints.map((wp: any) => [wp.latLng.lat, wp.latLng.lng]),
                  {
                    color: "#3b82f6",
                    weight: 5,
                    opacity: 0.7,
                    dashArray: "10, 10", // Make it dashed to indicate it's a direct route
                    lineCap: "round",
                  },
                ).addTo(map)

                // Add a note to the map - calculate center manually to avoid bounds error
                let centerLat = 0,
                  centerLng = 0
                currentWaypoints.forEach((wp: any) => {
                  centerLat += wp.latLng.lat
                  centerLng += wp.latLng.lng
                })
                centerLat /= currentWaypoints.length
                centerLng /= currentWaypoints.length
                const center = L.latLng(centerLat, centerLng)

                L.marker(center, {
                  icon: L.divIcon({
                    className: "route-note",
                    html: '<div style="background-color: rgba(255,255,255,0.8); color: #333; padding: 5px; border-radius: 4px; font-size: 12px;">Direct route (not following roads)</div>',
                    iconSize: [200, 30],
                    iconAnchor: [100, 15],
                  }),
                }).addTo(map)

                // Convert waypoints to format for encoding
                const points = currentWaypoints.map((wp: any) => [wp.latLng.lat, wp.latLng.lng])

                // Encode the polyline
                const encoded = PolylineUtil.encode(points)
                onPolylineChange(encoded)
              }
            } catch (error) {
              console.error("Error creating direct route:", error)
            }
          })
        }
      } catch (error) {
        console.error("Error initializing map:", error)
        if (mapRef.current) {
          mapRef.current.innerHTML =
            '<div style="padding: 20px; color: red;">Error initializing map. Please check console.</div>'
        }
      }
    }

    // Initialize map only once
    initMap()

    // Cleanup function when component unmounts
    return () => {
      // Set unmounted flag
      isMounted.current = false

      // Cleanup function to properly dispose of map resources
      const cleanupMap = () => {
        try {
          console.log("Cleaning up map resources...")

          // First remove routing control if it exists
          if (routingControlRef.current && mapInstanceRef.current) {
            console.log("Removing routing control")
            try {
              mapInstanceRef.current.removeControl(routingControlRef.current)
            } catch (e) {
              console.warn("Error removing routing control:", e)
            }
            routingControlRef.current = null
          }

          // Then remove the map if it exists
          if (mapInstanceRef.current) {
            console.log("Removing map instance")
            try {
              mapInstanceRef.current.remove()
            } catch (e) {
              console.warn("Error removing map:", e)
            }
            mapInstanceRef.current = null
          }

          // Clean up the DOM element
          if (mapRef.current) {
            mapRef.current.innerHTML = ""
            // Remove any Leaflet-specific attributes
            if (mapRef.current._leaflet_id) {
              delete mapRef.current._leaflet_id
            }
          }
        } catch (error) {
          console.error("Error during map cleanup:", error)
        }
      }

      // Execute cleanup immediately to prevent issues with fast re-renders
      cleanupMap()
    }
  }, [center, zoom, initialPolyline, onPolylineChange, readOnly, mapReady])

  // Add this function if it's not already defined in your component
  const createFallbackRouter = () => {
    // This is a simple fallback router that just creates straight lines between waypoints
    return {
      route: (waypoints: any, callback: Function) => {
        try {
          // Create a simple route with straight lines
          const coordinates: any[] = []

          waypoints.forEach((wp: any) => {
            if (wp && wp.latLng) {
              coordinates.push({
                lat: wp.latLng.lat,
                lng: wp.latLng.lng,
              })
            }
          })

          // Create a simple route object
          const route = {
            name: "Direct route",
            coordinates: coordinates,
            summary: {
              totalDistance: 0,
              totalTime: 0,
            },
            inputWaypoints: waypoints,
            waypoints: waypoints,
          }

          // Calculate simple distance
          for (let i = 1; i < coordinates.length; i++) {
            const p1 = coordinates[i - 1]
            const p2 = coordinates[i]

            // Simple Euclidean distance (not accurate for real-world distances)
            const dx = p2.lng - p1.lng
            const dy = p2.lat - p1.lat
            const distance = Math.sqrt(dx * dx + dy * dy) * 111000 // Rough conversion to meters

            route.summary.totalDistance += distance
            route.summary.totalTime += distance / 50 // Assume 50 m/s speed
          }

          callback(null, [route])
        } catch (error) {
          callback(error, null)
        }
      },
    }
  }

  // Reset the map container key when props change to force a complete re-render
  useEffect(() => {
    mapContainerKey.current = Math.random().toString(36).substring(2, 11)
    // Force cleanup of any existing map
    if (mapInstanceRef.current) {
      try {
        if (routingControlRef.current) {
          mapInstanceRef.current.removeControl(routingControlRef.current)
          routingControlRef.current = null
        }
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      } catch (e) {
        console.warn("Error cleaning up map on key change:", e)
      }
    }
    // Reset map ready state
    setMapReady(false)
    // Check container dimensions after a short delay
    setTimeout(() => {
      if (mapRef.current && mapRef.current.clientWidth > 0 && mapRef.current.clientHeight > 0) {
        setMapReady(true)
      }
    }, 100)
  }, [center, zoom, initialPolyline, readOnly])

  return (
    <div className="relative">
      <div
        key={mapContainerKey.current}
        ref={mapRef}
        className="h-[500px] w-full rounded-md border"
      />

      {/* Rate limiting status notification */}
      {routingStatus === 'rate-limited' && (
        <div className="absolute top-2 left-2 right-2 bg-yellow-100 border border-yellow-400 text-yellow-800 px-3 py-2 rounded-md text-sm z-[1001]">
          <div className="flex items-center">
            <span className="font-medium">⚠️ Rate Limited:</span>
            <span className="ml-2">Using direct routing due to API limits. Routes may not follow roads exactly.</span>
          </div>
        </div>
      )}

      {!readOnly && (
        <>
          <div className={`absolute ${routingStatus === 'rate-limited' ? 'top-16' : 'top-4'} left-0 right-0 mx-auto w-fit bg-white p-3 rounded-md shadow-md text-sm z-[1000]`}>
            <div className="flex items-center font-medium text-center">
              <MapIcon className="h-5 w-5 mr-2 text-blue-500" />
              <span>Click on the map to add waypoints. Drag waypoints to adjust the route.</span>
            </div>
          </div>
          <div className="absolute bottom-4 left-4 bg-white p-3 rounded-md shadow-md text-sm">
            <div className="font-medium mb-1 text-gray-700">How to create a route:</div>
            <div className="flex items-center">
              <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2">
                1
              </span>
              <span>Click to add waypoints</span>
            </div>
            <div className="flex items-center mt-1">
              <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2">
                2
              </span>
              <span>Drag markers to adjust</span>
            </div>
            <div className="flex items-center mt-1">
              <span className="bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2">
                3
              </span>
              <span>Use "Direct Route" button if routing fails</span>
            </div>
            {routingStatus === 'rate-limited' && (
              <div className="flex items-center mt-1">
                <span className="bg-yellow-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2">
                  ⚠
                </span>
                <span>Rate limited - using direct routes</span>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

