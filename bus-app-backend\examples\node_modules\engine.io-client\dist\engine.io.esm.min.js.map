{"version": 3, "file": "engine.io.esm.min.js", "sources": ["../../engine.io-parser/build/esm/commons.js", "../../engine.io-parser/build/esm/encodePacket.browser.js", "../../engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../../engine.io-parser/build/esm/decodePacket.browser.js", "../../engine.io-parser/build/esm/index.js", "../../socket.io-component-emitter/lib/esm/index.js", "../build/esm/globals.js", "../build/esm/util.js", "../build/esm/transport.js", "../build/esm/contrib/parseqs.js", "../build/esm/transports/polling.js", "../build/esm/contrib/has-cors.js", "../build/esm/transports/polling-xhr.js", "../build/esm/transports/websocket.js", "../build/esm/transports/webtransport.js", "../build/esm/transports/index.js", "../build/esm/contrib/parseuri.js", "../build/esm/socket.js", "../build/esm/transports/polling-fetch.js", "../build/esm/index.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n", "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n", "import { Polling } from \"./polling.js\";\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), <PERSON><PERSON>, <PERSON>un\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */\nexport class Fetch extends Polling {\n    doPoll() {\n        this._fetch()\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch read error\", res.status, res);\n            }\n            res.text().then((data) => this.onData(data));\n        })\n            .catch((err) => {\n            this.onError(\"fetch read error\", err);\n        });\n    }\n    doWrite(data, callback) {\n        this._fetch(data)\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch write error\", res.status, res);\n            }\n            callback();\n        })\n            .catch((err) => {\n            this.onError(\"fetch write error\", err);\n        });\n    }\n    _fetch(data) {\n        var _a;\n        const isPost = data !== undefined;\n        const headers = new Headers(this.opts.extraHeaders);\n        if (isPost) {\n            headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n        }\n        (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n        return fetch(this.uri(), {\n            method: isPost ? \"POST\" : \"GET\",\n            body: isPost ? data : null,\n            headers,\n            credentials: this.opts.withCredentials ? \"include\" : \"omit\",\n        }).then((res) => {\n            var _a;\n            // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n            (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n            return res;\n        });\n    }\n}\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade, } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "chars", "lookup", "i", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "length", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "createPacketEncoderStream", "TransformStream", "transform", "packet", "controller", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "encodePacketToBinary", "payloadLength", "header", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "nextTick", "Promise", "resolve", "setTimeoutFn", "globalThisShim", "self", "window", "Function", "pick", "attr", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "bind", "clearTimeoutFn", "randomString", "Date", "now", "Math", "random", "TransportError", "Error", "constructor", "reason", "description", "context", "super", "Transport", "writable", "query", "socket", "forceBase64", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "str", "encodeURIComponent", "Polling", "_polling", "name", "_poll", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "count", "join", "encodePayload", "doWrite", "uri", "timestampRequests", "timestampParam", "sid", "b64", "value", "XMLHttpRequest", "err", "hasCORS", "empty", "BaseXHR", "location", "isSSL", "protocol", "xd", "req", "request", "method", "xhrStatus", "pollXhr", "Request", "createRequest", "_opts", "_method", "_uri", "_data", "undefined", "_create", "_a", "xdomain", "xhr", "_xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "e", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "getResponseHeader", "status", "_onLoad", "_onError", "document", "_index", "requestsCount", "requests", "_cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "hasXHR2", "newRequest", "responseType", "XHR", "assign", "concat", "isReactNative", "navigator", "product", "toLowerCase", "BaseWS", "protocols", "headers", "ws", "createSocket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "WebSocketCtor", "WebSocket", "MozWebSocket", "WS", "_packet", "WT", "_transport", "WebTransport", "transportOptions", "closed", "catch", "ready", "createBidirectionalStream", "stream", "decoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "pow", "createPacketDecoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "_writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "polling", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "withEventListeners", "OFFLINE_EVENT_LISTENERS", "listener", "SocketWithoutUpgrade", "writeBuffer", "_prevBufferLen", "_pingInterval", "_pingTimeout", "_maxPayload", "_pingTimeoutTime", "Infinity", "parsed<PERSON><PERSON>", "_transportsByName", "t", "transportName", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "_beforeunloadEventListener", "transport", "_offlineEventListener", "_onClose", "_cookieJar", "createCookieJar", "_open", "createTransport", "EIO", "id", "priorWebsocketSuccess", "setTransport", "_onDrain", "_onPacket", "flush", "onHandshake", "JSON", "_sendPacket", "_resetPingTimeout", "code", "pingInterval", "pingTimeout", "_pingTimeoutTimer", "delay", "upgrading", "_getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "_hasPingExpired", "hasExpired", "msg", "options", "compress", "cleanupAndClose", "waitForUpgrade", "tryAllTransports", "SocketWithUpgrade", "_upgrades", "_probe", "failed", "onTransportOpen", "cleanup", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "_filterUpgrades", "upgrades", "filteredUpgrades", "Socket", "o", "map", "DEFAULT_TRANSPORTS", "filter", "<PERSON>tch", "_fetch", "res", "ok", "text", "isPost", "Headers", "set", "appendCookies", "fetch", "body", "credentials", "getSetCookie"], "mappings": ";;;;;AAAA,MAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,MAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAASC,IAC/BH,EAAqBH,EAAaM,IAAQA,CAAG,IAEjD,MAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAE/BC,EAAUC,GACyB,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,GAAOA,EAAIC,kBAAkBH,YAEjCI,EAAe,EAAGZ,OAAMC,QAAQY,EAAgBC,IAC9CZ,GAAkBD,aAAgBE,KAC9BU,EACOC,EAASb,GAGTc,EAAmBd,EAAMa,GAG/BP,IACJN,aAAgBO,aAAeC,EAAOR,IACnCY,EACOC,EAASb,GAGTc,EAAmB,IAAIZ,KAAK,CAACF,IAAQa,GAI7CA,EAAStB,EAAaQ,IAASC,GAAQ,KAE5Cc,EAAqB,CAACd,EAAMa,KAC9B,MAAME,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,MAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CP,EAAS,KAAOK,GAAW,IACnC,EACWH,EAAWM,cAAcrB,EAAK,EAEzC,SAASsB,EAAQtB,GACb,OAAIA,aAAgBuB,WACTvB,EAEFA,aAAgBO,YACd,IAAIgB,WAAWvB,GAGf,IAAIuB,WAAWvB,EAAKU,OAAQV,EAAKwB,WAAYxB,EAAKyB,WAEjE,CACA,IAAIC,EClDJ,MAAMC,EAAQ,mEAERC,EAA+B,oBAAfL,WAA6B,GAAK,IAAIA,WAAW,KACvE,IAAK,IAAIM,EAAI,EAAGA,EAAIF,GAAcE,IAC9BD,EAAOD,EAAMG,WAAWD,IAAMA,EAkB3B,MCrBDvB,EAA+C,mBAAhBC,YACxBwB,EAAe,CAACC,EAAeC,KACxC,GAA6B,iBAAlBD,EACP,MAAO,CACHjC,KAAM,UACNC,KAAMkC,EAAUF,EAAeC,IAGvC,MAAMlC,EAAOiC,EAAcG,OAAO,GAClC,GAAa,MAATpC,EACA,MAAO,CACHA,KAAM,UACNC,KAAMoC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAI7D,OADmBvC,EAAqBK,GAIjCiC,EAAcM,OAAS,EACxB,CACEvC,KAAML,EAAqBK,GAC3BC,KAAMgC,EAAcK,UAAU,IAEhC,CACEtC,KAAML,EAAqBK,IARxBD,CASN,EAEHsC,EAAqB,CAACpC,EAAMiC,KAC9B,GAAI3B,EAAuB,CACvB,MAAMiC,EDTQ,CAACC,IACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOF,OAAeQ,EAAMN,EAAOF,OAAWS,EAAI,EACnC,MAA9BP,EAAOA,EAAOF,OAAS,KACvBO,IACkC,MAA9BL,EAAOA,EAAOF,OAAS,IACvBO,KAGR,MAAMG,EAAc,IAAIzC,YAAYsC,GAAeI,EAAQ,IAAI1B,WAAWyB,GAC1E,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWb,EAAOY,EAAOV,WAAWD,IACpCa,EAAWd,EAAOY,EAAOV,WAAWD,EAAI,IACxCc,EAAWf,EAAOY,EAAOV,WAAWD,EAAI,IACxCe,EAAWhB,EAAOY,EAAOV,WAAWD,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CAAW,ECTEE,CAAOlD,GACvB,OAAOkC,EAAUK,EAASN,EAC7B,CAEG,MAAO,CAAEO,QAAQ,EAAMxC,OAC1B,EAECkC,EAAY,CAAClC,EAAMiC,IAEZ,SADDA,EAEIjC,aAAgBE,KAETF,EAIA,IAAIE,KAAK,CAACF,IAIjBA,aAAgBO,YAETP,EAIAA,EAAKU,OCvDtByC,EAAYC,OAAOC,aAAa,IA4B/B,SAASC,IACZ,OAAO,IAAIC,gBAAgB,CACvB,SAAAC,CAAUC,EAAQC,IHmBnB,SAA8BD,EAAQ5C,GACrCZ,GAAkBwD,EAAOzD,gBAAgBE,KAClCuD,EAAOzD,KAAK2D,cAAcC,KAAKtC,GAASsC,KAAK/C,GAE/CP,IACJmD,EAAOzD,gBAAgBO,aAAeC,EAAOiD,EAAOzD,OAC9Ca,EAASS,EAAQmC,EAAOzD,OAEnCW,EAAa8C,GAAQ,GAAQI,IACpBnC,IACDA,EAAe,IAAIoC,aAEvBjD,EAASa,EAAaqC,OAAOF,GAAS,GAE9C,CGhCYG,CAAqBP,GAASzB,IAC1B,MAAMiC,EAAgBjC,EAAcM,OACpC,IAAI4B,EAEJ,GAAID,EAAgB,IAChBC,EAAS,IAAI3C,WAAW,GACxB,IAAI4C,SAASD,EAAOxD,QAAQ0D,SAAS,EAAGH,QAEvC,GAAIA,EAAgB,MAAO,CAC5BC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOxD,QACjC2D,EAAKD,SAAS,EAAG,KACjBC,EAAKC,UAAU,EAAGL,EACrB,KACI,CACDC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOxD,QACjC2D,EAAKD,SAAS,EAAG,KACjBC,EAAKE,aAAa,EAAGC,OAAOP,GAC/B,CAEGR,EAAOzD,MAA+B,iBAAhByD,EAAOzD,OAC7BkE,EAAO,IAAM,KAEjBR,EAAWe,QAAQP,GACnBR,EAAWe,QAAQzC,EAAc,GAExC,GAET,CACA,IAAI0C,EACJ,SAASC,EAAYC,GACjB,OAAOA,EAAOC,QAAO,CAACC,EAAKC,IAAUD,EAAMC,EAAMzC,QAAQ,EAC7D,CACA,SAAS0C,EAAaJ,EAAQK,GAC1B,GAAIL,EAAO,GAAGtC,SAAW2C,EACrB,OAAOL,EAAOM,QAElB,MAAMxE,EAAS,IAAIa,WAAW0D,GAC9B,IAAIE,EAAI,EACR,IAAK,IAAItD,EAAI,EAAGA,EAAIoD,EAAMpD,IACtBnB,EAAOmB,GAAK+C,EAAO,GAAGO,KAClBA,IAAMP,EAAO,GAAGtC,SAChBsC,EAAOM,QACPC,EAAI,GAMZ,OAHIP,EAAOtC,QAAU6C,EAAIP,EAAO,GAAGtC,SAC/BsC,EAAO,GAAKA,EAAO,GAAGQ,MAAMD,IAEzBzE,CACX,CC/EO,SAAS2E,EAAQ5E,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIZ,KAAOwF,EAAQlF,UACtBM,EAAIZ,GAAOwF,EAAQlF,UAAUN,GAE/B,OAAOY,CACT,CAhBkB6E,CAAM7E,EACxB,CA0BA4E,EAAQlF,UAAUoF,GAClBF,EAAQlF,UAAUqF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,GACpCD,KAAKC,EAAW,IAAMH,GAASE,KAAKC,EAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQlF,UAAU2F,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,UAChB,CAID,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQlF,UAAU4F,IAClBV,EAAQlF,UAAU+F,eAClBb,EAAQlF,UAAUgG,mBAClBd,EAAQlF,UAAUiG,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAGjC,GAAKK,UAAU3D,OAEjB,OADAqD,KAAKC,EAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,EAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAU3D,OAEjB,cADOqD,KAAKC,EAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAIyE,EAAUhE,OAAQT,IAEpC,IADAwE,EAAKC,EAAUzE,MACJ6D,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO1E,EAAG,GACpB,KACD,CASH,OAJyB,IAArByE,EAAUhE,eACLqD,KAAKC,EAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQlF,UAAUqG,KAAO,SAASf,GAChCE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAU3D,OAAS,GACpCgE,EAAYX,KAAKC,EAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIoE,UAAU3D,OAAQT,IACpC4E,EAAK5E,EAAI,GAAKoE,UAAUpE,GAG1B,GAAIyE,EAEG,CAAIzE,EAAI,EAAb,IAAK,IAAWiB,GADhBwD,EAAYA,EAAUlB,MAAM,IACI9C,OAAQT,EAAIiB,IAAOjB,EACjDyE,EAAUzE,GAAGmE,MAAML,KAAMc,EADKnE,CAKlC,OAAOqD,IACT,EAGAN,EAAQlF,UAAUwG,aAAetB,EAAQlF,UAAUqG,KAUnDnB,EAAQlF,UAAUyG,UAAY,SAASnB,GAErC,OADAE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAC9BD,KAAKC,EAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQlF,UAAU0G,aAAe,SAASpB,GACxC,QAAUE,KAAKiB,UAAUnB,GAAOnD,MAClC,ECxKY,MAACwE,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAE/DX,GAAOU,QAAQC,UAAUpD,KAAKyC,GAG/B,CAACA,EAAIY,IAAiBA,EAAaZ,EAAI,GAGzCa,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GChBR,SAASC,EAAK7G,KAAQ8G,GACzB,OAAOA,EAAK1C,QAAO,CAACC,EAAK0C,KACjB/G,EAAIgH,eAAeD,KACnB1C,EAAI0C,GAAK/G,EAAI+G,IAEV1C,IACR,CAAE,EACT,CAEA,MAAM4C,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBtH,EAAKuH,GACnCA,EAAKC,iBACLxH,EAAIwG,aAAeS,EAAmBQ,KAAKP,GAC3ClH,EAAI0H,eAAiBN,EAAqBK,KAAKP,KAG/ClH,EAAIwG,aAAeU,EAAWC,WAAWM,KAAKP,GAC9ClH,EAAI0H,eAAiBR,EAAWG,aAAaI,KAAKP,GAE1D,CAkCO,SAASS,IACZ,OAAQC,KAAKC,MAAMlI,SAAS,IAAIiC,UAAU,GACtCkG,KAAKC,SAASpI,SAAS,IAAIiC,UAAU,EAAG,EAChD,CCtDO,MAAMoG,UAAuBC,MAChC,WAAAC,CAAYC,EAAQC,EAAaC,GAC7BC,MAAMH,GACNjD,KAAKkD,YAAcA,EACnBlD,KAAKmD,QAAUA,EACfnD,KAAK5F,KAAO,gBACf,EAEE,MAAMiJ,UAAkB3D,EAO3B,WAAAsD,CAAYX,GACRe,QACApD,KAAKsD,UAAW,EAChBlB,EAAsBpC,KAAMqC,GAC5BrC,KAAKqC,KAAOA,EACZrC,KAAKuD,MAAQlB,EAAKkB,MAClBvD,KAAKwD,OAASnB,EAAKmB,OACnBxD,KAAK/E,gBAAkBoH,EAAKoB,WAC/B,CAUD,OAAAC,CAAQT,EAAQC,EAAaC,GAEzB,OADAC,MAAMpC,aAAa,QAAS,IAAI8B,EAAeG,EAAQC,EAAaC,IAC7DnD,IACV,CAID,IAAA2D,GAGI,OAFA3D,KAAK4D,WAAa,UAClB5D,KAAK6D,SACE7D,IACV,CAID,KAAA8D,GAKI,MAJwB,YAApB9D,KAAK4D,YAAgD,SAApB5D,KAAK4D,aACtC5D,KAAK+D,UACL/D,KAAKgE,WAEFhE,IACV,CAMD,IAAAiE,CAAKC,GACuB,SAApBlE,KAAK4D,YACL5D,KAAKmE,MAAMD,EAKlB,CAMD,MAAAE,GACIpE,KAAK4D,WAAa,OAClB5D,KAAKsD,UAAW,EAChBF,MAAMpC,aAAa,OACtB,CAOD,MAAAqD,CAAOhK,GACH,MAAMyD,EAAS1B,EAAa/B,EAAM2F,KAAKwD,OAAOlH,YAC9C0D,KAAKsE,SAASxG,EACjB,CAMD,QAAAwG,CAASxG,GACLsF,MAAMpC,aAAa,SAAUlD,EAChC,CAMD,OAAAkG,CAAQO,GACJvE,KAAK4D,WAAa,SAClBR,MAAMpC,aAAa,QAASuD,EAC/B,CAMD,KAAAC,CAAMC,GAAY,CAClB,SAAAC,CAAUC,EAAQpB,EAAQ,IACtB,OAAQoB,EACJ,MACA3E,KAAK4E,IACL5E,KAAK6E,IACL7E,KAAKqC,KAAKyC,KACV9E,KAAK+E,EAAOxB,EACnB,CACD,CAAAqB,GACI,MAAMI,EAAWhF,KAAKqC,KAAK2C,SAC3B,OAAkC,IAA3BA,EAASC,QAAQ,KAAcD,EAAW,IAAMA,EAAW,GACrE,CACD,CAAAH,GACI,OAAI7E,KAAKqC,KAAK6C,OACRlF,KAAKqC,KAAK8C,QAAUC,OAA0B,MAAnBpF,KAAKqC,KAAK6C,QACjClF,KAAKqC,KAAK8C,QAAqC,KAA3BC,OAAOpF,KAAKqC,KAAK6C,OACpC,IAAMlF,KAAKqC,KAAK6C,KAGhB,EAEd,CACD,CAAAH,CAAOxB,GACH,MAAM8B,EClIP,SAAgBvK,GACnB,IAAIwK,EAAM,GACV,IAAK,IAAIpJ,KAAKpB,EACNA,EAAIgH,eAAe5F,KACfoJ,EAAI3I,SACJ2I,GAAO,KACXA,GAAOC,mBAAmBrJ,GAAK,IAAMqJ,mBAAmBzK,EAAIoB,KAGpE,OAAOoJ,CACX,CDwH6BlH,CAAOmF,GAC5B,OAAO8B,EAAa1I,OAAS,IAAM0I,EAAe,EACrD,EEzIE,MAAMG,UAAgBnC,EACzB,WAAAL,GACII,SAAS9C,WACTN,KAAKyF,GAAW,CACnB,CACD,QAAIC,GACA,MAAO,SACV,CAOD,MAAA7B,GACI7D,KAAK2F,GACR,CAOD,KAAAnB,CAAMC,GACFzE,KAAK4D,WAAa,UAClB,MAAMY,EAAQ,KACVxE,KAAK4D,WAAa,SAClBa,GAAS,EAEb,GAAIzE,KAAKyF,IAAazF,KAAKsD,SAAU,CACjC,IAAIsC,EAAQ,EACR5F,KAAKyF,IACLG,IACA5F,KAAKG,KAAK,gBAAgB,aACpByF,GAASpB,GAC/B,KAEiBxE,KAAKsD,WACNsC,IACA5F,KAAKG,KAAK,SAAS,aACbyF,GAASpB,GAC/B,IAES,MAEGA,GAEP,CAMD,CAAAmB,GACI3F,KAAKyF,GAAW,EAChBzF,KAAK6F,SACL7F,KAAKgB,aAAa,OACrB,CAMD,MAAAqD,CAAOhK,GN/CW,EAACyL,EAAgBxJ,KACnC,MAAMyJ,EAAiBD,EAAerK,MAAM+B,GACtC0G,EAAU,GAChB,IAAK,IAAIhI,EAAI,EAAGA,EAAI6J,EAAepJ,OAAQT,IAAK,CAC5C,MAAM8J,EAAgB5J,EAAa2J,EAAe7J,GAAII,GAEtD,GADA4H,EAAQhE,KAAK8F,GACc,UAAvBA,EAAc5L,KACd,KAEP,CACD,OAAO8J,CAAO,EMoDV+B,CAAc5L,EAAM2F,KAAKwD,OAAOlH,YAAYrC,SAd1B6D,IAMd,GAJI,YAAckC,KAAK4D,YAA8B,SAAhB9F,EAAO1D,MACxC4F,KAAKoE,SAGL,UAAYtG,EAAO1D,KAEnB,OADA4F,KAAKgE,QAAQ,CAAEd,YAAa,oCACrB,EAGXlD,KAAKsE,SAASxG,EAAO,IAKrB,WAAakC,KAAK4D,aAElB5D,KAAKyF,GAAW,EAChBzF,KAAKgB,aAAa,gBACd,SAAWhB,KAAK4D,YAChB5D,KAAK2F,IAKhB,CAMD,OAAA5B,GACI,MAAMD,EAAQ,KACV9D,KAAKmE,MAAM,CAAC,CAAE/J,KAAM,UAAW,EAE/B,SAAW4F,KAAK4D,WAChBE,IAKA9D,KAAKG,KAAK,OAAQ2D,EAEzB,CAOD,KAAAK,CAAMD,GACFlE,KAAKsD,UAAW,ENnHF,EAACY,EAAShJ,KAE5B,MAAMyB,EAASuH,EAAQvH,OACjBoJ,EAAiB,IAAIhF,MAAMpE,GACjC,IAAIuJ,EAAQ,EACZhC,EAAQjK,SAAQ,CAAC6D,EAAQ5B,KAErBlB,EAAa8C,GAAQ,GAAQzB,IACzB0J,EAAe7J,GAAKG,IACd6J,IAAUvJ,GACZzB,EAAS6K,EAAeI,KAAK3I,GAChC,GACH,GACJ,EMuGE4I,CAAclC,GAAU7J,IACpB2F,KAAKqG,QAAQhM,GAAM,KACf2F,KAAKsD,UAAW,EAChBtD,KAAKgB,aAAa,QAAQ,GAC5B,GAET,CAMD,GAAAsF,GACI,MAAM3B,EAAS3E,KAAKqC,KAAK8C,OAAS,QAAU,OACtC5B,EAAQvD,KAAKuD,OAAS,GAQ5B,OANI,IAAUvD,KAAKqC,KAAKkE,oBACpBhD,EAAMvD,KAAKqC,KAAKmE,gBAAkB/D,KAEjCzC,KAAK/E,gBAAmBsI,EAAMkD,MAC/BlD,EAAMmD,IAAM,GAET1G,KAAK0E,UAAUC,EAAQpB,EACjC,EC9IL,IAAIoD,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,cACjC,CACA,MAAOC,GAGP,CACO,MAAMC,EAAUH,ECLvB,SAASI,IAAW,CACb,MAAMC,UAAgBxB,EAOzB,WAAAxC,CAAYX,GAER,GADAe,MAAMf,GACkB,oBAAb4E,SAA0B,CACjC,MAAMC,EAAQ,WAAaD,SAASE,SACpC,IAAIjC,EAAO+B,SAAS/B,KAEfA,IACDA,EAAOgC,EAAQ,MAAQ,MAE3BlH,KAAKoH,GACoB,oBAAbH,UACJ5E,EAAK2C,WAAaiC,SAASjC,UAC3BE,IAAS7C,EAAK6C,IACzB,CACJ,CAQD,OAAAmB,CAAQhM,EAAM0F,GACV,MAAMsH,EAAMrH,KAAKsH,QAAQ,CACrBC,OAAQ,OACRlN,KAAMA,IAEVgN,EAAIzH,GAAG,UAAWG,GAClBsH,EAAIzH,GAAG,SAAS,CAAC4H,EAAWrE,KACxBnD,KAAK0D,QAAQ,iBAAkB8D,EAAWrE,EAAQ,GAEzD,CAMD,MAAA0C,GACI,MAAMwB,EAAMrH,KAAKsH,UACjBD,EAAIzH,GAAG,OAAQI,KAAKqE,OAAO9B,KAAKvC,OAChCqH,EAAIzH,GAAG,SAAS,CAAC4H,EAAWrE,KACxBnD,KAAK0D,QAAQ,iBAAkB8D,EAAWrE,EAAQ,IAEtDnD,KAAKyH,QAAUJ,CAClB,EAEE,MAAMK,UAAgBhI,EAOzB,WAAAsD,CAAY2E,EAAerB,EAAKjE,GAC5Be,QACApD,KAAK2H,cAAgBA,EACrBvF,EAAsBpC,KAAMqC,GAC5BrC,KAAK4H,EAAQvF,EACbrC,KAAK6H,EAAUxF,EAAKkF,QAAU,MAC9BvH,KAAK8H,EAAOxB,EACZtG,KAAK+H,OAAQC,IAAc3F,EAAKhI,KAAOgI,EAAKhI,KAAO,KACnD2F,KAAKiI,GACR,CAMD,CAAAA,GACI,IAAIC,EACJ,MAAM7F,EAAOV,EAAK3B,KAAK4H,EAAO,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aAClHvF,EAAK8F,UAAYnI,KAAK4H,EAAMR,GAC5B,MAAMgB,EAAOpI,KAAKqI,EAAOrI,KAAK2H,cAActF,GAC5C,IACI+F,EAAIzE,KAAK3D,KAAK6H,EAAS7H,KAAK8H,GAAM,GAClC,IACI,GAAI9H,KAAK4H,EAAMU,aAAc,CAEzBF,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACvD,IAAK,IAAIrM,KAAK8D,KAAK4H,EAAMU,aACjBtI,KAAK4H,EAAMU,aAAaxG,eAAe5F,IACvCkM,EAAII,iBAAiBtM,EAAG8D,KAAK4H,EAAMU,aAAapM,GAG3D,CACJ,CACD,MAAOuM,GAAM,CACb,GAAI,SAAWzI,KAAK6H,EAChB,IACIO,EAAII,iBAAiB,eAAgB,2BACxC,CACD,MAAOC,GAAM,CAEjB,IACIL,EAAII,iBAAiB,SAAU,MAClC,CACD,MAAOC,GAAM,CACmB,QAA/BP,EAAKlI,KAAK4H,EAAMc,iBAA8B,IAAPR,GAAyBA,EAAGS,WAAWP,GAE3E,oBAAqBA,IACrBA,EAAIQ,gBAAkB5I,KAAK4H,EAAMgB,iBAEjC5I,KAAK4H,EAAMiB,iBACXT,EAAIU,QAAU9I,KAAK4H,EAAMiB,gBAE7BT,EAAIW,mBAAqB,KACrB,IAAIb,EACmB,IAAnBE,EAAIxE,aAC4B,QAA/BsE,EAAKlI,KAAK4H,EAAMc,iBAA8B,IAAPR,GAAyBA,EAAGc,aAEpEZ,EAAIa,kBAAkB,gBAEtB,IAAMb,EAAIxE,aAEV,MAAQwE,EAAIc,QAAU,OAASd,EAAIc,OACnClJ,KAAKmJ,IAKLnJ,KAAKsB,cAAa,KACdtB,KAAKoJ,EAA+B,iBAAfhB,EAAIc,OAAsBd,EAAIc,OAAS,EAAE,GAC/D,GACN,EAELd,EAAInE,KAAKjE,KAAK+H,EACjB,CACD,MAAOU,GAOH,YAHAzI,KAAKsB,cAAa,KACdtB,KAAKoJ,EAASX,EAAE,GACjB,EAEN,CACuB,oBAAbY,WACPrJ,KAAKsJ,EAAS5B,EAAQ6B,gBACtB7B,EAAQ8B,SAASxJ,KAAKsJ,GAAUtJ,KAEvC,CAMD,CAAAoJ,CAASvC,GACL7G,KAAKgB,aAAa,QAAS6F,EAAK7G,KAAKqI,GACrCrI,KAAKyJ,GAAS,EACjB,CAMD,CAAAA,CAASC,GACL,QAAI,IAAuB1J,KAAKqI,GAAQ,OAASrI,KAAKqI,EAAtD,CAIA,GADArI,KAAKqI,EAAKU,mBAAqBhC,EAC3B2C,EACA,IACI1J,KAAKqI,EAAKsB,OACb,CACD,MAAOlB,GAAM,CAEO,oBAAbY,iBACA3B,EAAQ8B,SAASxJ,KAAKsJ,GAEjCtJ,KAAKqI,EAAO,IAXX,CAYJ,CAMD,CAAAc,GACI,MAAM9O,EAAO2F,KAAKqI,EAAKuB,aACV,OAATvP,IACA2F,KAAKgB,aAAa,OAAQ3G,GAC1B2F,KAAKgB,aAAa,WAClBhB,KAAKyJ,IAEZ,CAMD,KAAAE,GACI3J,KAAKyJ,GACR,EASL,GAPA/B,EAAQ6B,cAAgB,EACxB7B,EAAQ8B,SAAW,CAAA,EAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArBjK,iBAAiC,CAE7CA,iBADyB,eAAgBmC,EAAa,WAAa,SAChC8H,GAAe,EACrD,CAEL,SAASA,IACL,IAAK,IAAI5N,KAAKwL,EAAQ8B,SACd9B,EAAQ8B,SAAS1H,eAAe5F,IAChCwL,EAAQ8B,SAAStN,GAAGyN,OAGhC,CACA,MAAMI,EAAU,WACZ,MAAM3B,EAAM4B,EAAW,CACnB7B,SAAS,IAEb,OAAOC,GAA4B,OAArBA,EAAI6B,YACrB,CALe,GAaT,MAAMC,UAAYlD,EACrB,WAAAhE,CAAYX,GACRe,MAAMf,GACN,MAAMoB,EAAcpB,GAAQA,EAAKoB,YACjCzD,KAAK/E,eAAiB8O,IAAYtG,CACrC,CACD,OAAA6D,CAAQjF,EAAO,IAEX,OADAxI,OAAOsQ,OAAO9H,EAAM,CAAE+E,GAAIpH,KAAKoH,IAAMpH,KAAKqC,MACnC,IAAIqF,EAAQsC,EAAYhK,KAAKsG,MAAOjE,EAC9C,EAEL,SAAS2H,EAAW3H,GAChB,MAAM8F,EAAU9F,EAAK8F,QAErB,IACI,GAAI,oBAAuBvB,kBAAoBuB,GAAWrB,GACtD,OAAO,IAAIF,cAElB,CACD,MAAO6B,GAAM,CACb,IAAKN,EACD,IACI,OAAO,IAAInG,EAAW,CAAC,UAAUoI,OAAO,UAAUjE,KAAK,OAAM,oBAChE,CACD,MAAOsC,GAAM,CAErB,CCzQA,MAAM4B,EAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACf,MAAMC,UAAepH,EACxB,QAAIqC,GACA,MAAO,WACV,CACD,MAAA7B,GACI,MAAMyC,EAAMtG,KAAKsG,MACXoE,EAAY1K,KAAKqC,KAAKqI,UAEtBrI,EAAOgI,EACP,CAAE,EACF1I,EAAK3B,KAAKqC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMrC,KAAKqC,KAAKiG,eACVjG,EAAKsI,QAAU3K,KAAKqC,KAAKiG,cAE7B,IACItI,KAAK4K,GAAK5K,KAAK6K,aAAavE,EAAKoE,EAAWrI,EAC/C,CACD,MAAOwE,GACH,OAAO7G,KAAKgB,aAAa,QAAS6F,EACrC,CACD7G,KAAK4K,GAAGtO,WAAa0D,KAAKwD,OAAOlH,WACjC0D,KAAK8K,mBACR,CAMD,iBAAAA,GACI9K,KAAK4K,GAAGG,OAAS,KACT/K,KAAKqC,KAAK2I,WACVhL,KAAK4K,GAAGK,EAAQC,QAEpBlL,KAAKoE,QAAQ,EAEjBpE,KAAK4K,GAAGO,QAAWC,GAAepL,KAAKgE,QAAQ,CAC3Cd,YAAa,8BACbC,QAASiI,IAEbpL,KAAK4K,GAAGS,UAAaC,GAAOtL,KAAKqE,OAAOiH,EAAGjR,MAC3C2F,KAAK4K,GAAGW,QAAW9C,GAAMzI,KAAK0D,QAAQ,kBAAmB+E,EAC5D,CACD,KAAAtE,CAAMD,GACFlE,KAAKsD,UAAW,EAGhB,IAAK,IAAIpH,EAAI,EAAGA,EAAIgI,EAAQvH,OAAQT,IAAK,CACrC,MAAM4B,EAASoG,EAAQhI,GACjBsP,EAAatP,IAAMgI,EAAQvH,OAAS,EAC1C3B,EAAa8C,EAAQkC,KAAK/E,gBAAiBZ,IAIvC,IACI2F,KAAKqG,QAAQvI,EAAQzD,EACxB,CACD,MAAOoO,GACN,CACG+C,GAGArK,GAAS,KACLnB,KAAKsD,UAAW,EAChBtD,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKsB,aACX,GAER,CACJ,CACD,OAAAyC,QAC2B,IAAZ/D,KAAK4K,KACZ5K,KAAK4K,GAAGW,QAAU,OAClBvL,KAAK4K,GAAG9G,QACR9D,KAAK4K,GAAK,KAEjB,CAMD,GAAAtE,GACI,MAAM3B,EAAS3E,KAAKqC,KAAK8C,OAAS,MAAQ,KACpC5B,EAAQvD,KAAKuD,OAAS,GAS5B,OAPIvD,KAAKqC,KAAKkE,oBACVhD,EAAMvD,KAAKqC,KAAKmE,gBAAkB/D,KAGjCzC,KAAK/E,iBACNsI,EAAMmD,IAAM,GAET1G,KAAK0E,UAAUC,EAAQpB,EACjC,EAEL,MAAMkI,EAAgBzJ,EAAW0J,WAAa1J,EAAW2J,aAUlD,MAAMC,UAAWnB,EACpB,YAAAI,CAAavE,EAAKoE,EAAWrI,GACzB,OAAQgI,EAIF,IAAIoB,EAAcnF,EAAKoE,EAAWrI,GAHlCqI,EACI,IAAIe,EAAcnF,EAAKoE,GACvB,IAAIe,EAAcnF,EAE/B,CACD,OAAAD,CAAQwF,EAASxR,GACb2F,KAAK4K,GAAG3G,KAAK5J,EAChB,EChHE,MAAMyR,UAAWzI,EACpB,QAAIqC,GACA,MAAO,cACV,CACD,MAAA7B,GACI,IAEI7D,KAAK+L,EAAa,IAAIC,aAAahM,KAAK0E,UAAU,SAAU1E,KAAKqC,KAAK4J,iBAAiBjM,KAAK0F,MAC/F,CACD,MAAOmB,GACH,OAAO7G,KAAKgB,aAAa,QAAS6F,EACrC,CACD7G,KAAK+L,EAAWG,OACXjO,MAAK,KACN+B,KAAKgE,SAAS,IAEbmI,OAAOtF,IACR7G,KAAK0D,QAAQ,qBAAsBmD,EAAI,IAG3C7G,KAAK+L,EAAWK,MAAMnO,MAAK,KACvB+B,KAAK+L,EAAWM,4BAA4BpO,MAAMqO,IAC9C,MAAMC,EVqDf,SAAmCC,EAAYlQ,GAC7CyC,IACDA,EAAe,IAAI0N,aAEvB,MAAMxN,EAAS,GACf,IAAIyN,EAAQ,EACRC,GAAkB,EAClBC,GAAW,EACf,OAAO,IAAIhP,gBAAgB,CACvB,SAAAC,CAAUuB,EAAOrB,GAEb,IADAkB,EAAOiB,KAAKd,KACC,CACT,GAAc,IAAVsN,EAAqC,CACrC,GAAI1N,EAAYC,GAAU,EACtB,MAEJ,MAAMV,EAASc,EAAaJ,EAAQ,GACpC2N,IAAkC,KAAtBrO,EAAO,IACnBoO,EAA6B,IAAZpO,EAAO,GAEpBmO,EADAC,EAAiB,IACT,EAEgB,MAAnBA,EACG,EAGA,CAEf,MACI,GAAc,IAAVD,EAAiD,CACtD,GAAI1N,EAAYC,GAAU,EACtB,MAEJ,MAAM4N,EAAcxN,EAAaJ,EAAQ,GACzC0N,EAAiB,IAAInO,SAASqO,EAAY9R,OAAQ8R,EAAYhR,WAAYgR,EAAYlQ,QAAQmQ,UAAU,GACxGJ,EAAQ,CACX,MACI,GAAc,IAAVA,EAAiD,CACtD,GAAI1N,EAAYC,GAAU,EACtB,MAEJ,MAAM4N,EAAcxN,EAAaJ,EAAQ,GACnCP,EAAO,IAAIF,SAASqO,EAAY9R,OAAQ8R,EAAYhR,WAAYgR,EAAYlQ,QAC5EoQ,EAAIrO,EAAKsO,UAAU,GACzB,GAAID,EAAInK,KAAKqK,IAAI,EAAG,IAAW,EAAG,CAE9BlP,EAAWe,QAAQ3E,GACnB,KACH,CACDwS,EAAiBI,EAAInK,KAAKqK,IAAI,EAAG,IAAMvO,EAAKsO,UAAU,GACtDN,EAAQ,CACX,KACI,CACD,GAAI1N,EAAYC,GAAU0N,EACtB,MAEJ,MAAMtS,EAAOgF,EAAaJ,EAAQ0N,GAClC5O,EAAWe,QAAQ1C,EAAawQ,EAAWvS,EAAO0E,EAAaxB,OAAOlD,GAAOiC,IAC7EoQ,EAAQ,CACX,CACD,GAAuB,IAAnBC,GAAwBA,EAAiBH,EAAY,CACrDzO,EAAWe,QAAQ3E,GACnB,KACH,CACJ,CACJ,GAET,CUxHsC+S,CAA0B9H,OAAO+H,iBAAkBnN,KAAKwD,OAAOlH,YAC/E8Q,EAASd,EAAOe,SAASC,YAAYf,GAAegB,YACpDC,EAAgB7P,IACtB6P,EAAcH,SAASI,OAAOnB,EAAOhJ,UACrCtD,KAAK0N,EAAUF,EAAclK,SAASqK,YACtC,MAAMC,EAAO,KACTR,EACKQ,OACA3P,MAAK,EAAG4P,OAAMlH,YACXkH,IAGJ7N,KAAKsE,SAASqC,GACdiH,IAAM,IAELzB,OAAOtF,IAAD,GACT,EAEN+G,IACA,MAAM9P,EAAS,CAAE1D,KAAM,QACnB4F,KAAKuD,MAAMkD,MACX3I,EAAOzD,KAAO,WAAW2F,KAAKuD,MAAMkD,SAExCzG,KAAK0N,EAAQvJ,MAAMrG,GAAQG,MAAK,IAAM+B,KAAKoE,UAAS,GACtD,GAET,CACD,KAAAD,CAAMD,GACFlE,KAAKsD,UAAW,EAChB,IAAK,IAAIpH,EAAI,EAAGA,EAAIgI,EAAQvH,OAAQT,IAAK,CACrC,MAAM4B,EAASoG,EAAQhI,GACjBsP,EAAatP,IAAMgI,EAAQvH,OAAS,EAC1CqD,KAAK0N,EAAQvJ,MAAMrG,GAAQG,MAAK,KACxBuN,GACArK,GAAS,KACLnB,KAAKsD,UAAW,EAChBtD,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKsB,aACX,GAER,CACJ,CACD,OAAAyC,GACI,IAAImE,EACuB,QAA1BA,EAAKlI,KAAK+L,SAA+B,IAAP7D,GAAyBA,EAAGpE,OAClE,EC3EO,MAACgK,EAAa,CACtBC,UAAWnC,EACXoC,aAAclC,EACdmC,QAAS/D,GCaPgE,EAAK,sPACLC,EAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,EAAM9I,GAClB,GAAIA,EAAI3I,OAAS,IACb,KAAM,eAEV,MAAM0R,EAAM/I,EAAKgJ,EAAIhJ,EAAIL,QAAQ,KAAMwD,EAAInD,EAAIL,QAAQ,MAC7C,GAANqJ,IAAiB,GAAN7F,IACXnD,EAAMA,EAAI5I,UAAU,EAAG4R,GAAKhJ,EAAI5I,UAAU4R,EAAG7F,GAAG8F,QAAQ,KAAM,KAAOjJ,EAAI5I,UAAU+L,EAAGnD,EAAI3I,SAE9F,IAAI6R,EAAIN,EAAGO,KAAKnJ,GAAO,IAAKgB,EAAM,CAAA,EAAIpK,EAAI,GAC1C,KAAOA,KACHoK,EAAI6H,EAAMjS,IAAMsS,EAAEtS,IAAM,GAU5B,OARU,GAANoS,IAAiB,GAAN7F,IACXnC,EAAIoI,OAASL,EACb/H,EAAIqI,KAAOrI,EAAIqI,KAAKjS,UAAU,EAAG4J,EAAIqI,KAAKhS,OAAS,GAAG4R,QAAQ,KAAM,KACpEjI,EAAIsI,UAAYtI,EAAIsI,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EjI,EAAIuI,SAAU,GAElBvI,EAAIwI,UAIR,SAAmBhU,EAAKgK,GACpB,MAAMiK,EAAO,WAAYC,EAAQlK,EAAKyJ,QAAQQ,EAAM,KAAKtT,MAAM,KACvC,KAApBqJ,EAAKrF,MAAM,EAAG,IAA6B,IAAhBqF,EAAKnI,QAChCqS,EAAMpO,OAAO,EAAG,GAEE,KAAlBkE,EAAKrF,OAAO,IACZuP,EAAMpO,OAAOoO,EAAMrS,OAAS,EAAG,GAEnC,OAAOqS,CACX,CAboBF,CAAUxI,EAAKA,EAAU,MACzCA,EAAI2I,SAaR,SAAkB3I,EAAK/C,GACnB,MAAMlJ,EAAO,CAAA,EAMb,OALAkJ,EAAMgL,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACA9U,EAAK8U,GAAMC,EAEvB,IACW/U,CACX,CArBmB4U,CAAS3I,EAAKA,EAAW,OACjCA,CACX,CCrCA,MAAM+I,EAAiD,mBAArBxP,kBACC,mBAAxBY,oBACL6O,EAA0B,GAC5BD,GAGAxP,iBAAiB,WAAW,KACxByP,EAAwBrV,SAASsV,GAAaA,KAAW,IAC1D,GAyBA,MAAMC,UAA6B9P,EAOtC,WAAAsD,CAAYsD,EAAKjE,GAiBb,GAhBAe,QACApD,KAAK1D,WX7BoB,cW8BzB0D,KAAKyP,YAAc,GACnBzP,KAAK0P,EAAiB,EACtB1P,KAAK2P,GAAiB,EACtB3P,KAAK4P,GAAgB,EACrB5P,KAAK6P,GAAe,EAKpB7P,KAAK8P,EAAmBC,IACpBzJ,GAAO,iBAAoBA,IAC3BjE,EAAOiE,EACPA,EAAM,MAENA,EAAK,CACL,MAAM0J,EAAY5B,EAAM9H,GACxBjE,EAAK2C,SAAWgL,EAAUrB,KAC1BtM,EAAK8C,OACsB,UAAvB6K,EAAU7I,UAA+C,QAAvB6I,EAAU7I,SAChD9E,EAAK6C,KAAO8K,EAAU9K,KAClB8K,EAAUzM,QACVlB,EAAKkB,MAAQyM,EAAUzM,MAC9B,MACQlB,EAAKsM,OACVtM,EAAK2C,SAAWoJ,EAAM/L,EAAKsM,MAAMA,MAErCvM,EAAsBpC,KAAMqC,GAC5BrC,KAAKmF,OACD,MAAQ9C,EAAK8C,OACP9C,EAAK8C,OACe,oBAAb8B,UAA4B,WAAaA,SAASE,SAC/D9E,EAAK2C,WAAa3C,EAAK6C,OAEvB7C,EAAK6C,KAAOlF,KAAKmF,OAAS,MAAQ,MAEtCnF,KAAKgF,SACD3C,EAAK2C,WACoB,oBAAbiC,SAA2BA,SAASjC,SAAW,aAC/DhF,KAAKkF,KACD7C,EAAK6C,OACoB,oBAAb+B,UAA4BA,SAAS/B,KACvC+B,SAAS/B,KACTlF,KAAKmF,OACD,MACA,MAClBnF,KAAK8N,WAAa,GAClB9N,KAAKiQ,EAAoB,GACzB5N,EAAKyL,WAAW7T,SAASiW,IACrB,MAAMC,EAAgBD,EAAE1V,UAAUkL,KAClC1F,KAAK8N,WAAW5N,KAAKiQ,GACrBnQ,KAAKiQ,EAAkBE,GAAiBD,CAAC,IAE7ClQ,KAAKqC,KAAOxI,OAAOsQ,OAAO,CACtBrF,KAAM,aACNsL,OAAO,EACPxH,iBAAiB,EACjByH,SAAS,EACT7J,eAAgB,IAChB8J,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfzE,iBAAkB,CAAE,EACpB0E,qBAAqB,GACtBtO,GACHrC,KAAKqC,KAAKyC,KACN9E,KAAKqC,KAAKyC,KAAKyJ,QAAQ,MAAO,KACzBvO,KAAKqC,KAAKkO,iBAAmB,IAAM,IACb,iBAApBvQ,KAAKqC,KAAKkB,QACjBvD,KAAKqC,KAAKkB,MRhGf,SAAgBqN,GACnB,IAAIC,EAAM,CAAA,EACNC,EAAQF,EAAGnV,MAAM,KACrB,IAAK,IAAIS,EAAI,EAAG6U,EAAID,EAAMnU,OAAQT,EAAI6U,EAAG7U,IAAK,CAC1C,IAAI8U,EAAOF,EAAM5U,GAAGT,MAAM,KAC1BoV,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC9D,CACD,OAAOH,CACX,CQwF8BtT,CAAOyC,KAAKqC,KAAKkB,QAEnC8L,IACIrP,KAAKqC,KAAKsO,sBAIV3Q,KAAKkR,EAA6B,KAC1BlR,KAAKmR,YAELnR,KAAKmR,UAAU3Q,qBACfR,KAAKmR,UAAUrN,QAClB,EAELjE,iBAAiB,eAAgBG,KAAKkR,GAA4B,IAEhD,cAAlBlR,KAAKgF,WACLhF,KAAKoR,EAAwB,KACzBpR,KAAKqR,EAAS,kBAAmB,CAC7BnO,YAAa,2BACf,EAENoM,EAAwBpP,KAAKF,KAAKoR,KAGtCpR,KAAKqC,KAAKuG,kBACV5I,KAAKsR,OAAaC,GAEtBvR,KAAKwR,GACR,CAQD,eAAAC,CAAgB/L,GACZ,MAAMnC,EAAQ1J,OAAOsQ,OAAO,CAAE,EAAEnK,KAAKqC,KAAKkB,OAE1CA,EAAMmO,IbPU,EaShBnO,EAAM4N,UAAYzL,EAEd1F,KAAK2R,KACLpO,EAAMkD,IAAMzG,KAAK2R,IACrB,MAAMtP,EAAOxI,OAAOsQ,OAAO,CAAA,EAAInK,KAAKqC,KAAM,CACtCkB,QACAC,OAAQxD,KACRgF,SAAUhF,KAAKgF,SACfG,OAAQnF,KAAKmF,OACbD,KAAMlF,KAAKkF,MACZlF,KAAKqC,KAAK4J,iBAAiBvG,IAC9B,OAAO,IAAI1F,KAAKiQ,EAAkBvK,GAAMrD,EAC3C,CAMD,CAAAmP,GACI,GAA+B,IAA3BxR,KAAK8N,WAAWnR,OAKhB,YAHAqD,KAAKsB,cAAa,KACdtB,KAAKgB,aAAa,QAAS,0BAA0B,GACtD,GAGP,MAAMmP,EAAgBnQ,KAAKqC,KAAKiO,iBAC5Bd,EAAqBoC,wBACqB,IAA1C5R,KAAK8N,WAAW7I,QAAQ,aACtB,YACAjF,KAAK8N,WAAW,GACtB9N,KAAK4D,WAAa,UAClB,MAAMuN,EAAYnR,KAAKyR,gBAAgBtB,GACvCgB,EAAUxN,OACV3D,KAAK6R,aAAaV,EACrB,CAMD,YAAAU,CAAaV,GACLnR,KAAKmR,WACLnR,KAAKmR,UAAU3Q,qBAGnBR,KAAKmR,UAAYA,EAEjBA,EACKvR,GAAG,QAASI,KAAK8R,EAASvP,KAAKvC,OAC/BJ,GAAG,SAAUI,KAAK+R,EAAUxP,KAAKvC,OACjCJ,GAAG,QAASI,KAAKoJ,EAAS7G,KAAKvC,OAC/BJ,GAAG,SAAUqD,GAAWjD,KAAKqR,EAAS,kBAAmBpO,IACjE,CAMD,MAAAmB,GACIpE,KAAK4D,WAAa,OAClB4L,EAAqBoC,sBACjB,cAAgB5R,KAAKmR,UAAUzL,KACnC1F,KAAKgB,aAAa,QAClBhB,KAAKgS,OACR,CAMD,CAAAD,CAAUjU,GACN,GAAI,YAAckC,KAAK4D,YACnB,SAAW5D,KAAK4D,YAChB,YAAc5D,KAAK4D,WAInB,OAHA5D,KAAKgB,aAAa,SAAUlD,GAE5BkC,KAAKgB,aAAa,aACVlD,EAAO1D,MACX,IAAK,OACD4F,KAAKiS,YAAYC,KAAK9D,MAAMtQ,EAAOzD,OACnC,MACJ,IAAK,OACD2F,KAAKmS,EAAY,QACjBnS,KAAKgB,aAAa,QAClBhB,KAAKgB,aAAa,QAClBhB,KAAKoS,IACL,MACJ,IAAK,QACD,MAAMvL,EAAM,IAAI9D,MAAM,gBAEtB8D,EAAIwL,KAAOvU,EAAOzD,KAClB2F,KAAKoJ,EAASvC,GACd,MACJ,IAAK,UACD7G,KAAKgB,aAAa,OAAQlD,EAAOzD,MACjC2F,KAAKgB,aAAa,UAAWlD,EAAOzD,MAMnD,CAOD,WAAA4X,CAAY5X,GACR2F,KAAKgB,aAAa,YAAa3G,GAC/B2F,KAAK2R,GAAKtX,EAAKoM,IACfzG,KAAKmR,UAAU5N,MAAMkD,IAAMpM,EAAKoM,IAChCzG,KAAK2P,EAAgBtV,EAAKiY,aAC1BtS,KAAK4P,EAAevV,EAAKkY,YACzBvS,KAAK6P,EAAcxV,EAAKmS,WACxBxM,KAAKoE,SAED,WAAapE,KAAK4D,YAEtB5D,KAAKoS,GACR,CAMD,CAAAA,GACIpS,KAAKwC,eAAexC,KAAKwS,GACzB,MAAMC,EAAQzS,KAAK2P,EAAgB3P,KAAK4P,EACxC5P,KAAK8P,EAAmBpN,KAAKC,MAAQ8P,EACrCzS,KAAKwS,EAAoBxS,KAAKsB,cAAa,KACvCtB,KAAKqR,EAAS,eAAe,GAC9BoB,GACCzS,KAAKqC,KAAK2I,WACVhL,KAAKwS,EAAkBtH,OAE9B,CAMD,CAAA4G,GACI9R,KAAKyP,YAAY7O,OAAO,EAAGZ,KAAK0P,GAIhC1P,KAAK0P,EAAiB,EAClB,IAAM1P,KAAKyP,YAAY9S,OACvBqD,KAAKgB,aAAa,SAGlBhB,KAAKgS,OAEZ,CAMD,KAAAA,GACI,GAAI,WAAahS,KAAK4D,YAClB5D,KAAKmR,UAAU7N,WACdtD,KAAK0S,WACN1S,KAAKyP,YAAY9S,OAAQ,CACzB,MAAMuH,EAAUlE,KAAK2S,IACrB3S,KAAKmR,UAAUlN,KAAKC,GAGpBlE,KAAK0P,EAAiBxL,EAAQvH,OAC9BqD,KAAKgB,aAAa,QACrB,CACJ,CAOD,CAAA2R,GAII,KAH+B3S,KAAK6P,GACR,YAAxB7P,KAAKmR,UAAUzL,MACf1F,KAAKyP,YAAY9S,OAAS,GAE1B,OAAOqD,KAAKyP,YAEhB,IAAImD,EAAc,EAClB,IAAK,IAAI1W,EAAI,EAAGA,EAAI8D,KAAKyP,YAAY9S,OAAQT,IAAK,CAC9C,MAAM7B,EAAO2F,KAAKyP,YAAYvT,GAAG7B,KAIjC,GAHIA,IACAuY,GVxUO,iBADI9X,EUyUeT,GVlU1C,SAAoBiL,GAChB,IAAIuN,EAAI,EAAGlW,EAAS,EACpB,IAAK,IAAIT,EAAI,EAAG6U,EAAIzL,EAAI3I,OAAQT,EAAI6U,EAAG7U,IACnC2W,EAAIvN,EAAInJ,WAAWD,GACf2W,EAAI,IACJlW,GAAU,EAELkW,EAAI,KACTlW,GAAU,EAELkW,EAAI,OAAUA,GAAK,MACxBlW,GAAU,GAGVT,IACAS,GAAU,GAGlB,OAAOA,CACX,CAxBemW,CAAWhY,GAGf8H,KAAKmQ,KAPQ,MAOFjY,EAAIgB,YAAchB,EAAIwE,QUsU5BpD,EAAI,GAAK0W,EAAc5S,KAAK6P,EAC5B,OAAO7P,KAAKyP,YAAYhQ,MAAM,EAAGvD,GAErC0W,GAAe,CAClB,CV/UF,IAAoB9X,EUgVnB,OAAOkF,KAAKyP,WACf,CAUa,CAAAuD,GACV,IAAKhT,KAAK8P,EACN,OAAO,EACX,MAAMmD,EAAavQ,KAAKC,MAAQ3C,KAAK8P,EAOrC,OANImD,IACAjT,KAAK8P,EAAmB,EACxB3O,GAAS,KACLnB,KAAKqR,EAAS,eAAe,GAC9BrR,KAAKsB,eAEL2R,CACV,CASD,KAAA9O,CAAM+O,EAAKC,EAASpT,GAEhB,OADAC,KAAKmS,EAAY,UAAWe,EAAKC,EAASpT,GACnCC,IACV,CASD,IAAAiE,CAAKiP,EAAKC,EAASpT,GAEf,OADAC,KAAKmS,EAAY,UAAWe,EAAKC,EAASpT,GACnCC,IACV,CAUD,CAAAmS,CAAY/X,EAAMC,EAAM8Y,EAASpT,GAS7B,GARI,mBAAsB1F,IACtB0F,EAAK1F,EACLA,OAAO2N,GAEP,mBAAsBmL,IACtBpT,EAAKoT,EACLA,EAAU,MAEV,YAAcnT,KAAK4D,YAAc,WAAa5D,KAAK4D,WACnD,QAEJuP,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,MAAMtV,EAAS,CACX1D,KAAMA,EACNC,KAAMA,EACN8Y,QAASA,GAEbnT,KAAKgB,aAAa,eAAgBlD,GAClCkC,KAAKyP,YAAYvP,KAAKpC,GAClBiC,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAKgS,OACR,CAID,KAAAlO,GACI,MAAMA,EAAQ,KACV9D,KAAKqR,EAAS,gBACdrR,KAAKmR,UAAUrN,OAAO,EAEpBuP,EAAkB,KACpBrT,KAAKI,IAAI,UAAWiT,GACpBrT,KAAKI,IAAI,eAAgBiT,GACzBvP,GAAO,EAELwP,EAAiB,KAEnBtT,KAAKG,KAAK,UAAWkT,GACrBrT,KAAKG,KAAK,eAAgBkT,EAAgB,EAqB9C,MAnBI,YAAcrT,KAAK4D,YAAc,SAAW5D,KAAK4D,aACjD5D,KAAK4D,WAAa,UACd5D,KAAKyP,YAAY9S,OACjBqD,KAAKG,KAAK,SAAS,KACXH,KAAK0S,UACLY,IAGAxP,GACH,IAGA9D,KAAK0S,UACVY,IAGAxP,KAGD9D,IACV,CAMD,CAAAoJ,CAASvC,GAEL,GADA2I,EAAqBoC,uBAAwB,EACzC5R,KAAKqC,KAAKkR,kBACVvT,KAAK8N,WAAWnR,OAAS,GACL,YAApBqD,KAAK4D,WAEL,OADA5D,KAAK8N,WAAWvO,QACTS,KAAKwR,IAEhBxR,KAAKgB,aAAa,QAAS6F,GAC3B7G,KAAKqR,EAAS,kBAAmBxK,EACpC,CAMD,CAAAwK,CAASpO,EAAQC,GACb,GAAI,YAAclD,KAAK4D,YACnB,SAAW5D,KAAK4D,YAChB,YAAc5D,KAAK4D,WAAY,CAS/B,GAPA5D,KAAKwC,eAAexC,KAAKwS,GAEzBxS,KAAKmR,UAAU3Q,mBAAmB,SAElCR,KAAKmR,UAAUrN,QAEf9D,KAAKmR,UAAU3Q,qBACX6O,IACIrP,KAAKkR,GACLzQ,oBAAoB,eAAgBT,KAAKkR,GAA4B,GAErElR,KAAKoR,GAAuB,CAC5B,MAAMlV,EAAIoT,EAAwBrK,QAAQjF,KAAKoR,IACpC,IAAPlV,GACAoT,EAAwB1O,OAAO1E,EAAG,EAEzC,CAGL8D,KAAK4D,WAAa,SAElB5D,KAAK2R,GAAK,KAEV3R,KAAKgB,aAAa,QAASiC,EAAQC,GAGnClD,KAAKyP,YAAc,GACnBzP,KAAK0P,EAAiB,CACzB,CACJ,EAELF,EAAqBrI,SbhYG,EawZjB,MAAMqM,UAA0BhE,EACnC,WAAAxM,GACII,SAAS9C,WACTN,KAAKyT,EAAY,EACpB,CACD,MAAArP,GAEI,GADAhB,MAAMgB,SACF,SAAWpE,KAAK4D,YAAc5D,KAAKqC,KAAKgO,QACxC,IAAK,IAAInU,EAAI,EAAGA,EAAI8D,KAAKyT,EAAU9W,OAAQT,IACvC8D,KAAK0T,GAAO1T,KAAKyT,EAAUvX,GAGtC,CAOD,EAAAwX,CAAOhO,GACH,IAAIyL,EAAYnR,KAAKyR,gBAAgB/L,GACjCiO,GAAS,EACbnE,EAAqBoC,uBAAwB,EAC7C,MAAMgC,EAAkB,KAChBD,IAEJxC,EAAUlN,KAAK,CAAC,CAAE7J,KAAM,OAAQC,KAAM,WACtC8W,EAAUhR,KAAK,UAAW+S,IACtB,IAAIS,EAEJ,GAAI,SAAWT,EAAI9Y,MAAQ,UAAY8Y,EAAI7Y,KAAM,CAG7C,GAFA2F,KAAK0S,WAAY,EACjB1S,KAAKgB,aAAa,YAAamQ,IAC1BA,EACD,OACJ3B,EAAqBoC,sBACjB,cAAgBT,EAAUzL,KAC9B1F,KAAKmR,UAAU3M,OAAM,KACbmP,GAEA,WAAa3T,KAAK4D,aAEtBiQ,IACA7T,KAAK6R,aAAaV,GAClBA,EAAUlN,KAAK,CAAC,CAAE7J,KAAM,aACxB4F,KAAKgB,aAAa,UAAWmQ,GAC7BA,EAAY,KACZnR,KAAK0S,WAAY,EACjB1S,KAAKgS,QAAO,GAEnB,KACI,CACD,MAAMnL,EAAM,IAAI9D,MAAM,eAEtB8D,EAAIsK,UAAYA,EAAUzL,KAC1B1F,KAAKgB,aAAa,eAAgB6F,EACrC,KACH,EAEN,SAASiN,IACDH,IAGJA,GAAS,EACTE,IACA1C,EAAUrN,QACVqN,EAAY,KACf,CAED,MAAM5F,EAAW1E,IACb,MAAMkN,EAAQ,IAAIhR,MAAM,gBAAkB8D,GAE1CkN,EAAM5C,UAAYA,EAAUzL,KAC5BoO,IACA9T,KAAKgB,aAAa,eAAgB+S,EAAM,EAE5C,SAASC,IACLzI,EAAQ,mBACX,CAED,SAASJ,IACLI,EAAQ,gBACX,CAED,SAAS0I,EAAUC,GACX/C,GAAa+C,EAAGxO,OAASyL,EAAUzL,MACnCoO,GAEP,CAED,MAAMD,EAAU,KACZ1C,EAAU5Q,eAAe,OAAQqT,GACjCzC,EAAU5Q,eAAe,QAASgL,GAClC4F,EAAU5Q,eAAe,QAASyT,GAClChU,KAAKI,IAAI,QAAS+K,GAClBnL,KAAKI,IAAI,YAAa6T,EAAU,EAEpC9C,EAAUhR,KAAK,OAAQyT,GACvBzC,EAAUhR,KAAK,QAASoL,GACxB4F,EAAUhR,KAAK,QAAS6T,GACxBhU,KAAKG,KAAK,QAASgL,GACnBnL,KAAKG,KAAK,YAAa8T,IACyB,IAA5CjU,KAAKyT,EAAUxO,QAAQ,iBACd,iBAATS,EAEA1F,KAAKsB,cAAa,KACTqS,GACDxC,EAAUxN,MACb,GACF,KAGHwN,EAAUxN,MAEjB,CACD,WAAAsO,CAAY5X,GACR2F,KAAKyT,EAAYzT,KAAKmU,GAAgB9Z,EAAK+Z,UAC3ChR,MAAM6O,YAAY5X,EACrB,CAOD,EAAA8Z,CAAgBC,GACZ,MAAMC,EAAmB,GACzB,IAAK,IAAInY,EAAI,EAAGA,EAAIkY,EAASzX,OAAQT,KAC5B8D,KAAK8N,WAAW7I,QAAQmP,EAASlY,KAClCmY,EAAiBnU,KAAKkU,EAASlY,IAEvC,OAAOmY,CACV,EAqBE,MAAMC,WAAed,EACxB,WAAAxQ,CAAYsD,EAAKjE,EAAO,IACpB,MAAMkS,EAAmB,iBAARjO,EAAmBA,EAAMjE,IACrCkS,EAAEzG,YACFyG,EAAEzG,YAAyC,iBAApByG,EAAEzG,WAAW,MACrCyG,EAAEzG,YAAcyG,EAAEzG,YAAc,CAAC,UAAW,YAAa,iBACpD0G,KAAKrE,GAAkBsE,EAAmBtE,KAC1CuE,QAAQxE,KAAQA,KAEzB9M,MAAMkD,EAAKiO,EACd,EC3sBE,MAAMI,WAAcnP,EACvB,MAAAK,GACI7F,KAAK4U,KACA3W,MAAM4W,IACP,IAAKA,EAAIC,GACL,OAAO9U,KAAK0D,QAAQ,mBAAoBmR,EAAI3L,OAAQ2L,GAExDA,EAAIE,OAAO9W,MAAM5D,GAAS2F,KAAKqE,OAAOhK,IAAM,IAE3C8R,OAAOtF,IACR7G,KAAK0D,QAAQ,mBAAoBmD,EAAI,GAE5C,CACD,OAAAR,CAAQhM,EAAMa,GACV8E,KAAK4U,GAAOva,GACP4D,MAAM4W,IACP,IAAKA,EAAIC,GACL,OAAO9U,KAAK0D,QAAQ,oBAAqBmR,EAAI3L,OAAQ2L,GAEzD3Z,GAAU,IAETiR,OAAOtF,IACR7G,KAAK0D,QAAQ,oBAAqBmD,EAAI,GAE7C,CACD,EAAA+N,CAAOva,GACH,IAAI6N,EACJ,MAAM8M,OAAkBhN,IAAT3N,EACTsQ,EAAU,IAAIsK,QAAQjV,KAAKqC,KAAKiG,cAKtC,OAJI0M,GACArK,EAAQuK,IAAI,eAAgB,4BAEE,QAAjChN,EAAKlI,KAAKwD,OAAO8N,SAA+B,IAAPpJ,GAAyBA,EAAGiN,cAAcxK,GAC7EyK,MAAMpV,KAAKsG,MAAO,CACrBiB,OAAQyN,EAAS,OAAS,MAC1BK,KAAML,EAAS3a,EAAO,KACtBsQ,UACA2K,YAAatV,KAAKqC,KAAKuG,gBAAkB,UAAY,SACtD3K,MAAM4W,IACL,IAAI3M,EAGJ,OADkC,QAAjCA,EAAKlI,KAAKwD,OAAO8N,SAA+B,IAAPpJ,GAAyBA,EAAGc,aAAa6L,EAAIlK,QAAQ4K,gBACxFV,CAAG,GAEjB,ECnDO,MAAC1N,GAAWmN,GAAOnN"}