<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple Bus Tracking Client</title>
  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
     integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
     crossorigin=""/>
  <!-- Leaflet JavaScript -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
     integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
     crossorigin=""></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    .header {
      background-color: #4285F4;
      color: white;
      padding: 10px 20px;
    }
    .container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
    .sidebar {
      width: 300px;
      padding: 20px;
      background-color: #f8f9fa;
      overflow-y: auto;
    }
    .map-container {
      flex: 1;
      position: relative;
    }
    #map {
      height: 100%;
      width: 100%;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input, button {
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    button {
      background-color: #4285F4;
      color: white;
      border: none;
      cursor: pointer;
    }
    button:hover {
      background-color: #3b77db;
    }
    .log {
      margin-top: 20px;
      height: 200px;
      overflow-y: auto;
      background-color: #fff;
      padding: 10px;
      border: 1px solid #ddd;
      font-family: monospace;
      font-size: 12px;
    }
    .status {
      padding: 5px 10px;
      border-radius: 4px;
      margin-bottom: 10px;
      font-weight: bold;
    }
    .connected {
      background-color: #d4edda;
      color: #155724;
    }
    .disconnected {
      background-color: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Simple Bus Tracking Client</h1>
  </div>

  <div class="container">
    <div class="sidebar">
      <div id="connectionStatus" class="status disconnected">Disconnected</div>

      <div class="form-group">
        <label for="serverUrl">Server URL:</label>
        <input type="text" id="serverUrl" value="http://localhost:5000" style="width: 100%;">
      </div>

      <div class="form-group">
        <label for="token">JWT Token:</label>
        <input type="text" id="token" placeholder="Enter your JWT token" style="width: 100%;">
      </div>

      <div class="form-group">
        <button id="connectBtn">Connect</button>
        <button id="disconnectBtn" disabled>Disconnect</button>
      </div>

      <div class="form-group">
        <label for="busId">Bus ID:</label>
        <input type="text" id="busId" placeholder="Enter bus ID" style="width: 70%;">
        <button id="subscribeBusBtn" disabled>Subscribe</button>
      </div>

      <div class="form-group">
        <button id="testMarkerBtn">Add Test Marker</button>
        <button id="clearMarkersBtn">Clear All Markers</button>
      </div>

      <div class="form-group">
        <button id="emitTestUpdateBtn">Emit Test Update</button>
        <button id="checkSocketBtn">Check Socket</button>
      </div>

      <h3>Event Log</h3>
      <div id="log" class="log"></div>
    </div>

    <div class="map-container">
      <div id="map"></div>
    </div>
  </div>

  <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
  <script>
    // DOM Elements
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const serverUrlInput = document.getElementById('serverUrl');
    const tokenInput = document.getElementById('token');
    const connectionStatus = document.getElementById('connectionStatus');
    const logElement = document.getElementById('log');
    const busIdInput = document.getElementById('busId');
    const subscribeBusBtn = document.getElementById('subscribeBusBtn');

    // Socket.IO instance
    let socket = null;
    let busLocationsSocket = null;

    // Map and markers
    let map = null;
    let markers = {};

    // Initialize map
    function initMap() {
      try {
        log('Initializing map...');

        // Create map
        map = L.map('map').setView([37.7749, -122.4194], 12);

        // Add OpenStreetMap tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19
        }).addTo(map);

        // Add a test marker
        const testMarker = L.marker([37.7749, -122.4194])
          .addTo(map)
          .bindPopup('Test Marker')
          .openPopup();

        // Add a circle marker to test that type of marker
        const circleMarker = L.circleMarker([37.7749, -122.4194], {
          radius: 8,
          fillColor: '#ff0000',
          color: '#fff',
          weight: 2,
          opacity: 1,
          fillOpacity: 0.8
        }).addTo(map);
        circleMarker.bindPopup('Test Circle Marker');

        log('Map initialized with test markers');

        // Add map click handler for debugging
        map.on('click', function(e) {
          const clickLat = e.latlng.lat.toFixed(6);
          const clickLng = e.latlng.lng.toFixed(6);
          log(`Map clicked at: ${clickLat}, ${clickLng}`);

          // Create a test marker at the clicked location
          const testData = {
            busId: 'click-' + Date.now(),
            latitude: e.latlng.lat,
            longitude: e.latlng.lng,
            heading: 0,
            speed: 0,
            timestamp: Date.now(),
            source: 'test',
            bus: {
              busNumber: 'CLICK',
              busName: 'Clicked Location'
            }
          };

          updateBusMarker(testData);
        });
      } catch (error) {
        log(`Error initializing map: ${error.message}`);
        console.error('Map initialization error:', error);
      }
    }

    // Connect to WebSocket server
    connectBtn.addEventListener('click', () => {
      const serverUrl = serverUrlInput.value;
      const token = tokenInput.value;

      if (!token) {
        log('Error: Authentication token is required');
        return;
      }

      try {
        log(`Connecting to ${serverUrl} with token ${token.substring(0, 10)}...`);

        // Connect to main namespace
        socket = io(serverUrl, {
          auth: { token },
          reconnection: true,
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          timeout: 20000
        });

        // Connect to bus-locations namespace
        busLocationsSocket = io(`${serverUrl}/bus-locations`, {
          auth: { token },
          reconnection: true,
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          timeout: 20000
        });

        log('Socket.IO instances created, waiting for connection...');

        // Main socket events
        socket.on('connect', () => {
          setConnected(true);
          log(`Connected to main namespace (ID: ${socket.id})`);
        });

        socket.on('disconnect', (reason) => {
          setConnected(false);
          log(`Disconnected from main namespace: ${reason}`);
        });

        socket.on('connect_error', (error) => {
          log(`Main connection error: ${error.message}`);
          console.error('Main connection error:', error);
          setConnected(false);
        });

        socket.on('error', (error) => {
          log(`Main socket error: ${error.message}`);
          console.error('Main socket error:', error);
        });

        socket.on('reconnect_attempt', (attempt) => {
          log(`Main reconnection attempt: ${attempt}`);
        });

        // Bus locations socket events
        busLocationsSocket.on('connect', () => {
          log(`Connected to bus-locations namespace (ID: ${busLocationsSocket.id})`);
          subscribeBusBtn.disabled = false;
        });

        busLocationsSocket.on('disconnect', (reason) => {
          log(`Disconnected from bus-locations namespace: ${reason}`);
          subscribeBusBtn.disabled = true;
        });

        busLocationsSocket.on('connect_error', (error) => {
          log(`Bus locations connection error: ${error.message}`);
          console.error('Bus locations connection error:', error);
          subscribeBusBtn.disabled = true;
        });

        busLocationsSocket.on('error', (error) => {
          log(`Bus locations socket error: ${error.message}`);
          console.error('Bus locations socket error:', error);
        });

        busLocationsSocket.on('reconnect_attempt', (attempt) => {
          log(`Bus locations reconnection attempt: ${attempt}`);
        });

        // Listen for debug messages
        busLocationsSocket.on('debug', (data) => {
          log(`DEBUG: ${data.message}`);
          console.log('Debug data:', data);
        });

        // Listen for bus location updates
        busLocationsSocket.on('bus-location-update', (data) => {
          try {
            const busInfo = data.bus ? data.bus.busNumber : (data.busId ? data.busId.substring(0, 5) : 'Unknown');
            log(`Bus ${busInfo} at ${data.latitude.toFixed(6)}, ${data.longitude.toFixed(6)} (source: ${data.source || 'unknown'})`);
            console.log('Received bus location update:', data);

            // Try to update the marker
            updateBusMarker(data);
          } catch (error) {
            log(`Error processing bus location update: ${error.message}`);
            console.error('Error processing bus location update:', error, data);
          }
        });
      } catch (error) {
        log(`Error initializing connection: ${error.message}`);
        console.error('Connection initialization error:', error);
      }
    });

    // Disconnect from WebSocket server
    disconnectBtn.addEventListener('click', () => {
      if (socket) socket.disconnect();
      if (busLocationsSocket) busLocationsSocket.disconnect();
      setConnected(false);
    });

    // Subscribe to bus
    subscribeBusBtn.addEventListener('click', () => {
      const busId = busIdInput.value;
      if (!busId) {
        log('Error: Bus ID is required');
        return;
      }

      if (!busLocationsSocket || !busLocationsSocket.connected) {
        log('Error: Not connected to WebSocket server');
        return;
      }

      try {
        log(`Subscribing to bus: ${busId}...`);
        busLocationsSocket.emit('subscribe-bus', busId);

        // Also try to emit a test event to verify the connection
        busLocationsSocket.emit('ping', { time: Date.now() }, (response) => {
          log(`Ping response: ${JSON.stringify(response)}`);
        });

        log(`Subscription request sent for bus: ${busId}`);

        // Try to get any existing bus location
        const testData = {
          busId: busId,
          latitude: 37.7749,
          longitude: -122.4194,
          heading: 0,
          speed: 0,
          timestamp: Date.now(),
          source: 'test',
          bus: {
            busNumber: 'TEST',
            busName: 'Test Bus'
          }
        };

        // Add a test marker to verify the map is working
        log('Adding test marker for the subscribed bus');
        updateBusMarker(testData);
      } catch (error) {
        log(`Error subscribing to bus: ${error.message}`);
        console.error('Subscription error:', error);
      }
    });

    // Update bus marker on map
    function updateBusMarker(data) {
      if (!map) {
        log('Map not initialized yet');
        return;
      }

      try {
        // Validate required data
        if (!data.busId || !data.latitude || !data.longitude) {
          log(`Invalid bus data: missing required fields`);
          console.error('Invalid bus data:', data);
          return;
        }

        const { busId, latitude, longitude, source } = data;
        const busNumber = data.bus ? data.bus.busNumber : (busId ? busId.substring(0, 5) : 'Unknown');

        log(`Processing marker for bus ${busNumber}`);

        // Use a simple circle marker
        if (markers[busId]) {
          // Update existing marker
          log(`Updating existing marker for bus ${busNumber}`);
          markers[busId].setLatLng([latitude, longitude]);

          // Update popup content
          markers[busId].setPopupContent(createPopupContent(data));
        } else {
          // Create new marker
          log(`Creating new marker for bus ${busNumber} at ${latitude}, ${longitude}`);
          const color = source === 'passenger' || source === 'test' ? '#FFA500' : '#4285F4';

          // Create a circle marker
          const marker = L.circleMarker([latitude, longitude], {
            radius: 8,
            fillColor: color,
            color: '#fff',
            weight: 2,
            opacity: 1,
            fillOpacity: 0.8
          }).addTo(map);

          // Add popup
          marker.bindPopup(createPopupContent(data));

          // Store marker
          markers[busId] = marker;

          // Center map on first marker
          if (Object.keys(markers).length === 1) {
            map.setView([latitude, longitude], 14);
          }

          log(`Marker created for bus ${busNumber}`);
        }
      } catch (error) {
        log(`Error updating marker: ${error.message}`);
        console.error('Error updating marker:', error, data);
      }
    }

    // Create popup content
    function createPopupContent(data) {
      const { busId, latitude, longitude, speed, heading, timestamp, source } = data;
      const busInfo = data.bus ? `${data.bus.busNumber} - ${data.bus.busName}` : busId;
      const formattedTime = new Date(timestamp).toLocaleTimeString();

      return `
        <div>
          <strong>Bus:</strong> ${busInfo}<br>
          <strong>Position:</strong> ${latitude.toFixed(6)}, ${longitude.toFixed(6)}<br>
          <strong>Speed:</strong> ${speed ? speed.toFixed(1) + ' km/h' : 'N/A'}<br>
          <strong>Heading:</strong> ${heading ? heading.toFixed(1) + '°' : 'N/A'}<br>
          <strong>Time:</strong> ${formattedTime}<br>
          <strong>Source:</strong> ${source || 'driver'}
        </div>
      `;
    }

    // Set connection status
    function setConnected(isConnected) {
      if (isConnected) {
        connectionStatus.textContent = 'Connected';
        connectionStatus.className = 'status connected';
        connectBtn.disabled = true;
        disconnectBtn.disabled = false;
      } else {
        connectionStatus.textContent = 'Disconnected';
        connectionStatus.className = 'status disconnected';
        connectBtn.disabled = false;
        disconnectBtn.disabled = true;
        subscribeBusBtn.disabled = true;
      }
    }

    // Log message
    function log(message) {
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = document.createElement('div');
      logEntry.textContent = `[${timestamp}] ${message}`;
      logElement.appendChild(logEntry);
      logElement.scrollTop = logElement.scrollHeight;
    }

    // Test marker button
    document.getElementById('testMarkerBtn').addEventListener('click', () => {
      if (!map) {
        log('Map not initialized yet');
        return;
      }

      // Generate random coordinates near the center of the map
      const center = map.getCenter();
      const lat = center.lat + (Math.random() - 0.5) * 0.01;
      const lng = center.lng + (Math.random() - 0.5) * 0.01;

      // Create test data
      const testData = {
        busId: 'test-' + Date.now(),
        latitude: lat,
        longitude: lng,
        heading: Math.random() * 360,
        speed: Math.random() * 50,
        timestamp: Date.now(),
        source: 'test',
        bus: {
          busNumber: 'TEST',
          busName: 'Test Bus'
        }
      };

      log(`Adding test marker at ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
      updateBusMarker(testData);
    });

    // Clear markers button
    document.getElementById('clearMarkersBtn').addEventListener('click', () => {
      if (!map) {
        log('Map not initialized yet');
        return;
      }

      // Remove all markers from the map
      Object.values(markers).forEach(marker => {
        map.removeLayer(marker);
      });

      // Clear markers object
      markers = {};

      log('All markers cleared');
    });

    // Emit test update button
    document.getElementById('emitTestUpdateBtn').addEventListener('click', () => {
      if (!busLocationsSocket || !busLocationsSocket.connected) {
        log('Error: Not connected to WebSocket server');
        return;
      }

      const busId = busIdInput.value;
      if (!busId) {
        log('Error: Bus ID is required');
        return;
      }

      // Generate test data
      const testData = {
        busId: busId,
        latitude: 37.7749 + (Math.random() - 0.5) * 0.01,
        longitude: -122.4194 + (Math.random() - 0.5) * 0.01,
        heading: Math.random() * 360,
        speed: Math.random() * 50,
        timestamp: Date.now(),
        source: 'test',
        bus: {
          busNumber: 'TEST',
          busName: 'Test Bus'
        }
      };

      // Manually emit the event to yourself
      log(`Manually emitting bus-location-update for bus ${busId}`);
      console.log('Emitting test data:', testData);

      // This is a hack to test if the event handler is working
      // It directly calls the event handler function with the test data
      busLocationsSocket._callbacks['$bus-location-update'][0](testData);

      // Also try to update the marker directly
      updateBusMarker(testData);
    });

    // Check socket button
    document.getElementById('checkSocketBtn').addEventListener('click', () => {
      if (!busLocationsSocket) {
        log('Socket not initialized');
        return;
      }

      log(`Socket status: ${busLocationsSocket.connected ? 'Connected' : 'Disconnected'}`);
      log(`Socket ID: ${busLocationsSocket.id || 'None'}`);
      log(`Socket namespace: ${busLocationsSocket.nsp || '/bus-locations'}`);

      // Check rooms
      const busId = busIdInput.value;
      if (busId) {
        log(`Should be subscribed to bus:${busId}`);
      }

      // Log all event handlers
      const eventHandlers = Object.keys(busLocationsSocket._callbacks || {});
      log(`Registered event handlers: ${eventHandlers.join(', ')}`);

      console.log('Socket object:', busLocationsSocket);
    });

    // Initialize map when page loads
    document.addEventListener('DOMContentLoaded', initMap);
  </script>
</body>
</html>
