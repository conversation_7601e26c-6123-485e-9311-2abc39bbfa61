import { getAuthToken } from './auth';
import type { LoginCredentials, RegisterCredentials, UserData } from './auth';

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// API request options
interface RequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  includeAuth?: boolean;
}

// Generic API request function
export async function apiRequest<T>(
  endpoint: string,
  options: RequestOptions = { method: 'GET', includeAuth: true }
): Promise<T> {
  const { method, body, includeAuth = true } = options;

  // Prepare headers
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // Add auth token if required
  if (includeAuth) {
    const token = getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }

  // Prepare request options
  const requestOptions: RequestInit = {
    method,
    headers,
    // Include credentials for cross-origin requests to allow cookies
    credentials: 'include',
  };

  // Add body for non-GET requests
  if (body && method !== 'GET') {
    requestOptions.body = JSON.stringify(body);
  }

  // Make the request
  const response = await fetch(`${API_BASE_URL}${endpoint}`, requestOptions);

  // Parse the response
  const data = await response.json();

  // Handle error responses
  if (!response.ok) {
    throw new Error(data.message || 'Something went wrong');
  }

  return data as T;
}

// Auth API functions
export const authApi = {
  // User authentication
  registerUser: (credentials: RegisterCredentials): Promise<UserData> => {
    return apiRequest<UserData>('/users/register', {
      method: 'POST',
      body: credentials,
      includeAuth: false,
    });
  },

  loginUser: (credentials: LoginCredentials): Promise<UserData> => {
    return apiRequest<UserData>('/users/login', {
      method: 'POST',
      body: credentials,
      includeAuth: false,
    });
  },

  getUserProfile: (): Promise<UserData> => {
    return apiRequest<UserData>('/users/profile');
  },

  // Admin authentication
  loginAdmin: (credentials: LoginCredentials): Promise<UserData> => {
    return apiRequest<UserData>('/admin/login', {
      method: 'POST',
      body: credentials,
      includeAuth: false,
    });
  },

  // Test authentication
  testAuth: (): Promise<any> => {
    return apiRequest<any>('/test/auth');
  },
};
