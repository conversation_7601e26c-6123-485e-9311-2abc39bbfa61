{"name": "bus-app-backend", "version": "1.0.0", "description": "", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.6.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase-admin": "^13.3.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.0", "morgan": "^1.10.0", "prisma": "^6.6.0", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}