const express = require('express');
const router = express.Router();
const driverController = require('../controllers/driverController');
const { authMiddleware } = require('../middleware/authMiddleware');
const { adminMiddleware } = require('../middleware/adminMiddleware');

/**
 * @route   GET /api/drivers
 * @desc    Get all drivers
 * @access  Private/Admin
 */
router.get('/', adminMiddleware, driverController.getAllDrivers);

/**
 * @route   GET /api/drivers/:id
 * @desc    Get driver by ID
 * @access  Private
 */
router.get('/:id', adminMiddleware, driverController.getDriverById);

/**
 * @route   GET /api/drivers/:id/bus-location
 * @desc    Get current bus location
 * @access  Private
 */
router.get('/:id/bus-location', adminMiddleware, driverController.getCurrentBusLocation);

/**
 * @route   POST /api/drivers
 * @desc    Create a new driver
 * @access  Private/Admin
 */
router.post('/', adminMiddleware, driverController.createDriver);

/**
 * @route   PUT /api/drivers/:id
 * @desc    Update a driver
 * @access  Private/Admin
 */
router.put('/:id', adminMiddleware, driverController.updateDriver);

/**
 * @route   PUT /api/drivers/:id/status
 * @desc    Update driver status
 * @access  Private
 */
router.put('/:id/status', authMiddleware, driverController.updateDriverStatus);

/**
 * @route   DELETE /api/drivers/:id
 * @desc    Delete a driver
 * @access  Private/Admin
 */
router.delete('/:id', adminMiddleware, driverController.deleteDriver);

module.exports = router;
