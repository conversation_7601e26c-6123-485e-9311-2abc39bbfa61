const express = require('express');
const router = express.Router();
const busController = require('../controllers/busController');
const { authMiddleware } = require('../middleware/authMiddleware');
const { adminMiddleware } = require('../middleware/adminMiddleware');

/**
 * @route   GET /api/buses
 * @desc    Get all buses
 * @access  Private
 */
router.get('/', authMiddleware, busController.getAllBuses);

/**
 * @route   GET /api/buses/:id
 * @desc    Get bus by ID
 * @access  Private
 */
router.get('/:id', busController.getBusById);

/**
 * @route   GET /api/buses/:id/location
 * @desc    Get bus current location
 * @access  Private
 */
router.get('/:id/location', busController.getBusLocation);

/**
 * @route   GET /api/buses/:id/schedule
 * @desc    Get bus schedule
 * @access  Public
 */
router.get('/:id/schedule', busController.getBusSchedule);

/**
 * @route   POST /api/buses
 * @desc    Create a new bus
 * @access  Private/Admin
 */
router.post('/', adminMiddleware, busController.createBus);

/**
 * @route   POST /api/buses/:id/location
 * @desc    Update bus location
 * @access  Private/Driver
 */
router.post('/:id/location', authMiddleware, busController.updateBusLocation);

/**
 * @route   POST /api/buses/:id/assign-driver
 * @desc    Assign driver to bus
 * @access  Private/Admin
 */
router.post('/:id/assign-driver', adminMiddleware, busController.assignDriver);

/**
 * @route   PUT /api/buses/:id
 * @desc    Update a bus
 * @access  Private/Admin
 */
router.put('/:id', adminMiddleware, busController.updateBus);

/**
 * @route   DELETE /api/buses/:id
 * @desc    Delete a bus
 * @access  Private/Admin
 */
router.delete('/:id', adminMiddleware, busController.deleteBus);

module.exports = router;
