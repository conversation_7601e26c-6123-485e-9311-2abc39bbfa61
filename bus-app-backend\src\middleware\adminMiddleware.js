const jwt = require('jsonwebtoken');
const { prisma } = require('../config/db');

/**
 * Protect admin routes - Middleware to verify JWT token and admin role
 */
const adminMiddleware = async (req, res, next) => {
  let token;

  // Check if token exists in headers
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret');

      // Get admin from the token
      const admin = await prisma.admin.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
        },
      });

      // If admin not found, try to find a user with admin role
      if (!admin) {
        const user = await prisma.user.findUnique({
          where: { id: decoded.id },
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        });

        if (user && user.role === 'admin') {
          req.user = user;
          return next();
        }
      }

      if (!admin || admin.role !== 'admin') {
        return res.status(403).json({ message: 'Not authorized as an admin' });
      }

      // Set admin in request
      req.user = admin;
      next();
    } catch (error) {
      console.error(error);
      res.status(401).json({ message: 'Not authorized, token failed' });
    }
  } else if (!token) {
    res.status(401).json({ message: 'Not authorized, no token' });
  }
};

module.exports = {
  adminMiddleware,
};
