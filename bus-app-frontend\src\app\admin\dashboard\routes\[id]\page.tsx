'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon, BusIcon, EditIcon, MapIcon, PlusIcon } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { RoutingMap } from '@/components/admin/map/RoutingMap';
import Link from 'next/link';
import { toast } from 'sonner';
import { Route, routeService } from '@/services/routeService';

export default function RouteDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { user } = useAuth();

  const [route, setRoute] = useState<Route | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRoute = async () => {
    try {
      setLoading(true);
      const data = await routeService.getRoute(params.id);
      setRoute(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching route:', err);
      setError('Failed to load route details. Please try again.');
      toast.error('Failed to load route details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoute();
  }, [params.id]);

  return (
    <ProtectedRoute allowedRoles={['admin']}>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center">
            <Button variant="ghost" size="icon" asChild className="mr-2">
              <Link href="/admin/dashboard/routes">
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Route Details</h1>
          </div>

          {route && (
            <Button asChild>
              <Link href={`/admin/dashboard/routes/${params.id}/edit`}>
                <EditIcon className="mr-2 h-4 w-4" />
                Edit Route
              </Link>
            </Button>
          )}
        </div>

        {error && (
          <div className="bg-destructive/15 text-destructive p-4 rounded-md mb-6">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        ) : route ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="bg-card rounded-lg shadow-sm p-6">
                <h2 className="text-2xl font-semibold mb-4">
                  {route.routeNumber ? `${route.routeNumber} - ` : ''}{route.routeName}
                </h2>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Start Point</h3>
                    <p>{route.startPoint}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">End Point</h3>
                    <p>{route.endPoint}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Distance</h3>
                    <p>{route.distanceKm} km</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Est. Time</h3>
                    <p>{route.estimatedTime} min</p>
                  </div>
                </div>

                {route.description && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                    <p>{route.description}</p>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: route.color || '#3b82f6' }}
                  ></div>
                  <span className="text-sm">{route.color || '#3b82f6'}</span>
                </div>
              </div>

              <div className="bg-card rounded-lg shadow-sm p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Stops</h2>
                  <Button size="sm" asChild>
                    <Link href={`/admin/dashboard/routes/${params.id}/stops/add`}>
                      <PlusIcon className="mr-2 h-4 w-4" />
                      Add Stop
                    </Link>
                  </Button>
                </div>

                {route.stops.length === 0 ? (
                  <p className="text-muted-foreground">No stops added to this route yet.</p>
                ) : (
                  <div className="space-y-4">
                    {route.stops.sort((a, b) => a.stopOrder - b.stopOrder).map((stop) => (
                      <div key={stop.id} className="flex items-center gap-4 p-3 border rounded-md">
                        <div className="bg-primary/10 text-primary font-medium rounded-full w-8 h-8 flex items-center justify-center">
                          {stop.stopOrder}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{stop.stopName}</h3>
                          {stop.stopCode && <p className="text-sm text-muted-foreground">Code: {stop.stopCode}</p>}
                        </div>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/dashboard/stops/${stop.id}`}>
                            View
                          </Link>
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="bg-card rounded-lg shadow-sm p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Buses</h2>
                  <Button size="sm" asChild>
                    <Link href={`/admin/dashboard/routes/${params.id}/assign-bus`}>
                      <PlusIcon className="mr-2 h-4 w-4" />
                      Assign Bus
                    </Link>
                  </Button>
                </div>

                {route.buses.length === 0 ? (
                  <p className="text-muted-foreground">No buses assigned to this route yet.</p>
                ) : (
                  <div className="space-y-4">
                    {route.buses.map((bus) => (
                      <div key={bus.id} className="flex items-center gap-4 p-3 border rounded-md">
                        <div className="bg-primary/10 text-primary rounded-full w-8 h-8 flex items-center justify-center">
                          <BusIcon className="h-4 w-4" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{bus.busNumber} - {bus.busName}</h3>
                          <p className="text-sm text-muted-foreground">Status: {bus.status}</p>
                        </div>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/dashboard/buses/${bus.id}`}>
                            View
                          </Link>
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-card rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold mb-4">Route Path</h2>

                {route.polyline ? (
                  <RoutingMap
                    initialPolyline={route.polyline}
                    onPolylineChange={() => {}}
                    readOnly={true}
                    center={[19.0760, 72.8777]} // Mumbai coordinates
                    zoom={12}
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center p-8 border-2 border-dashed rounded-md">
                    <MapIcon className="h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground text-center mb-4">No route path has been configured yet.</p>
                    <Button asChild>
                      <Link href={`/admin/dashboard/routes/${params.id}/edit`}>
                        Configure Route Path
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-card rounded-lg shadow-sm p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">Route Not Found</h2>
            <p className="text-muted-foreground mb-6">
              The requested route could not be found or you don't have permission to view it.
            </p>
            <Button asChild>
              <Link href="/admin/dashboard/routes">
                Back to Routes
              </Link>
            </Button>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
