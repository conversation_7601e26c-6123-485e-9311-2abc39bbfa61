# Bus Tracking WebSocket Testing Guide

This directory contains example scripts and tools to test the WebSocket-based real-time bus tracking system.

## Prerequisites

Before running the tests, make sure you have:

1. Node.js and npm installed
2. The bus-app-backend server running
3. MongoDB connected and working
4. Valid JWT tokens for authentication

## Installation

Install the required dependencies:

```bash
npm install
```

## Step 1: Create Test Users and Bus

First, create test users and a test bus for your testing:

```bash
npm run create-users
npm run create-bus
```

These scripts will:
1. Create a test driver user and passenger users
2. Log in existing users if they already exist
3. Create a test bus or find an existing one
4. Print tokens, bus IDs, and configuration for all test scripts

Copy the tokens and bus ID from the output and update them in the respective test scripts.

## Step 2: Test Authentication

Verify that your JWT token is valid:

```bash
npm run test-auth
```

This script will:
1. Test the public endpoint
2. Test the authenticated endpoint
3. Test the socket authentication endpoint

If any of these tests fail, run the create-users script again to get fresh tokens.

## Step 3: Test WebSocket Connection

Test the WebSocket connection:

```bash
npm run test-connection
```

This script will:
1. Connect to the main namespace
2. Connect to the bus-locations namespace
3. Subscribe to test events
4. Listen for bus location updates and passenger contributions

If the connection is successful, you should see:
- "Connected to main namespace"
- "Connected to bus-locations namespace"

## Step 4: Test Bus Movement

Simulate a bus moving and sending location updates:

```bash
npm run test-bus-movement
```

Before running this script, make sure to:
1. Update the `busId` in the configuration
2. Update the `token` with a valid driver token
3. Adjust the route points if needed

## Step 5: Test Passenger Contributions

Simulate passengers contributing their location data:

```bash
npm run test-passenger
```

Before running this script, make sure to:
1. Update the `passengerTokens` with valid passenger tokens
2. Adjust the `baseLocation` to match your test area

## Step 6: Test with the WebSocket Client

Open the WebSocket client in your browser:

```
examples/websocket-client.html
```

1. Enter your server URL (e.g., `http://localhost:5000`)
2. Enter a valid JWT token
3. Click "Connect"
4. Subscribe to buses, routes, or nearby buses
5. Watch the map and event log for updates

## Troubleshooting

### Connection Issues

If you're having trouble connecting:

1. Make sure your server is running
2. Verify that your JWT token is valid (use `npm run test-auth`)
3. Check the server logs for errors
4. Make sure the WebSocket port is not blocked by a firewall

### Authentication Issues

If you're getting authentication errors:

1. Generate a new token by logging in
2. Make sure the token is correctly formatted in the test scripts
3. Check that the JWT_SECRET in your .env file matches the one used to sign the token

### Missing Updates

If you're not receiving updates:

1. Make sure you're subscribed to the correct bus or route
2. Check that the test scripts are running and sending updates
3. Verify that the WebSocket connection is established
4. Look for errors in the server logs

## Advanced Testing

For more advanced testing scenarios, see the full testing guide in the documentation:

```
docs/testing-guide.md
```
