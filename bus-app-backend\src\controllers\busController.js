const { prisma } = require('../config/db');
const firebaseService = require('../services/firebaseService');
const locationUtils = require('../utils/locationUtils');
const { emitBusLocationUpdate } = require('../services/socketService');

/**
 * Get all buses
 * @route GET /api/buses
 * @access Public
 */
const getAllBuses = async (req, res) => {
  try {
    const { status, isActive, routeId } = req.query;

    // Build filter object
    const filter = {};
    if (status) filter.status = status;
    if (isActive !== undefined) filter.isActive = isActive === 'true';
    if (routeId) filter.routeId = routeId;

    const buses = await prisma.bus.findMany({
      where: filter,
      include: {
        route: {
          select: {
            id: true,
            routeName: true,
            routeNumber: true,
            startPoint: true,
            endPoint: true,
          },
        },
      },
      orderBy: {
        busNumber: 'asc',
      },
    });

    res.json(buses);
  } catch (error) {
    console.error('Get all buses error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get bus by ID
 * @route GET /api/buses/:id
 * @access Public
 */
const getBusById = async (req, res) => {
  try {
    const bus = await prisma.bus.findUnique({
      where: { id: req.params.id },
      include: {
        route: {
          select: {
            id: true,
            routeName: true,
            routeNumber: true,
            startPoint: true,
            endPoint: true,
          },
        },
        drivers: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
            licenseNo: true,
            status: true,
          },
        },
      },
    });

    if (!bus) {
      return res.status(404).json({ message: 'Bus not found' });
    }

    // Get the latest location
    const latestLocation = await prisma.busLocation.findFirst({
      where: { busId: bus.id },
      orderBy: { timestamp: 'desc' },
    });

    // Get current schedule
    const currentSchedule = await prisma.schedule.findFirst({
      where: {
        busId: bus.id,
        isActive: true,
      },
    });

    res.json({
      ...bus,
      currentLocation: latestLocation || null,
      currentSchedule: currentSchedule || null,
    });
  } catch (error) {
    console.error('Get bus by ID error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Create a new bus
 * @route POST /api/buses
 * @access Private/Admin
 */
const createBus = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const {
      busNumber,
      busName,
      totalSeats,
      currentSeatsAvailable,
      routeId,
      busType,
      features,
      fuelType,
      lastMaintenanceDate,
      status
    } = req.body;

    // Validate required fields
    if (!busNumber || !busName || !totalSeats || currentSeatsAvailable === undefined) {
      return res.status(400).json({
        message: 'Please provide busNumber, busName, totalSeats, and currentSeatsAvailable'
      });
    }

    // Check if bus already exists
    const busExists = await prisma.bus.findUnique({
      where: { busNumber },
    });

    if (busExists) {
      return res.status(400).json({ message: 'Bus with this number already exists' });
    }

    // If routeId is provided, check if route exists
    if (routeId) {
      const routeExists = await prisma.route.findUnique({
        where: { id: routeId },
      });

      if (!routeExists) {
        return res.status(400).json({ message: 'Route not found' });
      }
    }

    // Create bus
    const bus = await prisma.bus.create({
      data: {
        busNumber,
        busName,
        totalSeats: parseInt(totalSeats),
        currentSeatsAvailable: parseInt(currentSeatsAvailable),
        currentPassengerCount: 0,
        isCrowded: parseInt(currentSeatsAvailable) < parseInt(totalSeats) * 0.2, // 80% full is crowded
        isActive: true,
        status: status || 'operational',
        busType,
        features: features ? features.split(',').map(f => f.trim()) : [],
        fuelType,
        lastMaintenanceDate: lastMaintenanceDate ? new Date(lastMaintenanceDate) : null,
        routeId,
      },
    });

    res.status(201).json(bus);
  } catch (error) {
    console.error('Create bus error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update a bus
 * @route PUT /api/buses/:id
 * @access Private/Admin
 */
const updateBus = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const {
      busNumber,
      busName,
      totalSeats,
      currentSeatsAvailable,
      currentPassengerCount,
      isCrowded,
      isActive,
      status,
      busType,
      features,
      fuelType,
      lastMaintenanceDate,
      routeId
    } = req.body;

    // Check if bus exists
    const bus = await prisma.bus.findUnique({
      where: { id: req.params.id },
    });

    if (!bus) {
      return res.status(404).json({ message: 'Bus not found' });
    }

    // If busNumber is changed, check if it's unique
    if (busNumber && busNumber !== bus.busNumber) {
      const busExists = await prisma.bus.findUnique({
        where: { busNumber },
      });

      if (busExists) {
        return res.status(400).json({ message: 'Bus with this number already exists' });
      }
    }

    // If routeId is provided, check if route exists
    if (routeId && routeId !== bus.routeId) {
      const routeExists = await prisma.route.findUnique({
        where: { id: routeId },
      });

      if (!routeExists) {
        return res.status(400).json({ message: 'Route not found' });
      }
    }

    // Prepare update data
    const updateData = {};
    if (busNumber) updateData.busNumber = busNumber;
    if (busName) updateData.busName = busName;
    if (totalSeats !== undefined) updateData.totalSeats = parseInt(totalSeats);
    if (currentSeatsAvailable !== undefined) updateData.currentSeatsAvailable = parseInt(currentSeatsAvailable);
    if (currentPassengerCount !== undefined) updateData.currentPassengerCount = parseInt(currentPassengerCount);
    if (isCrowded !== undefined) updateData.isCrowded = isCrowded === true || isCrowded === 'true';
    if (isActive !== undefined) updateData.isActive = isActive === true || isActive === 'true';
    if (status) updateData.status = status;
    if (busType) updateData.busType = busType;
    if (features) updateData.features = features.split(',').map(f => f.trim());
    if (fuelType) updateData.fuelType = fuelType;
    if (lastMaintenanceDate) updateData.lastMaintenanceDate = new Date(lastMaintenanceDate);
    if (routeId) updateData.routeId = routeId;

    // Update bus
    const updatedBus = await prisma.bus.update({
      where: { id: req.params.id },
      data: updateData,
    });

    res.json(updatedBus);
  } catch (error) {
    console.error('Update bus error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Delete a bus
 * @route DELETE /api/buses/:id
 * @access Private/Admin
 */
const deleteBus = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    // Check if bus exists
    const bus = await prisma.bus.findUnique({
      where: { id: req.params.id },
    });

    if (!bus) {
      return res.status(404).json({ message: 'Bus not found' });
    }

    // Delete bus
    await prisma.bus.delete({
      where: { id: req.params.id },
    });

    res.json({ message: 'Bus removed successfully' });
  } catch (error) {
    console.error('Delete bus error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get bus current location
 * @route GET /api/buses/:id/location
 * @access Public
 */
const getBusLocation = async (req, res) => {
  try {
    // Check if bus exists
    const bus = await prisma.bus.findUnique({
      where: { id: req.params.id },
    });

    if (!bus) {
      return res.status(404).json({ message: 'Bus not found' });
    }

    // Get the latest location
    const location = await prisma.busLocation.findFirst({
      where: { busId: req.params.id },
      orderBy: { timestamp: 'desc' },
    });

    if (!location) {
      return res.status(404).json({ message: 'Location not found for this bus' });
    }

    res.json(location);
  } catch (error) {
    console.error('Get bus location error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update bus location
 * @route POST /api/buses/:id/location
 * @access Private/Driver
 */
const updateBusLocation = async (req, res) => {
  try {
    const { latitude, longitude, speed, heading, accuracy, altitude } = req.body;

    // Validate required fields
    if (!latitude || !longitude) {
      return res.status(400).json({ message: 'Latitude and longitude are required' });
    }

    // Check if bus exists
    const bus = await prisma.bus.findUnique({
      where: { id: req.params.id },
    });

    if (!bus) {
      return res.status(404).json({ message: 'Bus not found' });
    }

    // Check if user is a driver assigned to this bus
    if (req.user.role === 'driver') {
      const driver = await prisma.driver.findUnique({
        where: { userId: req.user.id },
      });

      if (!driver || driver.assignedBusId !== req.params.id) {
        return res.status(403).json({ message: 'You are not authorized to update this bus location' });
      }
    } else if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized' });
    }

    // Find nearest stop (optional)
    let nearestStopId = null;
    let distanceToStop = null;

    if (bus.routeId) {
      const stops = await prisma.stop.findMany({
        where: { routeId: bus.routeId },
      });

      if (stops.length > 0) {
        // Calculate distance to each stop
        const distances = stops.map(stop => {
          const distance = locationUtils.calculateDistance(
            parseFloat(latitude),
            parseFloat(longitude),
            stop.latitude,
            stop.longitude
          );
          return { stopId: stop.id, distance };
        });

        // Find the nearest stop
        const nearest = distances.reduce((min, current) =>
          current.distance < min.distance ? current : min, distances[0]);

        nearestStopId = nearest.stopId;
        distanceToStop = nearest.distance;
      }
    }

    // Mark any pending passenger contributions as processed since we have a driver update
    await prisma.passengerLocationContribution.updateMany({
      where: {
        busId: req.params.id,
        isProcessed: false
      },
      data: { isProcessed: true }
    });

    // Create location record
    const location = await prisma.busLocation.create({
      data: {
        busId: req.params.id,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        speed: speed ? parseFloat(speed) : null,
        heading: heading ? parseFloat(heading) : null,
        accuracy: accuracy ? parseFloat(accuracy) : null,
        altitude: altitude ? parseFloat(altitude) : null,
        nearestStopId,
        distanceToStop,
      },
    });

    // Emit location update via WebSocket
    await emitBusLocationUpdate(req.params.id, {
      id: location.id,
      latitude: location.latitude,
      longitude: location.longitude,
      speed: location.speed,
      heading: location.heading,
      accuracy: location.accuracy,
      altitude: location.altitude,
      timestamp: location.timestamp,
      nearestStopId: location.nearestStopId,
      distanceToStop: location.distanceToStop,
      source: 'driver', // Indicate this is from a driver, not passenger data
    });

    res.status(201).json(location);
  } catch (error) {
    console.error('Update bus location error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get bus schedule
 * @route GET /api/buses/:id/schedule
 * @access Public
 */
const getBusSchedule = async (req, res) => {
  try {
    // Check if bus exists
    const bus = await prisma.bus.findUnique({
      where: { id: req.params.id },
    });

    if (!bus) {
      return res.status(404).json({ message: 'Bus not found' });
    }

    // Get schedules
    const schedules = await prisma.schedule.findMany({
      where: { busId: req.params.id },
      orderBy: { startTime: 'asc' },
    });

    res.json(schedules);
  } catch (error) {
    console.error('Get bus schedule error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Assign driver to bus
 * @route POST /api/buses/:id/assign-driver
 * @access Private/Admin
 */
const assignDriver = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const { driverId } = req.body;

    // Validate required fields
    if (!driverId) {
      return res.status(400).json({ message: 'Driver ID is required' });
    }

    // Check if bus exists
    const bus = await prisma.bus.findUnique({
      where: { id: req.params.id },
    });

    if (!bus) {
      return res.status(404).json({ message: 'Bus not found' });
    }

    // Check if driver exists
    const driver = await prisma.driver.findUnique({
      where: { id: driverId },
    });

    if (!driver) {
      return res.status(404).json({ message: 'Driver not found' });
    }

    // Update driver
    const updatedDriver = await prisma.driver.update({
      where: { id: driverId },
      data: {
        assignedBusId: req.params.id,
        status: 'on_duty',
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            deviceToken: true,
          },
        },
      },
    });

    // Send notification to driver if they have a device token
    if (updatedDriver.user.deviceToken) {
      // Check notification preferences
      const notificationPreference = await prisma.notificationPreference.findUnique({
        where: { userId: updatedDriver.user.id },
      });

      // Send notification if preferences allow
      if (!notificationPreference ||
          (notificationPreference.enabled && notificationPreference.serviceUpdates)) {
        await firebaseService.sendNotificationToUser(
          updatedDriver.user.id,
          {
            title: 'Bus Assignment',
            body: `You have been assigned to bus ${bus.busNumber} (${bus.busName}).`
          },
          {
            type: 'assignment',
            busId: bus.id,
            busNumber: bus.busNumber,
            busName: bus.busName
          }
        );
      }
    }

    // Remove deviceToken from response
    const { deviceToken, ...userWithoutToken } = updatedDriver.user;
    const driverResponse = {
      ...updatedDriver,
      user: userWithoutToken
    };

    res.json(driverResponse);
  } catch (error) {
    console.error('Assign driver error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};



module.exports = {
  getAllBuses,
  getBusById,
  createBus,
  updateBus,
  deleteBus,
  getBusLocation,
  updateBusLocation,
  getBusSchedule,
  assignDriver,
};
