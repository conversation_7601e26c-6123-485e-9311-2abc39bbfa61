'use client';

import { useEffect, useRef, useState } from 'react';
import { BusIcon, MapPinIcon } from 'lucide-react';

// Define the Bus type
interface Bus {
  id: string;
  busNumber?: string;
  busName?: string;
  status?: string;
  currentLocation?: {
    latitude: number;
    longitude: number;
    heading?: number;
    speed?: number;
    timestamp?: string;
    isPassengerData?: boolean;
  };
  route?: {
    routeName: string;
    routeNumber: string;
  };
  currentPassengerCount?: number;
  totalSeats?: number;
  currentSeatsAvailable?: number;
  isCrowded?: boolean;
  isActive?: boolean;
}

interface BusMapProps {
  buses: Bus[];
  selectedBusId: string | null;
  userLocation: { latitude: number; longitude: number } | null;
  onBusSelect: (busId: string) => void;
}

export function BusMap({ buses, selectedBusId, userLocation, onBusSelect }: BusMapProps) {
  // Extend HTMLDivElement to include Leaflet properties
  interface MapDiv extends HTMLDivElement {
    _leaflet_id?: number;
  }

  const mapRef = useRef<MapDiv>(null);
  const [mapInstance, setMapInstance] = useState<any>(null);
  const markersRef = useRef<{ [key: string]: any }>({});
  const userMarkerRef = useRef<any>(null);
  const [leaflet, setLeaflet] = useState<any>(null);

  // Initialize the map
  useEffect(() => {
    // Store map instance in a ref to access it in cleanup
    let map: any = null;

    // Dynamic import of Leaflet (client-side only)
    const initializeMap = async () => {
      if (typeof window === 'undefined' || !mapRef.current) return;

      try {
        // Import Leaflet dynamically
        const L = await import('leaflet');

        // Import Leaflet CSS
        await import('leaflet/dist/leaflet.css');

        // Set Leaflet instance
        setLeaflet(L);

        // Make sure the map container doesn't already have a map
        if (mapRef.current._leaflet_id) {
          console.log('Map container already has a map instance, cleaning up');
          return;
        }

        // Create map instance with a unique ID
        map = L.map(mapRef.current, {
          attributionControl: false,
          zoomControl: true
        }).setView([37.7749, -122.4194], 12);

        // Add OpenStreetMap tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19
        }).addTo(map);

        // Set map instance in state
        setMapInstance(map);
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    };

    // Initialize the map
    initializeMap();

    // Cleanup function
    return () => {
      // Clean up all markers
      Object.values(markersRef.current).forEach(marker => {
        if (marker) marker.remove();
      });

      // Clean up user marker
      if (userMarkerRef.current) {
        userMarkerRef.current.remove();
      }

      // Clean up map instance
      if (map) {
        console.log('Removing map instance');
        map.remove();
      }

      // Reset state and refs
      markersRef.current = {};
      userMarkerRef.current = null;
      setMapInstance(null);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update user location marker
  useEffect(() => {
    if (!mapInstance || !leaflet || !userLocation) return;

    // Remove existing user marker
    if (userMarkerRef.current) {
      userMarkerRef.current.remove();
    }

    // Create user marker
    const newUserMarker = leaflet.marker(
      [userLocation.latitude, userLocation.longitude],
      {
        icon: leaflet.divIcon({
          className: 'user-location-marker',
          html: `<div class="flex items-center justify-center w-8 h-8 bg-blue-500 rounded-full border-2 border-white">
                  <span class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="1"/></svg></span>
                </div>`,
          iconSize: [32, 32],
          iconAnchor: [16, 16]
        })
      }
    ).addTo(mapInstance);

    newUserMarker.bindPopup('Your Location');
    userMarkerRef.current = newUserMarker;

    // Center map on user location
    mapInstance.setView([userLocation.latitude, userLocation.longitude], 14);

    // Cleanup function
    return () => {
      if (newUserMarker) {
        newUserMarker.remove();
      }
    };
  }, [mapInstance, leaflet, userLocation]);

  // Update bus markers
  useEffect(() => {
    if (!mapInstance || !leaflet) return;

    // Use the ref for markers instead of state
    const currentMarkers = markersRef.current;
    const markersToRemove: string[] = [];

    // Add or update markers for each bus
    buses.forEach(bus => {
      if (!bus.currentLocation) return;

      const { latitude, longitude } = bus.currentLocation;

      // If marker already exists, update its position
      if (currentMarkers[bus.id]) {
        currentMarkers[bus.id].setLatLng([latitude, longitude]);

        // Update the icon to reflect selection state
        const busIcon = leaflet.divIcon({
          className: `bus-marker ${selectedBusId === bus.id ? 'selected' : ''}`,
          html: `<div class="flex items-center justify-center w-8 h-8 ${
            bus.status?.toLowerCase() === 'operational' ? 'bg-green-500' : 'bg-yellow-500'
          } rounded-full border-2 border-white ${
            selectedBusId === bus.id ? 'ring-2 ring-blue-500' : ''
          }">
                  <span class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 6v6"/><path d="M15 6v6"/><path d="M2 12h19.6"/><path d="M18 18h3"/><path d="M2 7h18a4 4 0 0 1 4 4v0a4 4 0 0 1-4 4h-2"/><path d="M4 7v7a4 4 0 0 0 4 4h8"/><circle cx="7" cy="18" r="2"/><circle cx="15" cy="18" r="2"/></svg></span>
                </div>`,
          iconSize: [32, 32],
          iconAnchor: [16, 16]
        });

        currentMarkers[bus.id].setIcon(busIcon);
      } else {
        // Create new marker
        const busIcon = leaflet.divIcon({
          className: `bus-marker ${selectedBusId === bus.id ? 'selected' : ''}`,
          html: `<div class="flex items-center justify-center w-8 h-8 ${
            bus.status?.toLowerCase() === 'operational' ? 'bg-green-500' : 'bg-yellow-500'
          } rounded-full border-2 border-white ${
            selectedBusId === bus.id ? 'ring-2 ring-blue-500' : ''
          }">
                  <span class="text-white"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 6v6"/><path d="M15 6v6"/><path d="M2 12h19.6"/><path d="M18 18h3"/><path d="M2 7h18a4 4 0 0 1 4 4v0a4 4 0 0 1-4 4h-2"/><path d="M4 7v7a4 4 0 0 0 4 4h8"/><circle cx="7" cy="18" r="2"/><circle cx="15" cy="18" r="2"/></svg></span>
                </div>`,
          iconSize: [32, 32],
          iconAnchor: [16, 16]
        });

        const marker = leaflet.marker([latitude, longitude], { icon: busIcon })
          .addTo(mapInstance)
          .on('click', () => onBusSelect(bus.id));

        // Add popup with bus information
        const popupContent = `
          <div class="bus-popup">
            <h3 class="font-bold">${bus.busNumber || 'Unknown'} - ${bus.busName || 'No name'}</h3>
            <p>Status: ${bus.status || 'Unknown'}</p>
            ${bus.route ? `<p>Route: ${bus.route.routeNumber} - ${bus.route.routeName}</p>` : '<p>Route: Not assigned</p>'}
          </div>
        `;

        marker.bindPopup(popupContent);
        currentMarkers[bus.id] = marker;
      }

      // Open popup if selected
      if (selectedBusId === bus.id) {
        currentMarkers[bus.id].openPopup();
        mapInstance.setView([latitude, longitude], 15);
      }
    });

    // Identify markers for buses that no longer exist
    Object.keys(currentMarkers).forEach(busId => {
      if (!buses.find(bus => bus.id === busId)) {
        markersToRemove.push(busId);
      }
    });

    // Remove markers that are no longer needed
    markersToRemove.forEach(busId => {
      if (currentMarkers[busId]) {
        currentMarkers[busId].remove();
        delete currentMarkers[busId];
      }
    });

    // No need to call setMarkers since we're using a ref

    // Cleanup function
    return () => {
      // This cleanup will run when the component unmounts or when dependencies change
      Object.values(currentMarkers).forEach(marker => {
        if (marker) marker.remove();
      });
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [buses, mapInstance, leaflet, selectedBusId, onBusSelect]);

  return (
    <div ref={mapRef} className="h-full w-full" />
  );
}
