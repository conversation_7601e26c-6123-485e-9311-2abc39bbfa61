'use client';

import { LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Link from 'next/link';

export default function AdminDashboardPage() {
  const { user, logout } = useAuth();

  return (
    <ProtectedRoute allowedRoles={['admin']}>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <Button variant="outline" onClick={logout}>
            <LogOut className="mr-2 h-4 w-4" />
            Logout
          </Button>
        </div>

        <div className="bg-card rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Welcome, {user?.name || 'Admin'}!</h2>
          <p className="text-muted-foreground">
            This is your admin dashboard where you can manage users, buses, routes, and more.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-card rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-2">Manage Users</h3>
            <p className="text-muted-foreground mb-4">
              View and manage user accounts.
            </p>
            <Button className="w-full">
              User Management
            </Button>
          </div>

          <div className="bg-card rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-2">Manage Buses</h3>
            <p className="text-muted-foreground mb-4">
              Add, edit, or remove buses from the system.
            </p>
            <Button className="w-full">
              Bus Management
            </Button>
          </div>

          <div className="bg-card rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-2">Manage Routes</h3>
            <p className="text-muted-foreground mb-4">
              Create and edit bus routes and schedules.
            </p>
            <Button className="w-full" asChild>
              <Link href="/admin/dashboard/routes">
                Route Management
              </Link>
            </Button>
          </div>

          <div className="bg-card rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-2">Manage Drivers</h3>
            <p className="text-muted-foreground mb-4">
              Assign drivers to buses and manage driver accounts.
            </p>
            <Button className="w-full">
              Driver Management
            </Button>
          </div>

          <div className="bg-card rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-2">Analytics</h3>
            <p className="text-muted-foreground mb-4">
              View system analytics and reports.
            </p>
            <Button className="w-full">
              View Analytics
            </Button>
          </div>

          <div className="bg-card rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-2">System Settings</h3>
            <p className="text-muted-foreground mb-4">
              Configure system settings and preferences.
            </p>
            <Button className="w-full">
              Settings
            </Button>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
