const express = require('express');
const router = express.Router();
const { authMiddleware } = require('../middleware/authMiddleware');
const { prisma } = require('../config/db');
const locationUtils = require('../utils/locationUtils');
const { emitBusLocationUpdate, emitDebugMessage } = require('../services/socketService');

/**
 * @route   GET /api/test/auth
 * @desc    Test authentication
 * @access  Private
 */
router.get('/auth', authMiddleware, (req, res) => {
  res.json({
    message: 'Authentication successful',
    user: {
      id: req.user.id,
      name: req.user.name,
      email: req.user.email,
      role: req.user.role
    }
  });
});

/**
 * @route   GET /api/test/public
 * @desc    Test public endpoint
 * @access  Public
 */
router.get('/public', (req, res) => {
  res.json({
    message: 'Public endpoint working'
  });
});

/**
 * @route   GET /api/test/ping
 * @desc    Simple ping endpoint to test API connectivity
 * @access  Public
 */
router.get('/ping', (req, res) => {
  res.json({
    status: 'success',
    message: 'API is running',
    timestamp: new Date(),
    version: '1.0.0'
  });
});

/**
 * @route   POST /api/test/bus-location/:id
 * @desc    Test updating bus location (no authorization checks)
 * @access  Private
 */
router.post('/bus-location/:id', authMiddleware, async (req, res) => {
  try {
    const { latitude, longitude, speed, heading, accuracy, altitude } = req.body;

    // Validate required fields
    if (!latitude || !longitude) {
      return res.status(400).json({ message: 'Latitude and longitude are required' });
    }

    // Check if bus exists
    const bus = await prisma.bus.findUnique({
      where: { id: req.params.id },
    });

    if (!bus) {
      return res.status(404).json({ message: 'Bus not found' });
    }

    // Find nearest stop (optional)
    let nearestStopId = null;
    let distanceToStop = null;

    if (bus.routeId) {
      const stops = await prisma.stop.findMany({
        where: { routeId: bus.routeId },
      });

      if (stops.length > 0) {
        // Calculate distance to each stop
        const distances = stops.map(stop => {
          const distance = locationUtils.calculateDistance(
            parseFloat(latitude),
            parseFloat(longitude),
            stop.latitude,
            stop.longitude
          );
          return { stopId: stop.id, distance };
        });

        // Find the nearest stop
        const nearest = distances.reduce((min, current) =>
          current.distance < min.distance ? current : min, distances[0]);

        nearestStopId = nearest.stopId;
        distanceToStop = nearest.distance;
      }
    }

    // Create location record
    const location = await prisma.busLocation.create({
      data: {
        busId: req.params.id,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        speed: speed ? parseFloat(speed) : null,
        heading: heading ? parseFloat(heading) : null,
        accuracy: accuracy ? parseFloat(accuracy) : null,
        altitude: altitude ? parseFloat(altitude) : null,
        nearestStopId,
        distanceToStop,
      },
    });

    // Emit location update via WebSocket
    await emitBusLocationUpdate(req.params.id, {
      id: location.id,
      latitude: location.latitude,
      longitude: location.longitude,
      speed: location.speed,
      heading: location.heading,
      accuracy: location.accuracy,
      altitude: location.altitude,
      timestamp: location.timestamp,
      nearestStopId: location.nearestStopId,
      distanceToStop: location.distanceToStop,
      source: 'test', // Indicate this is from a test, not driver data
    });

    res.status(201).json({
      message: 'Test bus location updated successfully',
      location
    });
  } catch (error) {
    console.error('Test update bus location error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

/**
 * @route   GET /api/test/debug
 * @desc    Send a debug message to all connected clients
 * @access  Private
 */
router.get('/debug', authMiddleware, (req, res) => {
  try {
    const message = req.query.message || 'Test debug message';
    const busId = req.query.busId;

    // Create test data
    const testData = {
      timestamp: new Date(),
      busId: busId || 'test-bus',
      message
    };

    // Emit debug message
    emitDebugMessage(message, testData);

    // If busId is provided, also emit a fake bus location update
    if (busId) {
      const locationData = {
        id: 'test-' + Date.now(),
        busId,
        latitude: 37.7749 + (Math.random() - 0.5) * 0.01,
        longitude: -122.4194 + (Math.random() - 0.5) * 0.01,
        speed: Math.random() * 50,
        heading: Math.random() * 360,
        accuracy: 10,
        timestamp: new Date(),
        source: 'test'
      };

      emitBusLocationUpdate(busId, locationData);
    }

    res.json({
      success: true,
      message: `Debug message sent: ${message}`,
      data: testData
    });
  } catch (error) {
    console.error('Debug message error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

module.exports = router;
