# Bus App Backend

A Node.js Express backend for a bus tracking application with real-time location updates using WebSockets.

## Features

- User authentication and authorization
- Bus, route, and stop management
- Real-time bus location tracking using WebSockets
- Passenger location contributions for improved tracking
- Nearby buses and stops search
- Bus arrival estimates

## Technology Stack

- **Node.js** - JavaScript runtime
- **Express** - Web framework
- **Socket.IO** - WebSocket implementation
- **MongoDB** - Database
- **Prisma** - ORM
- **JWT** - Authentication

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- MongoDB

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/bus-app-backend.git
cd bus-app-backend
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory with the following variables:
```
PORT=5000
NODE_ENV=development
DATABASE_URL=mongodb+srv://username:<EMAIL>/bus-app?retryWrites=true&w=majority
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=30d
CLIENT_URL=http://localhost:3000
ADMIN_CREATION_DEVICE_ID=your_device_id
```

4. Start the server:
```bash
npm run dev
```

## WebSocket Implementation

The application uses Socket.IO to provide real-time bus location updates. Key features include:

- Real-time bus location tracking
- Passenger location contributions
- Room-based subscriptions for specific buses or routes
- Authentication using JWT tokens

For detailed documentation on the WebSocket API, see [docs/websocket-api.md](docs/websocket-api.md).

## Testing

To test the WebSocket implementation, see [docs/testing-guide.md](docs/testing-guide.md).

Example test scripts and a WebSocket client are provided in the `examples` directory:

```bash
cd examples
npm install
npm run test-connection
```

## API Documentation

### Authentication

- `POST /api/users/register` - Register a new user
- `POST /api/users/login` - Login and get JWT token
- `GET /api/users/profile` - Get user profile

### Buses

- `GET /api/buses` - Get all buses
- `GET /api/buses/:id` - Get bus by ID
- `GET /api/buses/:id/location` - Get bus current location
- `POST /api/buses/:id/location` - Update bus location
- `GET /api/buses/:id/schedule` - Get bus schedule

### Routes

- `GET /api/routes` - Get all routes
- `GET /api/routes/:id` - Get route by ID
- `GET /api/routes/:id/stops` - Get all stops for a route

### Stops

- `GET /api/stops` - Get all stops
- `GET /api/stops/:id` - Get stop by ID
- `GET /api/stops/:id/arrivals` - Get estimated arrivals for a stop

### Locations

- `POST /api/locations/user` - Update user location
- `GET /api/locations/nearby-buses` - Get nearby buses
- `GET /api/locations/nearby-stops` - Get nearby stops

### WebSocket

- `GET /api/socket/auth` - Authenticate for WebSocket connection
- `GET /api/socket/status` - Get WebSocket server status

## License

This project is licensed under the MIT License - see the LICENSE file for details.
