
# socket.io-client

[![Build Status](https://github.com/socketio/socket.io-client/workflows/CI/badge.svg?branch=main)](https://github.com/socketio/socket.io-client/actions)
[![NPM version](https://badge.fury.io/js/socket.io-client.svg)](https://www.npmjs.com/package/socket.io-client)
![Downloads](http://img.shields.io/npm/dm/socket.io-client.svg?style=flat)
[![](http://slack.socket.io/badge.svg?)](http://slack.socket.io)

[![Sauce Test Status](https://saucelabs.com/browser-matrix/socket.svg)](https://saucelabs.com/u/socket)

## Documentation

Please see the documentation [here](https://socket.io/docs/v4/client-initialization/).

The source code of the website can be found [here](https://github.com/socketio/socket.io-website). Contributions are welcome!

## Debug / logging

In order to see all the client debug output, run the following command on the browser console – including the desired scope – and reload your app page:

```
localStorage.debug = '*';
```

And then, filter by the scopes you're interested in. See also: https://socket.io/docs/v4/logging-and-debugging/

## License

[MIT](/LICENSE)
