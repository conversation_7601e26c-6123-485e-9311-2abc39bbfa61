// src/contexts/AuthContext.tsx
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import { 
  UserData, 
  LoginCredentials, 
  RegisterCredentials,
  setAuthToken,
  setUserData,
  getUserData,
  getAuthToken,
  isAuthenticated,
  logout as logoutUtil
} from '@/lib/auth';
import { authApi } from '@/lib/api';

// Context type
interface AuthContextType {
  user: Omit<UserData, 'token'> | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials, isAdmin?: boolean) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => void;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<Omit<UserData, 'token'> | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const router = useRouter();

  // Check authentication status on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const userData = getUserData();
        const token = getAuthToken();
        
        if (userData && token && isAuthenticated()) {
          setUser(userData);
        }
      } catch (error) {
        console.error('Auth check error:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkAuth();
  }, []);

  // Login function
  const login = async (credentials: LoginCredentials, isAdmin = false) => {
    setIsLoading(true);
    
    try {
      const userData = isAdmin 
        ? await authApi.loginAdmin(credentials)
        : await authApi.loginUser(credentials);
      
      // Save token and user data
      setAuthToken(userData.token);
      
      // Save user data without token
      const { token, ...userDataWithoutToken } = userData;
      setUserData(userDataWithoutToken);
      setUser(userDataWithoutToken);
      
      // Show success message
      toast.success(`Welcome back, ${userData.name || userData.email}!`);
      
      // Redirect based on role
      if (userData.role === 'admin') {
        router.push('/admin/dashboard');
      } else {
        router.push('/dashboard');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      toast.error(error.message || 'Login failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (credentials: RegisterCredentials) => {
    setIsLoading(true);
    
    try {
      const userData = await authApi.registerUser(credentials);
      
      // Save token and user data
      setAuthToken(userData.token);
      
      // Save user data without token
      const { token, ...userDataWithoutToken } = userData;
      setUserData(userDataWithoutToken);
      setUser(userDataWithoutToken);
      
      // Show success message
      toast.success('Registration successful! Welcome aboard.');
      
      // Redirect to dashboard
      router.push('/dashboard');
    } catch (error: any) {
      console.error('Registration error:', error);
      toast.error(error.message || 'Registration failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    logoutUtil();
    setUser(null);
    toast.info('You have been logged out.');
    router.push('/');
  };

  // Context value
  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}
