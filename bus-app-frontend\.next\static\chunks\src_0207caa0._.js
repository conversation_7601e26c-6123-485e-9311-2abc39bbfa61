(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/label.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
function Label({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "label",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/label.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = Label;
;
var _c;
__turbopack_context__.k.register(_c, "Label");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
function Textarea({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        "data-slot": "textarea",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/textarea.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Textarea;
;
var _c;
__turbopack_context__.k.register(_c, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/auth/ProtectedRoute.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/auth/ProtectedRoute.tsx
__turbopack_context__.s({
    "default": (()=>ProtectedRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function ProtectedRoute({ children, allowedRoles }) {
    _s();
    const { user, isLoading, isAuthenticated } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProtectedRoute.useEffect": ()=>{
            // If not loading and not authenticated, redirect to login
            if (!isLoading && !isAuthenticated) {
                router.push('/login');
                return;
            }
            // If authenticated but role is not allowed, redirect to appropriate page
            if (!isLoading && isAuthenticated && user && allowedRoles && !allowedRoles.includes(user.role)) {
                if (user.role === 'admin') {
                    router.push('/admin/dashboard');
                } else {
                    router.push('/dashboard');
                }
            }
        }
    }["ProtectedRoute.useEffect"], [
        isLoading,
        isAuthenticated,
        user,
        router,
        allowedRoles
    ]);
    // Show nothing while loading
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: "Loading..."
        }, void 0, false, {
            fileName: "[project]/src/components/auth/ProtectedRoute.tsx",
            lineNumber: 45,
            columnNumber: 12
        }, this);
    }
    // If not authenticated, don't render children
    if (!isAuthenticated) {
        return null;
    }
    // If role check is required and user doesn't have the required role, don't render children
    if (allowedRoles && user && !allowedRoles.includes(user.role)) {
        return null;
    }
    // Otherwise, render children
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(ProtectedRoute, "kxF4ulbmLJTZQS84anwmFclQxXE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = ProtectedRoute;
var _c;
__turbopack_context__.k.register(_c, "ProtectedRoute");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/admin/map/RoutingMap.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "RoutingMap": (()=>RoutingMap)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/map.js [app-client] (ecmascript) <export default as MapIcon>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
// Polyline encoding/decoding utility
const PolylineUtil = {
    encode: function(points, precision) {
        if (!points.length) return "";
        const factor = Math.pow(10, precision !== undefined ? precision : 5);
        let output = this.encodePoint(points[0].lat || points[0][0], points[0].lng || points[0][1], factor);
        for(let i = 1; i < points.length; i++){
            const a = points[i - 1];
            const b = points[i];
            output += this.encodePoint(b.lat || b[0], b.lng || b[1], factor, a.lat || a[0], a.lng || a[1]);
        }
        return output;
    },
    encodePoint: function(lat, lng, factor, prevLat, prevLng) {
        // Round to the nearest factor
        lat = Math.round(lat * factor);
        lng = Math.round(lng * factor);
        // Delta encode
        const deltaLat = lat - (prevLat !== undefined ? Math.round(prevLat * factor) : 0);
        const deltaLng = lng - (prevLng !== undefined ? Math.round(prevLng * factor) : 0);
        return this.encodeSignedNumber(deltaLat) + this.encodeSignedNumber(deltaLng);
    },
    encodeSignedNumber: function(num) {
        let sgn_num = num << 1;
        if (num < 0) {
            sgn_num = ~sgn_num;
        }
        return this.encodeNumber(sgn_num);
    },
    encodeNumber: (num)=>{
        let encodeString = "";
        while(num >= 0x20){
            encodeString += String.fromCharCode((0x20 | num & 0x1f) + 63);
            num >>= 5;
        }
        encodeString += String.fromCharCode(num + 63);
        return encodeString;
    },
    decode: (encoded, precision)=>{
        if (!encoded.length) return [];
        const factor = Math.pow(10, precision !== undefined ? precision : 5);
        const len = encoded.length;
        let index = 0;
        let lat = 0;
        let lng = 0;
        const points = [];
        while(index < len){
            let b;
            let shift = 0;
            let result = 0;
            do {
                b = encoded.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            }while (b >= 0x20)
            const deltaLat = result & 1 ? ~(result >> 1) : result >> 1;
            lat += deltaLat;
            shift = 0;
            result = 0;
            do {
                b = encoded.charCodeAt(index++) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            }while (b >= 0x20)
            const deltaLng = result & 1 ? ~(result >> 1) : result >> 1;
            lng += deltaLng;
            points.push([
                lat / factor,
                lng / factor
            ]);
        }
        return points;
    }
};
function RoutingMap({ initialPolyline, onPolylineChange, center = [
    19.076,
    72.8777
], zoom = 12, readOnly = false }) {
    _s();
    const mapRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mapInstanceRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const routingControlRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mapContainerKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(Math.random().toString(36).substring(2, 11));
    // Track if component is mounted
    const isMounted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    const [mapReady, setMapReady] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [routingStatus, setRoutingStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('normal');
    // Effect to check if map container is ready
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RoutingMap.useEffect": ()=>{
            if ("object" === "undefined" || !mapRef.current) return;
            // Check if map container has dimensions
            const checkMapContainer = {
                "RoutingMap.useEffect.checkMapContainer": ()=>{
                    if (mapRef.current && mapRef.current.clientWidth > 0 && mapRef.current.clientHeight > 0) {
                        setMapReady(true);
                    } else {
                        // Try again in a moment
                        setTimeout(checkMapContainer, 100);
                    }
                }
            }["RoutingMap.useEffect.checkMapContainer"];
            checkMapContainer();
            return ({
                "RoutingMap.useEffect": ()=>{
                    setMapReady(false);
                }
            })["RoutingMap.useEffect"];
        }
    }["RoutingMap.useEffect"], []);
    // Effect to handle component mount/unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RoutingMap.useEffect": ()=>{
            // Set mounted flag
            isMounted.current = true;
            return ({
                "RoutingMap.useEffect": ()=>{
                    // Set unmounted flag
                    isMounted.current = false;
                    // Clean up map resources
                    if (mapInstanceRef.current) {
                        try {
                            if (routingControlRef.current) {
                                mapInstanceRef.current.removeControl(routingControlRef.current);
                                routingControlRef.current = null;
                            }
                            mapInstanceRef.current.remove();
                            mapInstanceRef.current = null;
                        } catch (e) {
                            console.warn("Error cleaning up map on unmount:", e);
                        }
                    }
                }
            })["RoutingMap.useEffect"];
        }
    }["RoutingMap.useEffect"], []);
    // Initialize the map
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RoutingMap.useEffect": ()=>{
            if ("object" === "undefined" || !mapRef.current || !mapReady) return;
            let map = null;
            let routing = null;
            // Dynamically import Leaflet
            const initMap = {
                "RoutingMap.useEffect.initMap": async ()=>{
                    try {
                        console.log("Initializing Leaflet map with routing...");
                        // Check if map is already initialized and clean it up first
                        if (mapInstanceRef.current) {
                            console.log("Map already exists, cleaning up first...");
                            try {
                                if (routingControlRef.current) {
                                    mapInstanceRef.current.removeControl(routingControlRef.current);
                                    routingControlRef.current = null;
                                }
                                mapInstanceRef.current.remove();
                                mapInstanceRef.current = null;
                            } catch (e) {
                                console.warn("Error cleaning up existing map:", e);
                            }
                        }
                        // Clear any previous content
                        if (mapRef.current) {
                            mapRef.current.innerHTML = "";
                            // Remove any Leaflet-specific attributes
                            if (mapRef.current._leaflet_id) {
                                delete mapRef.current._leaflet_id;
                            }
                        }
                        // Import libraries
                        const L = (await __turbopack_context__.r("[project]/node_modules/leaflet/dist/leaflet-src.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i)).default;
                        console.log("Leaflet loaded successfully");
                        // Handle CSS imports
                        try {
                            // @ts-ignore - CSS imports
                            await __turbopack_context__.r("[project]/node_modules/leaflet/dist/leaflet.css [app-client] (css, async loader)")(__turbopack_context__.i);
                            // @ts-ignore - CSS imports
                            await __turbopack_context__.r("[project]/node_modules/leaflet-routing-machine/dist/leaflet-routing-machine.css [app-client] (css, async loader)")(__turbopack_context__.i);
                            console.log("Leaflet CSS loaded");
                            // Fix Leaflet's default icon paths
                            delete L.Icon.Default.prototype._getIconUrl;
                            L.Icon.Default.mergeOptions({
                                iconRetinaUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png",
                                iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
                                shadowUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png"
                            });
                        } catch (e) {
                            console.error("CSS imports failed:", e);
                            if (mapRef.current) {
                                mapRef.current.innerHTML = '<div style="padding: 20px; color: red;">Error loading map styles. Please check console.</div>';
                            }
                            return;
                        }
                        // Make sure the map container is ready and has dimensions
                        if (!mapRef.current || !mapRef.current.clientWidth || !mapRef.current.clientHeight) {
                            console.error("Map container not ready or has zero dimensions");
                            return;
                        }
                        // Create map with additional safeguards
                        map = L.map(mapRef.current, {
                            center: center,
                            zoom: zoom,
                            zoomControl: true,
                            attributionControl: true,
                            fadeAnimation: false,
                            markerZoomAnimation: false,
                            preferCanvas: true
                        });
                        // Store map instance in ref
                        mapInstanceRef.current = map;
                        console.log("Map created successfully");
                        // Force a resize event after a short delay to ensure the map renders correctly
                        setTimeout({
                            "RoutingMap.useEffect.initMap": ()=>{
                                if (isMounted.current && map) {
                                    map.invalidateSize();
                                    console.log("Map size invalidated");
                                }
                            }
                        }["RoutingMap.useEffect.initMap"], 300);
                        // Add tile layer
                        L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                            maxZoom: 19
                        }).addTo(map);
                        // Import Leaflet Routing Machine
                        try {
                            await __turbopack_context__.r("[project]/node_modules/leaflet-routing-machine/dist/leaflet-routing-machine.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                            console.log("Leaflet Routing Machine loaded");
                        } catch (error) {
                            console.error("Error loading Leaflet Routing Machine:", error);
                            if (mapRef.current) {
                                mapRef.current.innerHTML = '<div style="padding: 20px; color: red;">Error loading routing engine. Please check console.</div>';
                            }
                            return;
                        }
                        // Initialize waypoints
                        let initialWaypoints = [];
                        // If initial polyline is provided, decode it to waypoints
                        if (initialPolyline && initialPolyline.length > 0) {
                            try {
                                const decodedPath = PolylineUtil.decode(initialPolyline);
                                // Convert to waypoints (use first, last, and some points in between)
                                if (decodedPath.length > 0) {
                                    // Always include first and last points
                                    initialWaypoints.push(L.latLng(decodedPath[0][0], decodedPath[0][1]));
                                    // Add some intermediate points if there are many
                                    if (decodedPath.length > 10) {
                                        const step = Math.floor(decodedPath.length / 5);
                                        for(let i = step; i < decodedPath.length - step; i += step){
                                            initialWaypoints.push(L.latLng(decodedPath[i][0], decodedPath[i][1]));
                                        }
                                    }
                                    // Add last point
                                    if (decodedPath.length > 1) {
                                        initialWaypoints.push(L.latLng(decodedPath[decodedPath.length - 1][0], decodedPath[decodedPath.length - 1][1]));
                                    }
                                }
                            } catch (error) {
                                console.error("Error decoding polyline:", error);
                                // If decoding fails, start with empty waypoints
                                initialWaypoints = [];
                            }
                        }
                        // If no waypoints from polyline, start with empty or default waypoints
                        if (initialWaypoints.length === 0 && !readOnly) {
                            // For new routes, start with two points in Mumbai
                            initialWaypoints = [
                                L.latLng(19.076, 72.8777),
                                L.latLng(19.1136, 72.9023)
                            ];
                        }
                        // Create a fallback router
                        const fallbackRouter = createFallbackRouter();
                        // Ensure waypoints have valid coordinates
                        initialWaypoints = initialWaypoints.filter({
                            "RoutingMap.useEffect.initMap": (wp)=>{
                                return wp && wp.lat && wp.lng && typeof wp.lat === "number" && typeof wp.lng === "number" && !isNaN(wp.lat) && !isNaN(wp.lng);
                            }
                        }["RoutingMap.useEffect.initMap"]);
                        // If we lost waypoints in filtering, add default ones
                        if (initialWaypoints.length < 2 && !readOnly) {
                            initialWaypoints = [
                                L.latLng(19.076, 72.8777),
                                L.latLng(19.1136, 72.9023)
                            ];
                        }
                        // Create a throttled OSRM router with rate limiting
                        let router;
                        let lastRequestTime = 0;
                        const MIN_REQUEST_INTERVAL = 2000 // Minimum 2 seconds between requests
                        ;
                        let rateLimitHit = false;
                        try {
                            // Create a custom router that wraps OSRM with rate limiting
                            const osrmRouter = L.Routing.osrmv1({
                                serviceUrl: "https://router.project-osrm.org/route/v1",
                                profile: "driving",
                                timeout: 10 * 1000,
                                suppressDemoServerWarning: true
                            });
                            // Wrap the original route method with throttling
                            const originalRoute = osrmRouter.route;
                            router = {
                                ...osrmRouter,
                                route: ({
                                    "RoutingMap.useEffect.initMap": function(waypoints, callback, context) {
                                        const now = Date.now();
                                        // If we hit rate limit recently, use fallback
                                        if (rateLimitHit) {
                                            console.log("Using fallback router due to recent rate limit");
                                            setRoutingStatus('rate-limited');
                                            return fallbackRouter.route(waypoints, callback);
                                        }
                                        // Check if enough time has passed since last request
                                        const timeSinceLastRequest = now - lastRequestTime;
                                        if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
                                            const delay = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
                                            console.log(`Throttling request, waiting ${delay}ms`);
                                            setTimeout({
                                                "RoutingMap.useEffect.initMap": ()=>{
                                                    lastRequestTime = Date.now();
                                                    originalRoute.call(this, waypoints, {
                                                        "RoutingMap.useEffect.initMap": function(error, routes) {
                                                            if (error && (error.status === 429 || error.message?.includes('429'))) {
                                                                console.warn("Rate limit hit, switching to fallback router");
                                                                rateLimitHit = true;
                                                                setRoutingStatus('rate-limited');
                                                                // Reset rate limit flag after 5 minutes
                                                                setTimeout({
                                                                    "RoutingMap.useEffect.initMap": ()=>{
                                                                        rateLimitHit = false;
                                                                        setRoutingStatus('normal');
                                                                        console.log("Rate limit cooldown complete");
                                                                    }
                                                                }["RoutingMap.useEffect.initMap"], 5 * 60 * 1000);
                                                                return fallbackRouter.route(waypoints, callback);
                                                            }
                                                            setRoutingStatus('normal');
                                                            callback(error, routes);
                                                        }
                                                    }["RoutingMap.useEffect.initMap"]);
                                                }
                                            }["RoutingMap.useEffect.initMap"], delay);
                                            return;
                                        }
                                        lastRequestTime = now;
                                        originalRoute.call(this, waypoints, {
                                            "RoutingMap.useEffect.initMap": function(error, routes) {
                                                if (error && (error.status === 429 || error.message?.includes('429'))) {
                                                    console.warn("Rate limit hit, switching to fallback router");
                                                    rateLimitHit = true;
                                                    setRoutingStatus('rate-limited');
                                                    // Reset rate limit flag after 5 minutes
                                                    setTimeout({
                                                        "RoutingMap.useEffect.initMap": ()=>{
                                                            rateLimitHit = false;
                                                            setRoutingStatus('normal');
                                                            console.log("Rate limit cooldown complete");
                                                        }
                                                    }["RoutingMap.useEffect.initMap"], 5 * 60 * 1000);
                                                    return fallbackRouter.route(waypoints, callback);
                                                }
                                                setRoutingStatus('normal');
                                                callback(error, routes);
                                            }
                                        }["RoutingMap.useEffect.initMap"]);
                                    }
                                })["RoutingMap.useEffect.initMap"]
                            };
                        } catch (error) {
                            console.warn("Failed to create OSRM router, using fallback:", error);
                            router = fallbackRouter;
                        }
                        // Create a simpler routing control without custom markers
                        // @ts-ignore - TypeScript definitions for Leaflet Routing Machine are incomplete
                        routing = L.Routing.control({
                            waypoints: initialWaypoints,
                            routeWhileDragging: false,
                            showAlternatives: false,
                            fitSelectedRoutes: true,
                            lineOptions: {
                                styles: [
                                    {
                                        color: "#3b82f6",
                                        weight: 5
                                    }
                                ],
                                extendToWaypoints: true,
                                missingRouteTolerance: 0
                            },
                            addWaypoints: !readOnly,
                            draggableWaypoints: !readOnly,
                            useZoomParameter: false,
                            router: router,
                            createMarker: {
                                "RoutingMap.useEffect.initMap": (i, wp, n)=>{
                                    // Create custom markers for start, end, and intermediate points
                                    let icon;
                                    if (i === 0) {
                                        // Start point (green)
                                        icon = L.divIcon({
                                            className: "custom-waypoint-icon start-icon",
                                            html: '<div style="background-color: #10b981; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);"></div>',
                                            iconSize: [
                                                16,
                                                16
                                            ],
                                            iconAnchor: [
                                                8,
                                                8
                                            ]
                                        });
                                    } else if (i === n - 1) {
                                        // End point (red)
                                        icon = L.divIcon({
                                            className: "custom-waypoint-icon end-icon",
                                            html: '<div style="background-color: #ef4444; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);"></div>',
                                            iconSize: [
                                                16,
                                                16
                                            ],
                                            iconAnchor: [
                                                8,
                                                8
                                            ]
                                        });
                                    } else {
                                        // Intermediate points (blue)
                                        icon = L.divIcon({
                                            className: "custom-waypoint-icon",
                                            html: '<div style="background-color: #3b82f6; width: 10px; height: 10px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);"></div>',
                                            iconSize: [
                                                14,
                                                14
                                            ],
                                            iconAnchor: [
                                                7,
                                                7
                                            ]
                                        });
                                    }
                                    return L.marker(wp.latLng, {
                                        draggable: !readOnly,
                                        icon: icon
                                    });
                                }
                            }["RoutingMap.useEffect.initMap"]
                        });
                        // Only add the routing control to the map if the map exists
                        if (map) {
                            routing.addTo(map);
                            // Store routing control in ref for cleanup
                            routingControlRef.current = routing;
                        } else {
                            console.error("Cannot add routing control - map is null");
                            return;
                        }
                        // Handle route changes
                        routing.on("routesfound", {
                            "RoutingMap.useEffect.initMap": (e)=>{
                                if (readOnly || !isMounted.current) return;
                                const routes = e.routes;
                                if (routes && routes.length > 0) {
                                    const selectedRoute = routes[0] // Use the first (best) route
                                    ;
                                    const coordinates = selectedRoute.coordinates;
                                    // Convert coordinates to format for encoding
                                    const points = coordinates.map({
                                        "RoutingMap.useEffect.initMap.points": (coord)=>[
                                                coord.lat,
                                                coord.lng
                                            ]
                                    }["RoutingMap.useEffect.initMap.points"]);
                                    // Encode the polyline
                                    const encoded = PolylineUtil.encode(points);
                                    onPolylineChange(encoded);
                                }
                            }
                        }["RoutingMap.useEffect.initMap"]);
                        // Add click handler to the map for adding waypoints manually
                        if (!readOnly && map) {
                            // Add custom controls to help users
                            // Help button
                            const helpControl = L.Control.extend({
                                options: {
                                    position: "topright"
                                },
                                onAdd: {
                                    "RoutingMap.useEffect.initMap.helpControl": ()=>{
                                        const container = L.DomUtil.create("div", "leaflet-bar leaflet-control leaflet-control-custom");
                                        container.style.backgroundColor = "white";
                                        container.style.padding = "6px 8px";
                                        container.style.fontSize = "14px";
                                        container.style.cursor = "pointer";
                                        container.style.borderRadius = "4px";
                                        container.style.boxShadow = "0 1px 5px rgba(0,0,0,0.4)";
                                        container.style.marginBottom = "10px";
                                        container.innerHTML = '<div style="display: flex; align-items: center;"><span style="background-color: #3b82f6; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 5px;">?</span> Help</div>';
                                        container.onclick = ({
                                            "RoutingMap.useEffect.initMap.helpControl": ()=>{
                                                alert("How to create a route:\n\n1. Click anywhere on the map to add waypoints\n2. Drag waypoints to adjust the route\n3. The route will automatically follow roads\n4. If road routing fails or rate limits are hit, a direct line will be shown\n5. Rate limiting prevents excessive API calls - wait a few minutes if you see the yellow warning");
                                            }
                                        })["RoutingMap.useEffect.initMap.helpControl"];
                                        return container;
                                    }
                                }["RoutingMap.useEffect.initMap.helpControl"]
                            });
                            // Direct route button (for when routing fails)
                            const directRouteControl = L.Control.extend({
                                options: {
                                    position: "topright"
                                },
                                onAdd: {
                                    "RoutingMap.useEffect.initMap.directRouteControl": ()=>{
                                        const container = L.DomUtil.create("div", "leaflet-bar leaflet-control leaflet-control-custom");
                                        container.style.backgroundColor = "white";
                                        container.style.padding = "6px 8px";
                                        container.style.fontSize = "14px";
                                        container.style.cursor = "pointer";
                                        container.style.borderRadius = "4px";
                                        container.style.boxShadow = "0 1px 5px rgba(0,0,0,0.4)";
                                        container.innerHTML = '<div style="display: flex; align-items: center;"><span style="background-color: #ef4444; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 5px;">↔</span> Direct Route</div>';
                                        container.onclick = ({
                                            "RoutingMap.useEffect.initMap.directRouteControl": ()=>{
                                                // Create a direct route between waypoints
                                                if (!routing) return;
                                                const waypoints = routing.getWaypoints().filter({
                                                    "RoutingMap.useEffect.initMap.directRouteControl.waypoints": (wp)=>wp && wp.latLng && typeof wp.latLng.lat === "number" && typeof wp.latLng.lng === "number"
                                                }["RoutingMap.useEffect.initMap.directRouteControl.waypoints"]);
                                                if (waypoints.length >= 2) {
                                                    // Create a direct polyline between waypoints
                                                    if (map) {
                                                        L.polyline(waypoints.map({
                                                            "RoutingMap.useEffect.initMap.directRouteControl": (wp)=>[
                                                                    wp.latLng.lat,
                                                                    wp.latLng.lng
                                                                ]
                                                        }["RoutingMap.useEffect.initMap.directRouteControl"]), {
                                                            color: "#ef4444",
                                                            weight: 5,
                                                            opacity: 0.7,
                                                            dashArray: "10, 10",
                                                            lineCap: "round"
                                                        }).addTo(map);
                                                    }
                                                    // Convert waypoints to format for encoding
                                                    const points = waypoints.map({
                                                        "RoutingMap.useEffect.initMap.directRouteControl.points": (wp)=>[
                                                                wp.latLng.lat,
                                                                wp.latLng.lng
                                                            ]
                                                    }["RoutingMap.useEffect.initMap.directRouteControl.points"]);
                                                    // Encode the polyline
                                                    const encoded = PolylineUtil.encode(points);
                                                    onPolylineChange(encoded);
                                                    alert("Created direct route between waypoints (not following roads)");
                                                } else {
                                                    alert("Please add at least 2 waypoints first");
                                                }
                                            }
                                        })["RoutingMap.useEffect.initMap.directRouteControl"];
                                        return container;
                                    }
                                }["RoutingMap.useEffect.initMap.directRouteControl"]
                            });
                            try {
                                new helpControl().addTo(map);
                                new directRouteControl().addTo(map);
                            } catch (e) {
                                console.warn("Could not add custom controls:", e);
                            }
                            // Add debounced route update mechanism
                            let routeUpdateTimeout = null;
                            const debouncedRouteUpdate = {
                                "RoutingMap.useEffect.initMap.debouncedRouteUpdate": ()=>{
                                    if (routeUpdateTimeout) {
                                        clearTimeout(routeUpdateTimeout);
                                    }
                                    routeUpdateTimeout = setTimeout({
                                        "RoutingMap.useEffect.initMap.debouncedRouteUpdate": ()=>{
                                            if (routing && isMounted.current) {
                                                try {
                                                    // Trigger route calculation manually
                                                    const waypoints = routing.getWaypoints();
                                                    if (waypoints.length >= 2) {
                                                        routing.route();
                                                    }
                                                } catch (error) {
                                                    console.error("Error updating route:", error);
                                                }
                                            }
                                        }
                                    }["RoutingMap.useEffect.initMap.debouncedRouteUpdate"], 1000) // Wait 1 second after last waypoint change
                                    ;
                                }
                            }["RoutingMap.useEffect.initMap.debouncedRouteUpdate"];
                            // Add click handler to the map
                            map.on("click", {
                                "RoutingMap.useEffect.initMap": (e)=>{
                                    if (!isMounted.current || !routing) return;
                                    try {
                                        const waypoints = routing.getWaypoints();
                                        const newWaypoint = L.Routing.waypoint(e.latlng, "Waypoint " + (waypoints.length + 1));
                                        // Add the new waypoint to the end of the route
                                        routing.spliceWaypoints(waypoints.length, 0, newWaypoint);
                                        // Trigger debounced route update
                                        debouncedRouteUpdate();
                                    } catch (error) {
                                        console.error("Error handling map click:", error);
                                    }
                                }
                            }["RoutingMap.useEffect.initMap"]);
                            // Add waypoint drag end handler for debounced updates
                            if (routing) {
                                routing.on('waypointdrag', {
                                    "RoutingMap.useEffect.initMap": ()=>{
                                    // Don't update route while dragging
                                    }
                                }["RoutingMap.useEffect.initMap"]);
                                routing.on('waypointdragend', {
                                    "RoutingMap.useEffect.initMap": ()=>{
                                        // Update route after drag ends
                                        debouncedRouteUpdate();
                                    }
                                }["RoutingMap.useEffect.initMap"]);
                            }
                        }
                        // Handle routing errors
                        if (routing) {
                            routing.on("routingerror", {
                                "RoutingMap.useEffect.initMap": (e)=>{
                                    if (!isMounted.current || !map) return;
                                    console.error("Routing error:", e.error);
                                    // Create a simple direct route between waypoints
                                    try {
                                        // Get current waypoints
                                        if (!routing) return;
                                        const currentWaypoints = routing.getWaypoints().filter({
                                            "RoutingMap.useEffect.initMap.currentWaypoints": (wp)=>wp && wp.latLng && typeof wp.latLng.lat === "number" && typeof wp.latLng.lng === "number"
                                        }["RoutingMap.useEffect.initMap.currentWaypoints"]);
                                        if (currentWaypoints.length >= 2) {
                                            // Create a direct polyline between waypoints
                                            L.polyline(currentWaypoints.map({
                                                "RoutingMap.useEffect.initMap": (wp)=>[
                                                        wp.latLng.lat,
                                                        wp.latLng.lng
                                                    ]
                                            }["RoutingMap.useEffect.initMap"]), {
                                                color: "#3b82f6",
                                                weight: 5,
                                                opacity: 0.7,
                                                dashArray: "10, 10",
                                                lineCap: "round"
                                            }).addTo(map);
                                            // Add a note to the map - calculate center manually to avoid bounds error
                                            let centerLat = 0, centerLng = 0;
                                            currentWaypoints.forEach({
                                                "RoutingMap.useEffect.initMap": (wp)=>{
                                                    centerLat += wp.latLng.lat;
                                                    centerLng += wp.latLng.lng;
                                                }
                                            }["RoutingMap.useEffect.initMap"]);
                                            centerLat /= currentWaypoints.length;
                                            centerLng /= currentWaypoints.length;
                                            const center = L.latLng(centerLat, centerLng);
                                            L.marker(center, {
                                                icon: L.divIcon({
                                                    className: "route-note",
                                                    html: '<div style="background-color: rgba(255,255,255,0.8); color: #333; padding: 5px; border-radius: 4px; font-size: 12px;">Direct route (not following roads)</div>',
                                                    iconSize: [
                                                        200,
                                                        30
                                                    ],
                                                    iconAnchor: [
                                                        100,
                                                        15
                                                    ]
                                                })
                                            }).addTo(map);
                                            // Convert waypoints to format for encoding
                                            const points = currentWaypoints.map({
                                                "RoutingMap.useEffect.initMap.points": (wp)=>[
                                                        wp.latLng.lat,
                                                        wp.latLng.lng
                                                    ]
                                            }["RoutingMap.useEffect.initMap.points"]);
                                            // Encode the polyline
                                            const encoded = PolylineUtil.encode(points);
                                            onPolylineChange(encoded);
                                        }
                                    } catch (error) {
                                        console.error("Error creating direct route:", error);
                                    }
                                }
                            }["RoutingMap.useEffect.initMap"]);
                        }
                    } catch (error) {
                        console.error("Error initializing map:", error);
                        if (mapRef.current) {
                            mapRef.current.innerHTML = '<div style="padding: 20px; color: red;">Error initializing map. Please check console.</div>';
                        }
                    }
                }
            }["RoutingMap.useEffect.initMap"];
            // Initialize map only once
            initMap();
            // Cleanup function when component unmounts
            return ({
                "RoutingMap.useEffect": ()=>{
                    // Set unmounted flag
                    isMounted.current = false;
                    // Cleanup function to properly dispose of map resources
                    const cleanupMap = {
                        "RoutingMap.useEffect.cleanupMap": ()=>{
                            try {
                                console.log("Cleaning up map resources...");
                                // First remove routing control if it exists
                                if (routingControlRef.current && mapInstanceRef.current) {
                                    console.log("Removing routing control");
                                    try {
                                        mapInstanceRef.current.removeControl(routingControlRef.current);
                                    } catch (e) {
                                        console.warn("Error removing routing control:", e);
                                    }
                                    routingControlRef.current = null;
                                }
                                // Then remove the map if it exists
                                if (mapInstanceRef.current) {
                                    console.log("Removing map instance");
                                    try {
                                        mapInstanceRef.current.remove();
                                    } catch (e) {
                                        console.warn("Error removing map:", e);
                                    }
                                    mapInstanceRef.current = null;
                                }
                                // Clean up the DOM element
                                if (mapRef.current) {
                                    mapRef.current.innerHTML = "";
                                    // Remove any Leaflet-specific attributes
                                    if (mapRef.current._leaflet_id) {
                                        delete mapRef.current._leaflet_id;
                                    }
                                }
                            } catch (error) {
                                console.error("Error during map cleanup:", error);
                            }
                        }
                    }["RoutingMap.useEffect.cleanupMap"];
                    // Execute cleanup immediately to prevent issues with fast re-renders
                    cleanupMap();
                }
            })["RoutingMap.useEffect"];
        }
    }["RoutingMap.useEffect"], [
        center,
        zoom,
        initialPolyline,
        onPolylineChange,
        readOnly,
        mapReady
    ]);
    // Add this function if it's not already defined in your component
    const createFallbackRouter = ()=>{
        // This is a simple fallback router that just creates straight lines between waypoints
        return {
            route: (waypoints, callback)=>{
                try {
                    // Create a simple route with straight lines
                    const coordinates = [];
                    waypoints.forEach((wp)=>{
                        if (wp && wp.latLng) {
                            coordinates.push({
                                lat: wp.latLng.lat,
                                lng: wp.latLng.lng
                            });
                        }
                    });
                    // Create a simple route object
                    const route = {
                        name: "Direct route",
                        coordinates: coordinates,
                        summary: {
                            totalDistance: 0,
                            totalTime: 0
                        },
                        inputWaypoints: waypoints,
                        waypoints: waypoints
                    };
                    // Calculate simple distance
                    for(let i = 1; i < coordinates.length; i++){
                        const p1 = coordinates[i - 1];
                        const p2 = coordinates[i];
                        // Simple Euclidean distance (not accurate for real-world distances)
                        const dx = p2.lng - p1.lng;
                        const dy = p2.lat - p1.lat;
                        const distance = Math.sqrt(dx * dx + dy * dy) * 111000 // Rough conversion to meters
                        ;
                        route.summary.totalDistance += distance;
                        route.summary.totalTime += distance / 50 // Assume 50 m/s speed
                        ;
                    }
                    callback(null, [
                        route
                    ]);
                } catch (error) {
                    callback(error, null);
                }
            }
        };
    };
    // Reset the map container key when props change to force a complete re-render
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RoutingMap.useEffect": ()=>{
            mapContainerKey.current = Math.random().toString(36).substring(2, 11);
            // Force cleanup of any existing map
            if (mapInstanceRef.current) {
                try {
                    if (routingControlRef.current) {
                        mapInstanceRef.current.removeControl(routingControlRef.current);
                        routingControlRef.current = null;
                    }
                    mapInstanceRef.current.remove();
                    mapInstanceRef.current = null;
                } catch (e) {
                    console.warn("Error cleaning up map on key change:", e);
                }
            }
            // Reset map ready state
            setMapReady(false);
            // Check container dimensions after a short delay
            setTimeout({
                "RoutingMap.useEffect": ()=>{
                    if (mapRef.current && mapRef.current.clientWidth > 0 && mapRef.current.clientHeight > 0) {
                        setMapReady(true);
                    }
                }
            }["RoutingMap.useEffect"], 100);
        }
    }["RoutingMap.useEffect"], [
        center,
        zoom,
        initialPolyline,
        readOnly
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: mapRef,
                className: "h-[500px] w-full rounded-md border"
            }, mapContainerKey.current, false, {
                fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                lineNumber: 889,
                columnNumber: 7
            }, this),
            routingStatus === 'rate-limited' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-2 left-2 right-2 bg-yellow-100 border border-yellow-400 text-yellow-800 px-3 py-2 rounded-md text-sm z-[1001]",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: "⚠️ Rate Limited:"
                        }, void 0, false, {
                            fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                            lineNumber: 899,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "ml-2",
                            children: "Using direct routing due to API limits. Routes may not follow roads exactly."
                        }, void 0, false, {
                            fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                            lineNumber: 900,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                    lineNumber: 898,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                lineNumber: 897,
                columnNumber: 9
            }, this),
            !readOnly && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `absolute ${routingStatus === 'rate-limited' ? 'top-16' : 'top-4'} left-0 right-0 mx-auto w-fit bg-white p-3 rounded-md shadow-md text-sm z-[1000]`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center font-medium text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapIcon$3e$__["MapIcon"], {
                                    className: "h-5 w-5 mr-2 text-blue-500"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                    lineNumber: 909,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "Click on the map to add waypoints. Drag waypoints to adjust the route."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                    lineNumber: 910,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                            lineNumber: 908,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                        lineNumber: 907,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute bottom-4 left-4 bg-white p-3 rounded-md shadow-md text-sm",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "font-medium mb-1 text-gray-700",
                                children: "How to create a route:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                lineNumber: 914,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2",
                                        children: "1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                        lineNumber: 916,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Click to add waypoints"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                        lineNumber: 919,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                lineNumber: 915,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center mt-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2",
                                        children: "2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                        lineNumber: 922,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Drag markers to adjust"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                        lineNumber: 925,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                lineNumber: 921,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center mt-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2",
                                        children: "3"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                        lineNumber: 928,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: 'Use "Direct Route" button if routing fails'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                        lineNumber: 931,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                lineNumber: 927,
                                columnNumber: 13
                            }, this),
                            routingStatus === 'rate-limited' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center mt-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-yellow-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2",
                                        children: "⚠"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                        lineNumber: 935,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Rate limited - using direct routes"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                        lineNumber: 938,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                                lineNumber: 934,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
                        lineNumber: 913,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/admin/map/RoutingMap.tsx",
        lineNumber: 888,
        columnNumber: 5
    }, this);
}
_s(RoutingMap, "7LIY3gDeGURcDOuQ0fuVFmDdhCA=");
_c = RoutingMap;
var _c;
__turbopack_context__.k.register(_c, "RoutingMap");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/routeService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "routeService": (()=>routeService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
;
const routeService = {
    getRoutes: async ()=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiRequest"])('/routes');
    },
    getRoute: async (id)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiRequest"])(`/routes/${id}`);
    },
    createRoute: async (data)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiRequest"])('/routes', {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    updateRoute: async (id, data)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiRequest"])(`/routes/${id}`, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    deleteRoute: async (id)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiRequest"])(`/routes/${id}`, {
            method: 'DELETE'
        });
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/admin/dashboard/routes/create/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CreateRoutePage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeftIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeftIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SaveIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-client] (ecmascript) <export default as SaveIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$ProtectedRoute$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/auth/ProtectedRoute.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$admin$2f$map$2f$RoutingMap$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/admin/map/RoutingMap.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$routeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/routeService.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
function CreateRoutePage() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        routeName: '',
        routeNumber: '',
        startPoint: '',
        endPoint: '',
        distanceKm: 0,
        estimatedTime: 0,
        description: '',
        color: '#3b82f6',
        polyline: ''
    });
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const handleChange = (e)=>{
        const { name, value } = e.target;
        // Handle numeric values
        if (name === 'distanceKm' || name === 'estimatedTime') {
            setFormData((prev)=>({
                    ...prev,
                    [name]: value === '' ? 0 : parseFloat(value)
                }));
        } else {
            setFormData((prev)=>({
                    ...prev,
                    [name]: value
                }));
        }
    };
    const handlePolylineChange = (polyline)=>{
        setFormData((prev)=>({
                ...prev,
                polyline
            }));
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        try {
            setLoading(true);
            setError(null);
            // Validate form
            if (!formData.routeName || !formData.startPoint || !formData.endPoint || formData.distanceKm <= 0 || formData.estimatedTime <= 0) {
                setError('Please fill in all required fields');
                return;
            }
            // Create route using the service
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$routeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["routeService"].createRoute(formData);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Route created successfully');
            // Redirect to route details page
            router.push(`/admin/dashboard/routes/${response.id}`);
        } catch (err) {
            console.error('Error creating route:', err);
            setError(err.message || 'Failed to create route. Please try again.');
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to create route');
        } finally{
            setLoading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$ProtectedRoute$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        allowedRoles: [
            'admin'
        ],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container mx-auto py-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-between items-center mb-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "icon",
                                asChild: true,
                                className: "mr-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/admin/dashboard/routes",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeftIcon$3e$__["ArrowLeftIcon"], {
                                        className: "h-5 w-5"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                        lineNumber: 91,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                    lineNumber: 90,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                lineNumber: 89,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-3xl font-bold",
                                children: "Create New Route"
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                lineNumber: 94,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                        lineNumber: 88,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                    lineNumber: 87,
                    columnNumber: 9
                }, this),
                error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-destructive/15 text-destructive p-4 rounded-md mb-6",
                    children: error
                }, void 0, false, {
                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                    lineNumber: 99,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: handleSubmit,
                    className: "space-y-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "routeName",
                                                    children: [
                                                        "Route Name ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-destructive",
                                                            children: "*"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                            lineNumber: 108,
                                                            columnNumber: 55
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 108,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                    id: "routeName",
                                                    name: "routeName",
                                                    value: formData.routeName,
                                                    onChange: handleChange,
                                                    placeholder: "e.g. Mumbai Central to Andheri",
                                                    required: true
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 109,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 107,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "routeNumber",
                                                    children: "Route Number"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 120,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                    id: "routeNumber",
                                                    name: "routeNumber",
                                                    value: formData.routeNumber,
                                                    onChange: handleChange,
                                                    placeholder: "e.g. R123"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 121,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 119,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "startPoint",
                                                    children: [
                                                        "Start Point ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-destructive",
                                                            children: "*"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                            lineNumber: 131,
                                                            columnNumber: 57
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 131,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                    id: "startPoint",
                                                    name: "startPoint",
                                                    value: formData.startPoint,
                                                    onChange: handleChange,
                                                    placeholder: "e.g. Mumbai Central",
                                                    required: true
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 132,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 130,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "endPoint",
                                                    children: [
                                                        "End Point ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-destructive",
                                                            children: "*"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                            lineNumber: 143,
                                                            columnNumber: 53
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 143,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                    id: "endPoint",
                                                    name: "endPoint",
                                                    value: formData.endPoint,
                                                    onChange: handleChange,
                                                    placeholder: "e.g. Andheri",
                                                    required: true
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 144,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 142,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid grid-cols-2 gap-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            htmlFor: "distanceKm",
                                                            children: [
                                                                "Distance (km) ",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-destructive",
                                                                    children: "*"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                                    lineNumber: 156,
                                                                    columnNumber: 61
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                            lineNumber: 156,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                            id: "distanceKm",
                                                            name: "distanceKm",
                                                            type: "number",
                                                            step: "0.1",
                                                            min: "0",
                                                            value: formData.distanceKm,
                                                            onChange: handleChange,
                                                            placeholder: "e.g. 15.5",
                                                            required: true
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                            lineNumber: 157,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 155,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                            htmlFor: "estimatedTime",
                                                            children: [
                                                                "Est. Time (min) ",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-destructive",
                                                                    children: "*"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                                    lineNumber: 171,
                                                                    columnNumber: 66
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                            lineNumber: 171,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                            id: "estimatedTime",
                                                            name: "estimatedTime",
                                                            type: "number",
                                                            min: "1",
                                                            value: formData.estimatedTime,
                                                            onChange: handleChange,
                                                            placeholder: "e.g. 45",
                                                            required: true
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                            lineNumber: 172,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 170,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 154,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "color",
                                                    children: "Route Color"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 186,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                            id: "color",
                                                            name: "color",
                                                            type: "color",
                                                            value: formData.color,
                                                            onChange: handleChange,
                                                            className: "w-12 h-10 p-1"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                            lineNumber: 188,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                            value: formData.color,
                                                            onChange: handleChange,
                                                            name: "color",
                                                            className: "flex-1"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                            lineNumber: 196,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 187,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 185,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "description",
                                                    children: "Description"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 206,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                                                    id: "description",
                                                    name: "description",
                                                    value: formData.description,
                                                    onChange: handleChange,
                                                    placeholder: "Enter route description",
                                                    rows: 4
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                    lineNumber: 207,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 205,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                    lineNumber: 106,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                            children: "Route Path"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 219,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$admin$2f$map$2f$RoutingMap$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RoutingMap"], {
                                            onPolylineChange: handlePolylineChange,
                                            center: [
                                                19.0760,
                                                72.8777
                                            ],
                                            zoom: 12
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 220,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-muted-foreground",
                                            children: "Click on the map to add waypoints. Drag waypoints to adjust the route. The route will automatically follow roads."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                            lineNumber: 225,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                    lineNumber: 218,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                            lineNumber: 105,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-end gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    type: "button",
                                    onClick: ()=>router.push('/admin/dashboard/routes'),
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                    lineNumber: 232,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "submit",
                                    disabled: loading,
                                    children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "animate-spin mr-2",
                                                children: "⏳"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                lineNumber: 242,
                                                columnNumber: 19
                                            }, this),
                                            "Creating..."
                                        ]
                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SaveIcon$3e$__["SaveIcon"], {
                                                className: "mr-2 h-4 w-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                                lineNumber: 247,
                                                columnNumber: 19
                                            }, this),
                                            "Create Route"
                                        ]
                                    }, void 0, true)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                                    lineNumber: 239,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                            lineNumber: 231,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
                    lineNumber: 104,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
            lineNumber: 86,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/admin/dashboard/routes/create/page.tsx",
        lineNumber: 85,
        columnNumber: 5
    }, this);
}
_s(CreateRoutePage, "MLhkd76i7Ln6K5U1rNg4rBMn5RA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = CreateRoutePage;
var _c;
__turbopack_context__.k.register(_c, "CreateRoutePage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_0207caa0._.js.map