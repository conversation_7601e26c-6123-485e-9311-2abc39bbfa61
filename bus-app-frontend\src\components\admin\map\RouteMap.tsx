'use client';

import { useEffect, useRef } from 'react';
import { MapIcon } from 'lucide-react';

// Polyline encoding/decoding utility
const PolylineUtil = {
  encode: function(points: any[], precision?: number) {
    if (!points.length) return '';

    const factor = Math.pow(10, precision !== undefined ? precision : 5);

    let output = this.encodePoint(points[0].lat || points[0][0], points[0].lng || points[0][1], factor);

    for (let i = 1; i < points.length; i++) {
      const a = points[i - 1];
      const b = points[i];
      output += this.encodePoint(
        b.lat || b[0],
        b.lng || b[1],
        factor,
        a.lat || a[0],
        a.lng || a[1]
      );
    }

    return output;
  },

  encodePoint: function(lat: number, lng: number, factor: number, prevLat?: number, prevLng?: number) {
    // Round to the nearest factor
    lat = Math.round(lat * factor);
    lng = Math.round(lng * factor);

    // Delta encode
    const deltaLat = lat - (prevLat !== undefined ? Math.round(prevLat * factor) : 0);
    const deltaLng = lng - (prevLng !== undefined ? Math.round(prevLng * factor) : 0);

    return this.encodeSignedNumber(deltaLat) + this.encodeSignedNumber(deltaLng);
  },

  encodeSignedNumber: function(num: number) {
    let sgn_num = num << 1;
    if (num < 0) {
      sgn_num = ~sgn_num;
    }
    return this.encodeNumber(sgn_num);
  },

  encodeNumber: function(num: number) {
    let encodeString = '';
    while (num >= 0x20) {
      encodeString += String.fromCharCode((0x20 | (num & 0x1f)) + 63);
      num >>= 5;
    }
    encodeString += String.fromCharCode(num + 63);
    return encodeString;
  },

  decode: function(encoded: string, precision?: number) {
    if (!encoded.length) return [];

    const factor = Math.pow(10, precision !== undefined ? precision : 5);
    const len = encoded.length;
    let index = 0;
    let lat = 0;
    let lng = 0;
    const points = [];

    while (index < len) {
      let b;
      let shift = 0;
      let result = 0;

      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);

      const deltaLat = ((result & 1) ? ~(result >> 1) : (result >> 1));
      lat += deltaLat;

      shift = 0;
      result = 0;

      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);

      const deltaLng = ((result & 1) ? ~(result >> 1) : (result >> 1));
      lng += deltaLng;

      points.push([lat / factor, lng / factor]);
    }

    return points;
  }
};

interface RouteMapProps {
  initialPolyline?: string;
  onPolylineChange: (polyline: string) => void;
  center?: [number, number]; // [latitude, longitude]
  zoom?: number;
  readOnly?: boolean;
}

export function RouteMap({
  initialPolyline,
  onPolylineChange,
  center = [19.0760, 72.8777], // Default center at Mumbai
  zoom = 12,
  readOnly = false
}: RouteMapProps) {
  // Extend HTMLDivElement to include Leaflet properties
  interface MapDiv extends HTMLDivElement {
    _leaflet_id?: number;
  }

  const mapRef = useRef<MapDiv>(null);

  // Initialize the map
  useEffect(() => {
    if (typeof window === 'undefined' || !mapRef.current) return;

    // Add a debug message to the map container
    if (mapRef.current) {
      mapRef.current.innerHTML = '<div style="padding: 20px; color: #666;">Loading map...</div>';
    }

    // Dynamically import Leaflet
    const initMap = async () => {
      try {
        console.log("Initializing Leaflet map...");

        // Import libraries
        const L = (await import('leaflet')).default;
        console.log("Leaflet loaded successfully");

        // Handle CSS imports with a workaround for TypeScript
        // These imports are needed for the styles but TypeScript complains about them
        try {
          // @ts-ignore - CSS imports
          await import('leaflet/dist/leaflet.css');
          await import('leaflet-draw');
          // @ts-ignore - CSS imports
          await import('leaflet-draw/dist/leaflet.draw.css');
          console.log("Leaflet CSS and Draw plugin loaded");

          // Fix Leaflet's default icon paths
          // @ts-ignore - Leaflet's typings don't include _getIconUrl but it exists
          delete (L.Icon.Default.prototype as any)._getIconUrl;
          L.Icon.Default.mergeOptions({
            iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
            iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
            shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
          });
          console.log("Leaflet icon paths fixed");
        } catch (e) {
          console.error("CSS imports failed:", e);
          if (mapRef.current) {
            mapRef.current.innerHTML = '<div style="padding: 20px; color: red;">Error loading map styles. Please check console.</div>';
          }
          return;
        }

        // Clean up existing map instance if it exists
        if (mapRef.current && mapRef.current._leaflet_id) {
          console.log('Map container already has a map instance, cleaning up');
          return;
        }

        // Create map - we've already checked mapRef.current is not null above
        console.log("Creating map with center:", center, "and zoom:", zoom);
        const map = L.map(mapRef.current!, {
          center: center,
          zoom: zoom,
          zoomControl: true,
          attributionControl: true
        });
        console.log("Map created successfully");

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19
        }).addTo(map);

        // Create feature group for drawn items
        const drawnItems = new L.FeatureGroup();
        map.addLayer(drawnItems);

        // Add drawing controls if not in read-only mode
        if (!readOnly) {
          // Create draw control
          const drawControl = new L.Control.Draw({
            edit: {
              featureGroup: drawnItems
            },
            draw: {
              polygon: false,
              rectangle: false,
              circle: false,
              circlemarker: false,
              marker: false,
              polyline: {
                shapeOptions: {
                  color: '#3b82f6',
                  weight: 5
                },
                allowIntersection: false
              }
            }
          });
          map.addControl(drawControl);

          // Handle draw created event
          map.on('draw:created', (e: any) => {
            const layer = e.layer;
            drawnItems.addLayer(layer);

            // Get polyline as encoded string
            const latlngs = layer.getLatLngs();
            // Flatten the latlngs if it's a multi-dimensional array
            const flatLatlngs = Array.isArray(latlngs[0]) ? latlngs[0] : latlngs;
            const encoded = PolylineUtil.encode(flatLatlngs);
            onPolylineChange(encoded);
          });

          // Handle draw edited event
          map.on('draw:edited', (e: any) => {
            const layers = e.layers;
            layers.eachLayer((layer: any) => {
              // Get polyline as encoded string
              const latlngs = layer.getLatLngs();
              // Flatten the latlngs if it's a multi-dimensional array
              const flatLatlngs = Array.isArray(latlngs[0]) ? latlngs[0] : latlngs;
              const encoded = PolylineUtil.encode(flatLatlngs);
              onPolylineChange(encoded);
            });
          });

          // Handle draw deleted event
          map.on('draw:deleted', () => {
            onPolylineChange('');
          });
        }

        // If initial polyline is provided, add it to the map
        if (initialPolyline && initialPolyline.length > 0) {
          try {
            const decodedPath = PolylineUtil.decode(initialPolyline);
            // Convert to LatLng objects for Leaflet
            const latLngs = decodedPath.map(point => L.latLng(point[0], point[1]));

            const polyline = L.polyline(latLngs, {
              color: '#3b82f6',
              weight: 5
            });

            drawnItems.addLayer(polyline);

            // Fit map to polyline bounds
            map.fitBounds(polyline.getBounds());
          } catch (error) {
            console.error('Error decoding polyline:', error);
          }
        }

        // Return cleanup function
        return () => {
          map.remove();
        };
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    };

    // Initialize map
    const cleanup = initMap();

    // Cleanup function
    return () => {
      cleanup?.then(cleanupFn => cleanupFn?.());
    };
  }, [center, zoom, initialPolyline, onPolylineChange, readOnly]);

  return (
    <div className="relative">
      <div ref={mapRef} className="h-[500px] w-full rounded-md border" />
      {!readOnly && (
        <div className="absolute bottom-4 left-4 bg-white p-2 rounded-md shadow-md text-sm">
          <div className="flex items-center">
            <MapIcon className="h-4 w-4 mr-1" />
            <span>Draw a route by clicking the polyline tool and then clicking on the map</span>
          </div>
        </div>
      )}
    </div>
  );
}
