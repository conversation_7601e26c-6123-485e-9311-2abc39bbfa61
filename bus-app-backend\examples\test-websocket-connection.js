/**
 * Test script to verify WebSocket connections
 * 
 * This script tests the WebSocket connection to the server and verifies
 * that events are being received correctly.
 */

const { io } = require('socket.io-client');

// Configuration
const config = {
  serverUrl: 'http://localhost:5000',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MGUzNjIzYTBmN2MxMTM2MDM3ODk3YiIsImlhdCI6MTc0NTc2MTgyNywiZXhwIjoxNzQ4MzUzODI3fQ.IRsNREQZhNO0C2zz3QW-zLc1vuRmHTjulXccjBn1BJE', // Add your JWT token here
  testDuration: 60000, // Test duration in milliseconds (1 minute)
};

// Main test function
async function testWebSocketConnection() {
  if (!config.token) {
    console.error('Error: JWT token is required. Please add your token to the config.');
    return;
  }

  console.log('Starting WebSocket connection test...');
  console.log(`Server URL: ${config.serverUrl}`);
  console.log(`Test duration: ${config.testDuration / 1000} seconds`);

  // Connect to main namespace
  const mainSocket = io(config.serverUrl, {
    auth: { token: config.token }
  });

  // Connect to bus-locations namespace
  const busLocationsSocket = io(`${config.serverUrl}/bus-locations`, {
    auth: { token: config.token }
  });

  // Set up event listeners for main socket
  mainSocket.on('connect', () => {
    console.log('✅ Connected to main namespace');
    console.log(`Socket ID: ${mainSocket.id}`);
  });

  mainSocket.on('disconnect', (reason) => {
    console.log(`❌ Disconnected from main namespace: ${reason}`);
  });

  mainSocket.on('connect_error', (error) => {
    console.error(`❌ Main connection error: ${error.message}`);
  });

  // Set up event listeners for bus-locations socket
  busLocationsSocket.on('connect', () => {
    console.log('✅ Connected to bus-locations namespace');
    console.log(`Socket ID: ${busLocationsSocket.id}`);
    
    // Subscribe to test events
    console.log('Subscribing to test events...');
    
    // Test subscribing to a bus
    busLocationsSocket.emit('subscribe-bus', 'test-bus-id');
    
    // Test subscribing to a route
    busLocationsSocket.emit('subscribe-route', 'test-route-id');
    
    // Test subscribing to nearby buses
    if (navigator && navigator.geolocation) {
      busLocationsSocket.emit('subscribe-nearby', {
        latitude: 37.7749,
        longitude: -122.4194,
        radius: 1000
      });
    }
  });

  busLocationsSocket.on('disconnect', (reason) => {
    console.log(`❌ Disconnected from bus-locations namespace: ${reason}`);
  });

  busLocationsSocket.on('connect_error', (error) => {
    console.error(`❌ Bus-locations connection error: ${error.message}`);
  });

  // Listen for bus location updates
  busLocationsSocket.on('bus-location-update', (data) => {
    console.log('📍 Received bus location update:');
    console.log(JSON.stringify(data, null, 2));
  });

  // Listen for passenger contributions
  busLocationsSocket.on('passenger-contribution', (data) => {
    console.log('👤 Received passenger contribution:');
    console.log(JSON.stringify(data, null, 2));
  });

  // End test after specified duration
  setTimeout(() => {
    console.log(`Test completed after ${config.testDuration / 1000} seconds.`);
    
    // Disconnect sockets
    mainSocket.disconnect();
    busLocationsSocket.disconnect();
    
    // Print summary
    console.log('\nTest Summary:');
    console.log(`Main socket connected: ${mainSocket.connected ? 'Yes' : 'No'}`);
    console.log(`Bus-locations socket connected: ${busLocationsSocket.connected ? 'Yes' : 'No'}`);
    
    process.exit(0);
  }, config.testDuration);
}

// Start test
testWebSocketConnection();
