import { jwtDecode } from 'jwt-decode';
import Cookies from 'js-cookie';

// Types
export interface UserData {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'driver' | 'admin';
  token: string;
  profilePicture?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  deviceToken?: string;
  deviceId?: string; // Added deviceId for admin login
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  phone?: string;
  deviceToken?: string;
}

// Constants
const TOKEN_COOKIE_NAME = 'bus_app_auth_token';
const USER_DATA_COOKIE_NAME = 'bus_app_user_data';
const COOKIE_OPTIONS = {
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  expires: 30, // 30 days
  path: '/'
};

// Token management
export const setAuthToken = (token: string): void => {
  Cookies.set(TOKEN_COOKIE_NAME, token, COOKIE_OPTIONS);
};

export const getAuthToken = (): string | undefined => {
  return Cookies.get(TOKEN_COOKIE_NAME);
};

export const removeAuthToken = (): void => {
  Cookies.remove(TOKEN_COOKIE_NAME, { path: '/' });
};

// User data management
export const setUserData = (userData: Omit<UserData, 'token'>): void => {
  Cookies.set(USER_DATA_COOKIE_NAME, JSON.stringify(userData), COOKIE_OPTIONS);
};

export const getUserData = (): Omit<UserData, 'token'> | null => {
  const userDataStr = Cookies.get(USER_DATA_COOKIE_NAME);
  if (!userDataStr) return null;

  try {
    return JSON.parse(userDataStr);
  } catch (error) {
    console.error('Error parsing user data:', error);
    return null;
  }
};

export const removeUserData = (): void => {
  Cookies.remove(USER_DATA_COOKIE_NAME, { path: '/' });
};

// Token validation
export const isTokenValid = (token: string): boolean => {
  try {
    const decoded = jwtDecode<{ exp: number }>(token);
    const currentTime = Date.now() / 1000;

    return decoded.exp > currentTime;
  } catch (error) {
    return false;
  }
};

// Authentication status
export const isAuthenticated = (): boolean => {
  const token = getAuthToken();
  if (!token) return false;

  return isTokenValid(token);
};

// Logout
export const logout = (): void => {
  removeAuthToken();
  removeUserData();
};
