import { apiRequest } from '@/lib/api';

// Types
export interface Bus {
  id: string;
  busNumber: string;
  busName: string;
  totalSeats: number;
  currentSeatsAvailable: number;
  currentPassengerCount: number;
  isCrowded: boolean;
  isActive: boolean;
  status: string;
  busType?: string;
  features?: string[];
  fuelType?: string;
  routeId?: string;
  route?: {
    id: string;
    routeName: string;
    routeNumber: string;
    startPoint: string;
    endPoint: string;
  };
  currentLocation?: {
    latitude: number;
    longitude: number;
    heading?: number;
    speed?: number;
    timestamp: string;
    isPassengerData?: boolean;
  };
}

export interface NearbyBusesParams {
  latitude: number;
  longitude: number;
  radius?: number;
}

// Get all buses
export async function getAllBuses(): Promise<Bus[]> {
  try {
    // Use apiRequest which automatically adds the auth token
    return await apiRequest<Bus[]>('/buses');
  } catch (error) {
    console.error('Failed to fetch buses:', error);
    throw error;
  }
}

// Get bus by ID
export async function getBusById(id: string): Promise<Bus> {
  try {
    // Use apiRequest which automatically adds the auth token
    return await apiRequest<Bus>(`/buses/${id}`);
  } catch (error) {
    console.error(`Failed to fetch bus with ID ${id}:`, error);
    throw error;
  }
}

// Get bus location
export async function getBusLocation(id: string) {
  try {
    // Use apiRequest which automatically adds the auth token
    return await apiRequest<any>(`/buses/${id}/location`);
  } catch (error) {
    console.error(`Failed to fetch location for bus ${id}:`, error);
    throw error;
  }
}

// Get nearby buses
export async function getNearbyBuses({
  latitude,
  longitude,
  radius = 1000,
}: NearbyBusesParams): Promise<Bus[]> {
  try {
    // Use apiRequest which automatically adds the auth token
    return await apiRequest<Bus[]>(
      `/locations/nearby-buses?latitude=${latitude}&longitude=${longitude}&radius=${radius}`
    );
  } catch (error) {
    console.error('Failed to fetch nearby buses:', error);
    throw error;
  }
}
