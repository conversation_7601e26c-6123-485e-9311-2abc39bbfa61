/**
 * <PERSON><PERSON><PERSON> to create a test bus for WebSocket testing
 */

const axios = require('axios');

// Configuration
const config = {
  serverUrl: 'http://localhost:5000',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MGRjNGM2OTgwMTRmZDYwNjYxOGU2NCIsImlhdCI6MTc0NTc2MTg3MSwiZXhwIjoxNzQ4MzUzODcxfQ.YkzuDpAwqLILyFXG8h0GPbtGpfeslqoooyHvQnbnYCU', // Add your admin JWT token here
  bus: {
    busNumber: 'TEST001',
    busName: 'Test Bus',
    totalSeats: 40,
    currentSeatsAvailable: 40,
    status: 'active',
    routeId: null // Optional: Add a route ID if you have one
  }
};

async function createTestBus() {
  if (!config.token) {
    console.error('Error: Admin JWT token is required. Please add your token to the config.');
    return;
  }

  console.log('Creating test bus...');
  
  try {
    // Set up axios instance with authorization header
    const api = axios.create({
      baseURL: config.serverUrl,
      headers: {
        'Authorization': `Bearer ${config.token}`,
        'Content-Type': 'application/json'
      }
    });

    // Try to create the bus
    console.log('Sending request to create bus...');
    const response = await api.post('/api/buses', config.bus);
    
    console.log('✅ Bus created successfully');
    console.log('Bus ID:', response.data.id);
    console.log('Bus Number:', response.data.busNumber);
    console.log('Bus Name:', response.data.busName);
    
    console.log('\nFor test-bus-movement.js:');
    console.log(`busId: '${response.data.id}',`);
    
    return response.data;
  } catch (error) {
    console.error('❌ Failed to create bus');
    
    if (error.response && error.response.status === 400 && 
        error.response.data.message.includes('already exists')) {
      console.log('Bus with this number already exists, trying to find it...');
      
      try {
        // Try to find the bus by number
        const api = axios.create({
          baseURL: config.serverUrl,
          headers: {
            'Authorization': `Bearer ${config.token}`,
            'Content-Type': 'application/json'
          }
        });
        
        const response = await api.get('/api/buses');
        const buses = response.data;
        
        const testBus = buses.find(bus => bus.busNumber === config.bus.busNumber);
        
        if (testBus) {
          console.log('✅ Found existing test bus');
          console.log('Bus ID:', testBus.id);
          console.log('Bus Number:', testBus.busNumber);
          console.log('Bus Name:', testBus.busName);
          
          console.log('\nFor test-bus-movement.js:');
          console.log(`busId: '${testBus.id}',`);
          
          return testBus;
        } else {
          console.error('Could not find test bus in the list of buses');
        }
      } catch (findError) {
        console.error('Error finding bus:', findError.message);
        if (findError.response) {
          console.error('Server response:', findError.response.data);
        }
      }
    } else {
      console.error('Error details:', error.message);
      if (error.response) {
        console.error('Server response:', error.response.data);
      }
    }
  }
}

// Run the script
createTestBus();
