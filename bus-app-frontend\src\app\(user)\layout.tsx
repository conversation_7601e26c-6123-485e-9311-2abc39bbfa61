'use client';

import { usePathname } from 'next/navigation';
import Client<PERSON>avbar from "@/components/client/navbar";

export default function UserLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  
  // Only hide navbar on auth pages
  const isAuthPage = pathname?.startsWith('/auth/');
  
  return (
    <div className="flex min-h-screen flex-col">
      {!isAuthPage && <ClientNavbar />}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}
