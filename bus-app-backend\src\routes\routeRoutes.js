const express = require('express');
const router = express.Router();
const routeController = require('../controllers/routeController');
const { authMiddleware } = require('../middleware/authMiddleware');
const { adminMiddleware } = require('../middleware/adminMiddleware');

/**
 * @route   GET /api/routes
 * @desc    Get all routes
 * @access  Private
 */
router.get('/', authMiddleware, routeController.getAllRoutes);

/**
 * @route   GET /api/routes/:id
 * @desc    Get route by ID
 * @access  Private
 */
router.get('/:id', authMiddleware, routeController.getRouteById);

/**
 * @route   GET /api/routes/:id/stops
 * @desc    Get all stops for a route
 * @access  Private
 */
router.get('/:id/stops', authMiddleware, routeController.getRouteStops);

/**
 * @route   GET /api/routes/:id/buses
 * @desc    Get all buses for a route
 * @access  Private
 */
router.get('/:id/buses', authMiddleware, routeController.getRouteBuses);

/**
 * @route   POST /api/routes
 * @desc    Create a new route
 * @access  Private/Admin
 */
router.post('/', adminMiddleware, routeController.createRoute);

/**
 * @route   POST /api/routes/:id/stops
 * @desc    Add a stop to a route
 * @access  Private/Admin
 */
router.post('/:id/stops', adminMiddleware, routeController.addRouteStop);

/**
 * @route   PUT /api/routes/:id
 * @desc    Update a route
 * @access  Private/Admin
 */
router.put('/:id', adminMiddleware, routeController.updateRoute);

/**
 * @route   DELETE /api/routes/:id
 * @desc    Delete a route
 * @access  Private/Admin
 */
router.delete('/:id', adminMiddleware, routeController.deleteRoute);

module.exports = router;
