/**
 * Test script to simulate passenger location contributions
 * 
 * This script simulates multiple passengers on a bus contributing their location
 * data to help track the bus when driver updates are not available.
 */

const axios = require('axios');

// Configuration
const config = {
  serverUrl: 'http://localhost:5000',
  passengerCount: 3, // Number of simulated passengers
  updateInterval: 10000, // Update interval in milliseconds (10 seconds)
  simulationDuration: 300000, // Simulation duration in milliseconds (5 minutes)
  // Passenger tokens (add your JWT tokens for test user accounts)
  passengerTokens: [
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************.q4I9kzYEnaZMEivSzMu6BvpbfA2PN-6UGUm8VvW6Ecw', // Passenger 1 token
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************.n7aIRTB26EoabUVVTyQ1mUJqyITnhVe5jChTsiwYys4', // Passenger 2 token
    ''  // Passenger 3 token
  ],
  // Base location (all passengers will be near this location with some random variation)
  baseLocation: {
    lat: 37.7749,
    lng: -122.4194
  },
  // Maximum random variation in meters
  locationVariation: 20,
  // Accuracy range (min, max) in meters
  accuracyRange: [5, 30]
};

// Helper function to add random variation to location
function addLocationVariation(baseLat, baseLng, maxVariationMeters) {
  // Convert meters to approximate degrees (very rough approximation)
  const metersToDegreesLat = 0.000009;
  const metersToDegreesLng = 0.000011;
  
  const latVariation = (Math.random() * 2 - 1) * maxVariationMeters * metersToDegreesLat;
  const lngVariation = (Math.random() * 2 - 1) * maxVariationMeters * metersToDegreesLng;
  
  return {
    lat: baseLat + latVariation,
    lng: baseLng + lngVariation
  };
}

// Helper function to get random value in range
function getRandomInRange(min, max) {
  return Math.random() * (max - min) + min;
}

// Main simulation function
async function simulatePassengerContributions() {
  // Validate configuration
  if (config.passengerTokens.some(token => !token)) {
    console.error('Error: All passenger tokens are required. Please add your tokens to the config.');
    return;
  }

  if (config.passengerCount > config.passengerTokens.length) {
    console.error(`Error: Not enough tokens for ${config.passengerCount} passengers. Reducing to ${config.passengerTokens.length}.`);
    config.passengerCount = config.passengerTokens.length;
  }

  console.log('Starting passenger contributions simulation...');
  console.log(`Number of passengers: ${config.passengerCount}`);
  console.log(`Server URL: ${config.serverUrl}`);
  console.log(`Update interval: ${config.updateInterval / 1000} seconds`);
  console.log(`Simulation duration: ${config.simulationDuration / 60000} minutes`);
  console.log(`Base location: ${config.baseLocation.lat}, ${config.baseLocation.lng}`);

  const startTime = Date.now();
  
  // Create API instances for each passenger
  const passengerApis = config.passengerTokens.slice(0, config.passengerCount).map(token => {
    return axios.create({
      baseURL: config.serverUrl,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
  });

  // Simulation loop
  const simulationInterval = setInterval(async () => {
    const elapsedTime = Date.now() - startTime;
    
    // Check if simulation should end
    if (elapsedTime >= config.simulationDuration) {
      clearInterval(simulationInterval);
      console.log('Simulation completed.');
      return;
    }

    // Send location updates for each passenger
    for (let i = 0; i < config.passengerCount; i++) {
      // Add random variation to base location
      const location = addLocationVariation(
        config.baseLocation.lat,
        config.baseLocation.lng,
        config.locationVariation
      );
      
      // Generate random accuracy
      const accuracy = getRandomInRange(config.accuracyRange[0], config.accuracyRange[1]);
      
      // Prepare location update data
      const locationData = {
        latitude: location.lat,
        longitude: location.lng,
        accuracy: accuracy,
        // Optional fields
        altitude: null,
        speed: getRandomInRange(0, 5), // Random low speed (0-5 km/h)
        heading: getRandomInRange(0, 360) // Random heading
      };

      try {
        // Send location update to server
        const response = await passengerApis[i].post('/api/locations/user', locationData);
        console.log(`Passenger ${i+1} location update sent: ${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}`);
        console.log(`Server response: ${response.status} ${response.statusText}`);
      } catch (error) {
        console.error(`Error sending location update for passenger ${i+1}:`, error.message);
        if (error.response) {
          console.error('Server response:', error.response.data);
        }
      }
    }
  }, config.updateInterval);
}

// Start simulation
simulatePassengerContributions();
