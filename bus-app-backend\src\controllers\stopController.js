const { prisma } = require('../config/db');

/**
 * Get all stops
 * @route GET /api/stops
 * @access Public
 */
const getAllStops = async (req, res) => {
  try {
    const { routeId, isActive } = req.query;
    
    // Build filter object
    const filter = {};
    if (routeId) filter.routeId = routeId;
    if (isActive !== undefined) filter.isActive = isActive === 'true';
    
    const stops = await prisma.stop.findMany({
      where: filter,
      include: {
        route: {
          select: {
            id: true,
            routeName: true,
            routeNumber: true,
          },
        },
      },
      orderBy: [
        { routeId: 'asc' },
        { stopOrder: 'asc' },
      ],
    });
    
    res.json(stops);
  } catch (error) {
    console.error('Get all stops error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get stop by ID
 * @route GET /api/stops/:id
 * @access Public
 */
const getStopById = async (req, res) => {
  try {
    const stop = await prisma.stop.findUnique({
      where: { id: req.params.id },
      include: {
        route: {
          select: {
            id: true,
            routeName: true,
            routeNumber: true,
            startPoint: true,
            endPoint: true,
          },
        },
      },
    });

    if (!stop) {
      return res.status(404).json({ message: 'Stop not found' });
    }

    // Get estimated arrivals for this stop
    const estimatedArrivals = await prisma.busArrivalEstimate.findMany({
      where: { 
        stopId: req.params.id,
        estimatedArrivalTime: {
          gte: new Date(),
        },
      },
      include: {
        stop: {
          select: {
            id: true,
            stopName: true,
            stopCode: true,
          },
        },
      },
      orderBy: { estimatedArrivalTime: 'asc' },
    });

    // Get buses that are near this stop
    const nearbyBuses = await prisma.busLocation.findMany({
      where: {
        nearestStopId: req.params.id,
        distanceToStop: {
          lte: 500, // Within 500 meters
        },
        timestamp: {
          gte: new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
        },
      },
      include: {
        bus: {
          select: {
            id: true,
            busNumber: true,
            busName: true,
            status: true,
            routeId: true,
          },
        },
      },
      orderBy: { timestamp: 'desc' },
    });

    // Group by bus (get only the latest location for each bus)
    const uniqueBuses = [];
    const busIds = new Set();
    
    for (const location of nearbyBuses) {
      if (!busIds.has(location.busId)) {
        busIds.add(location.busId);
        uniqueBuses.push(location);
      }
    }

    res.json({
      ...stop,
      estimatedArrivals,
      nearbyBuses: uniqueBuses,
    });
  } catch (error) {
    console.error('Get stop by ID error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update a stop
 * @route PUT /api/stops/:id
 * @access Private/Admin
 */
const updateStop = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const { 
      stopName, 
      stopCode,
      latitude, 
      longitude, 
      stopOrder,
      description,
      amenities,
      isActive,
      routeId
    } = req.body;

    // Check if stop exists
    const stop = await prisma.stop.findUnique({
      where: { id: req.params.id },
    });

    if (!stop) {
      return res.status(404).json({ message: 'Stop not found' });
    }

    // If routeId is changed, check if route exists
    if (routeId && routeId !== stop.routeId) {
      const routeExists = await prisma.route.findUnique({
        where: { id: routeId },
      });

      if (!routeExists) {
        return res.status(400).json({ message: 'Route not found' });
      }
    }

    // If stopOrder is changed and on the same route, check if we need to reorder other stops
    if (stopOrder && parseInt(stopOrder) !== stop.stopOrder && (!routeId || routeId === stop.routeId)) {
      // Get all stops with order >= stopOrder, excluding this stop
      const stopsToUpdate = await prisma.stop.findMany({
        where: { 
          routeId: stop.routeId,
          id: { not: stop.id },
          stopOrder: {
            gte: parseInt(stopOrder),
          },
        },
        orderBy: { stopOrder: 'asc' },
      });

      // Update stop orders
      for (const stopToUpdate of stopsToUpdate) {
        await prisma.stop.update({
          where: { id: stopToUpdate.id },
          data: { stopOrder: stopToUpdate.stopOrder + 1 },
        });
      }
    }

    // Prepare update data
    const updateData = {};
    if (stopName) updateData.stopName = stopName;
    if (stopCode !== undefined) updateData.stopCode = stopCode;
    if (latitude) updateData.latitude = parseFloat(latitude);
    if (longitude) updateData.longitude = parseFloat(longitude);
    if (stopOrder) updateData.stopOrder = parseInt(stopOrder);
    if (description !== undefined) updateData.description = description;
    if (amenities) updateData.amenities = amenities.split(',').map(a => a.trim());
    if (isActive !== undefined) updateData.isActive = isActive === true || isActive === 'true';
    if (routeId) updateData.routeId = routeId;

    // Update stop
    const updatedStop = await prisma.stop.update({
      where: { id: req.params.id },
      data: updateData,
    });

    res.json(updatedStop);
  } catch (error) {
    console.error('Update stop error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Delete a stop
 * @route DELETE /api/stops/:id
 * @access Private/Admin
 */
const deleteStop = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    // Check if stop exists
    const stop = await prisma.stop.findUnique({
      where: { id: req.params.id },
    });

    if (!stop) {
      return res.status(404).json({ message: 'Stop not found' });
    }

    // Delete stop
    await prisma.stop.delete({
      where: { id: req.params.id },
    });

    // Reorder remaining stops
    const remainingStops = await prisma.stop.findMany({
      where: { 
        routeId: stop.routeId,
        stopOrder: {
          gt: stop.stopOrder,
        },
      },
      orderBy: { stopOrder: 'asc' },
    });

    // Update stop orders
    for (const remainingStop of remainingStops) {
      await prisma.stop.update({
        where: { id: remainingStop.id },
        data: { stopOrder: remainingStop.stopOrder - 1 },
      });
    }

    res.json({ message: 'Stop removed successfully' });
  } catch (error) {
    console.error('Delete stop error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get estimated arrivals for a stop
 * @route GET /api/stops/:id/arrivals
 * @access Public
 */
const getStopArrivals = async (req, res) => {
  try {
    // Check if stop exists
    const stop = await prisma.stop.findUnique({
      where: { id: req.params.id },
    });

    if (!stop) {
      return res.status(404).json({ message: 'Stop not found' });
    }

    // Get estimated arrivals
    const arrivals = await prisma.busArrivalEstimate.findMany({
      where: { 
        stopId: req.params.id,
        estimatedArrivalTime: {
          gte: new Date(),
        },
      },
      include: {
        stop: {
          select: {
            id: true,
            stopName: true,
            stopCode: true,
          },
        },
      },
      orderBy: { estimatedArrivalTime: 'asc' },
    });

    res.json(arrivals);
  } catch (error) {
    console.error('Get stop arrivals error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Add estimated arrival for a stop
 * @route POST /api/stops/:id/arrivals
 * @access Private/Admin
 */
const addStopArrival = async (req, res) => {
  try {
    // Check if user is admin or driver
    if (req.user.role !== 'admin' && req.user.role !== 'driver') {
      return res.status(403).json({ message: 'Not authorized' });
    }

    const { 
      busId, 
      estimatedArrivalTime,
      status,
      delayMinutes
    } = req.body;

    // Validate required fields
    if (!busId || !estimatedArrivalTime) {
      return res.status(400).json({ 
        message: 'Please provide busId and estimatedArrivalTime' 
      });
    }

    // Check if stop exists
    const stop = await prisma.stop.findUnique({
      where: { id: req.params.id },
    });

    if (!stop) {
      return res.status(404).json({ message: 'Stop not found' });
    }

    // Check if bus exists
    const bus = await prisma.bus.findUnique({
      where: { id: busId },
    });

    if (!bus) {
      return res.status(404).json({ message: 'Bus not found' });
    }

    // If user is driver, check if they are assigned to this bus
    if (req.user.role === 'driver') {
      const driver = await prisma.driver.findUnique({
        where: { userId: req.user.id },
      });

      if (!driver || driver.assignedBusId !== busId) {
        return res.status(403).json({ message: 'You are not authorized to update arrivals for this bus' });
      }
    }

    // Create arrival estimate
    const arrival = await prisma.busArrivalEstimate.create({
      data: {
        busId,
        stopId: req.params.id,
        estimatedArrivalTime: new Date(estimatedArrivalTime),
        status: status || 'scheduled',
        delayMinutes: delayMinutes ? parseInt(delayMinutes) : null,
      },
    });

    res.status(201).json(arrival);
  } catch (error) {
    console.error('Add stop arrival error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  getAllStops,
  getStopById,
  updateStop,
  deleteStop,
  getStopArrivals,
  addStopArrival,
};
