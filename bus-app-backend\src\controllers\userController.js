const { prisma } = require('../config/db');
const { generateToken } = require('../utils/jwt');
const { hashPassword, comparePassword } = require('../utils/password');
const firebaseService = require('../services/firebaseService');

/**
 * Register a new user
 * @route POST /api/users/register
 * @access Public
 */
const registerUser = async (req, res) => {
  try {
    const { name, email, password, phone, role } = req.body;

    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({ message: 'Email and password are required' });
    }

    // Check if user already exists
    const userExists = await prisma.user.findUnique({
      where: { email },
    });

    if (userExists) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user with enhanced fields
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        phone,
        // Only allow setting role to 'driver' or 'user', admin must be set manually
        role: role === 'driver' ? 'driver' : 'user',
        lastLoginAt: new Date(),
      },
    });

    if (user) {
      // Create initial location record if user is a driver
      if (user.role === 'driver') {
        await prisma.driver.create({
          data: {
            userId: user.id,
            licenseNo: 'TBD', // This should be updated later
            phone: phone || '',
          },
        });
      }

      // Create default notification preferences
      await prisma.notificationPreference.create({
        data: {
          userId: user.id,
          enabled: true,
          loginAlerts: true,
          welcomeMessage: true,
          serviceUpdates: true,
          busDelays: true,
          promotions: true,
        },
      });

      // Send welcome notification if device token is provided
      if (req.body.deviceToken) {
        // Update user with device token
        await prisma.user.update({
          where: { id: user.id },
          data: { deviceToken: req.body.deviceToken },
        });

        // Send welcome notification
        await firebaseService.sendWelcomeNotification(user.id, user.name);
      }

      res.status(201).json({
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        token: generateToken(user.id),
      });
    } else {
      res.status(400).json({ message: 'Invalid user data' });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Authenticate user & get token
 * @route POST /api/users/login
 * @access Public
 */
const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({ message: 'Email and password are required' });
    }

    // Check for user email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({ message: 'Account is deactivated. Please contact support.' });
    }

    // Check password
    const isMatch = await comparePassword(password, user.password);

    if (!isMatch) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    // Update last login time and device token if provided
    const updateData = { lastLoginAt: new Date() };
    if (req.body.deviceToken) {
      updateData.deviceToken = req.body.deviceToken;
    }

    await prisma.user.update({
      where: { id: user.id },
      data: updateData,
    });

    // Get user location if exists
    const userLocation = await prisma.userLocation.findUnique({
      where: { userId: user.id },
      select: {
        latitude: true,
        longitude: true,
        updatedAt: true,
      },
    });

    // Check notification preferences for login alerts
    const notificationPreference = await prisma.notificationPreference.findUnique({
      where: { userId: user.id },
    });

    // Send login notification if preferences allow and device token exists
    if (user.deviceToken &&
        (!notificationPreference ||
         (notificationPreference.enabled && notificationPreference.loginAlerts))) {
      // Get device info from request headers or body
      const deviceInfo = req.body.deviceInfo ||
                         req.headers['user-agent'] ||
                         'an unknown device';

      // Send login notification
      await firebaseService.sendLoginNotification(user.id, deviceInfo);
    }

    res.json({
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      profilePicture: user.profilePicture,
      location: userLocation,
      token: generateToken(user.id),
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get user profile
 * @route GET /api/users/profile
 * @access Private
 */
const getUserProfile = async (req, res) => {
  try {
    // Get user with more detailed information
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        profilePicture: true,
        role: true,
        isActive: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get user location if exists
    const userLocation = await prisma.userLocation.findUnique({
      where: { userId: req.user.id },
      select: {
        latitude: true,
        longitude: true,
        updatedAt: true,
      },
    });

    // Get driver information if user is a driver
    let driverInfo = null;
    if (user.role === 'driver') {
      driverInfo = await prisma.driver.findUnique({
        where: { userId: req.user.id },
        select: {
          id: true,
          licenseNo: true,
          phone: true,
          licenseExpiry: true,
          experience: true,
          rating: true,
          status: true,
          assignedBusId: true,
        },
      });

      // If driver has an assigned bus, get bus details
      if (driverInfo && driverInfo.assignedBusId) {
        const bus = await prisma.bus.findUnique({
          where: { id: driverInfo.assignedBusId },
          select: {
            id: true,
            busNumber: true,
            busName: true,
            status: true,
          },
        });
        // Create a new object with the bus info to avoid TypeScript issues
        driverInfo = {
          ...driverInfo,
          assignedBus: bus
        };
      }
    }

    // Get favorite routes
    const favoriteRoutes = await prisma.favoriteRoute.findMany({
      where: { userId: req.user.id },
      select: {
        id: true,
        route: {
          select: {
            id: true,
            routeName: true,
            routeNumber: true,
            startPoint: true,
            endPoint: true,
          },
        },
      },
    });

    // Return comprehensive user profile
    res.json({
      ...user,
      location: userLocation || null,
      driverInfo: driverInfo || null,
      favoriteRoutes: favoriteRoutes.map(fr => ({
        id: fr.id,
        route: fr.route,
      })),
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update user profile
 * @route PUT /api/users/profile
 * @access Private
 */
const updateUserProfile = async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const { name, email, password, phone, profilePicture, deviceToken } = req.body;

    // Prepare update data
    const updateData = {};
    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (profilePicture) updateData.profilePicture = profilePicture;
    if (deviceToken) updateData.deviceToken = deviceToken;

    // Email change requires validation to ensure uniqueness
    if (email && email !== user.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email },
      });

      if (emailExists) {
        return res.status(400).json({ message: 'Email already in use' });
      }

      updateData.email = email;
    }

    // Password change
    if (password) {
      updateData.password = await hashPassword(password);
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: req.user.id },
      data: updateData,
    });

    // Update user location if provided
    const { latitude, longitude } = req.body;
    if (latitude && longitude) {
      // Check if user location exists
      const existingLocation = await prisma.userLocation.findUnique({
        where: { userId: req.user.id },
      });

      if (existingLocation) {
        // Update existing location
        await prisma.userLocation.update({
          where: { userId: req.user.id },
          data: {
            latitude: parseFloat(latitude),
            longitude: parseFloat(longitude),
          },
        });
      } else {
        // Create new location
        await prisma.userLocation.create({
          data: {
            userId: req.user.id,
            latitude: parseFloat(latitude),
            longitude: parseFloat(longitude),
          },
        });
      }
    }

    // Get updated user location
    const userLocation = await prisma.userLocation.findUnique({
      where: { userId: req.user.id },
      select: {
        latitude: true,
        longitude: true,
        updatedAt: true,
      },
    });

    res.json({
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      phone: updatedUser.phone,
      profilePicture: updatedUser.profilePicture,
      role: updatedUser.role,
      location: userLocation,
      token: generateToken(updatedUser.id),
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get all users
 * @route GET /api/users
 * @access Private/Admin
 */
const getUsers = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      },
    });

    res.json(users);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update user password
 * @route PUT /api/users/password
 * @access Private
 */
const updatePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Validate required fields
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ message: 'Current password and new password are required' });
    }

    // Get user with password
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify current password
    const isMatch = await comparePassword(currentPassword, user.password);
    if (!isMatch) {
      return res.status(401).json({ message: 'Current password is incorrect' });
    }

    // Check if new password is same as old password
    if (currentPassword === newPassword) {
      return res.status(400).json({ message: 'New password must be different from current password' });
    }

    // Hash new password
    const hashedPassword = await hashPassword(newPassword);

    // Update password
    await prisma.user.update({
      where: { id: req.user.id },
      data: { password: hashedPassword },
    });

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Update password error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  updatePassword,
  getUsers,
};
