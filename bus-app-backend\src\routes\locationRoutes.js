const express = require('express');
const router = express.Router();
const locationController = require('../controllers/locationController');
const { authMiddleware } = require('../middleware/authMiddleware');
const { adminMiddleware } = require('../middleware/adminMiddleware');

/**
 * @route   POST /api/locations/user
 * @desc    Update user location
 * @access  Private
 */
router.post('/user', authMiddleware, locationController.updateUserLocation);

/**
 * @route   GET /api/locations/nearby-buses
 * @desc    Get nearby buses
 * @access  Private
 */
router.get('/nearby-buses', authMiddleware, locationController.getNearbyBuses);

/**
 * @route   GET /api/locations/nearby-stops
 * @desc    Get nearby stops
 * @access  Private
 */
router.get('/nearby-stops', authMiddleware, locationController.getNearbyStops);

/**
 * @route   POST /api/locations/process-passenger-data
 * @desc    Process passenger contributions for all buses
 * @access  Private/Admin
 */
router.post('/process-passenger-data', adminMiddleware, locationController.processAllPassengerContributions);

/**
 * @route   GET /api/locations/passenger-contributions/:busId
 * @desc    Get passenger location contributions for a bus
 * @access  Private/Admin
 */
router.get('/passenger-contributions/:busId', adminMiddleware, locationController.getPassengerContributions);

module.exports = router;
