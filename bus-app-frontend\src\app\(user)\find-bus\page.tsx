// src/app/(user)/find-bus/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { BusMap } from '@/components/client/map/BusMap';
import { BusCard } from '@/components/client/bus/BusCard';
import { Button } from '@/components/ui/button';
import { MapPinIcon, RefreshCwIcon } from 'lucide-react';
import { useBusLocation } from '@/hooks/useBusLocation';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function FindBusPage() {
  const [selectedBusId, setSelectedBusId] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const { isAuthenticated } = useAuth();

  // Use our custom hook to manage bus location data
  const { buses, loading, error, refreshBuses } = useBusLocation({
    initialLocation: userLocation || undefined,
    radius: 2000 // 2km radius
  });

  // Function to get user's location
  const getUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          };
          setUserLocation(newLocation);

          // If we already have location and buses, refresh with new location
          if (isAuthenticated && newLocation) {
            refreshBuses({
              latitude: newLocation.latitude,
              longitude: newLocation.longitude,
              radius: 2000
            });
          }
        },
        (error) => {
          console.error('Error getting location:', error);
          toast.error('Could not get your location. Please check your location permissions.');
        }
      );
    } else {
      console.error('Geolocation is not supported by this browser.');
      toast.error('Your browser does not support geolocation.');
    }
  };

  // Get user location on component mount
  useEffect(() => {
    getUserLocation();
  }, []);

  // No need for manual redirect, ProtectedRoute handles it

  // Show error toast if there's an error
  useEffect(() => {
    if (error) {
      toast.error(`Error: ${error.message}`);
    }
  }, [error]);

  // Handle bus selection
  const handleBusSelect = (busId: string) => {
    setSelectedBusId(busId === selectedBusId ? null : busId);
  };

  // Refresh bus data
  const handleRefresh = () => {
    if (!userLocation) {
      getUserLocation();
      return;
    }

    refreshBuses({
      latitude: userLocation.latitude,
      longitude: userLocation.longitude,
      radius: 2000
    });

    toast.success('Refreshing bus locations...');
  };

  return (
    <ProtectedRoute>
      <div className="container mx-auto py-6 px-4 md:px-6">
        <div className="flex flex-col space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold">Find Bus</h1>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={getUserLocation}
                disabled={loading}
              >
                <MapPinIcon className="h-4 w-4 mr-2" />
                My Location
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading}
              >
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

          {/* Map Section */}
          <div className="h-[400px] w-full rounded-xl border overflow-hidden">
            <BusMap
              key="bus-map" // Add a stable key to prevent re-mounting
              buses={buses}
              selectedBusId={selectedBusId}
              userLocation={userLocation}
              onBusSelect={handleBusSelect}
            />
          </div>

          {/* Bus Cards Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {loading ? (
              // Loading skeleton
              Array(3)
                .fill(0)
                .map((_, index) => (
                  <div
                    key={index}
                    className="h-40 rounded-xl border bg-card animate-pulse"
                  />
                ))
            ) : buses.length > 0 ? (
              buses.map((bus) => (
                <BusCard
                  key={bus.id}
                  bus={bus}
                  isSelected={selectedBusId === bus.id}
                  onSelect={() => handleBusSelect(bus.id)}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-10">
                <p className="text-muted-foreground">No buses found nearby.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
