/**
 * A simple fallback router for Leaflet Routing Machine
 * This is used when the OSRM service is unavailable or rate limited
 */

// Define the router factory function
export function createFallbackRouter() {
  // Create a router object that implements the Leaflet Routing Machine router interface
  const router = {
    // Route method that creates a straight line between waypoints
    route: function(waypoints: any[], callback: Function) {
      try {
        console.log("Fallback router called with waypoints:", waypoints);

        // Add a small delay to simulate network request and prevent rapid calls
        setTimeout(() => {
          // Filter out undefined waypoints and ensure they have valid latLng
          const validWaypoints = waypoints.filter(wp => {
            return wp && wp.latLng &&
                   typeof wp.latLng.lat === 'number' &&
                   typeof wp.latLng.lng === 'number';
          });

        console.log("Valid waypoints:", validWaypoints);

        if (validWaypoints.length < 2) {
          console.warn("Not enough valid waypoints for routing");
          callback(new Error('At least two valid waypoints are required'), []);
          return this;
        }

        // Create a simple route with straight lines
        const coordinates = validWaypoints.map(wp => wp.latLng);

        // Create a simple instruction for each segment
        const instructions = [];
        for (let i = 0; i < validWaypoints.length - 1; i++) {
          instructions.push({
            type: 'Straight',
            distance: calculateDistance(
              validWaypoints[i].latLng.lat,
              validWaypoints[i].latLng.lng,
              validWaypoints[i+1].latLng.lat,
              validWaypoints[i+1].latLng.lng
            ),
            time: 0,
            road: 'Direct route',
            direction: 'Straight',
            index: i
          });
        }

        // Calculate simple distance and time
        let totalDistance = 0;
        for (let i = 1; i < validWaypoints.length; i++) {
          const prev = validWaypoints[i - 1].latLng;
          const current = validWaypoints[i].latLng;
          totalDistance += calculateDistance(prev.lat, prev.lng, current.lat, current.lng);
        }

        const route = {
          name: 'Direct route',
          coordinates: coordinates,
          waypoints: validWaypoints,
          inputWaypoints: waypoints,
          instructions: instructions,
          summary: {
            totalDistance: totalDistance,
            totalTime: totalDistance / 50 * 3600 // Rough estimate: 50 km/h
          }
        };

        console.log("Created fallback route:", route);

          // Call the callback with the route
          callback(null, [route]);
        }, 500); // 500ms delay to prevent rapid calls
      } catch (error) {
        console.error("Error in fallback router:", error);
        setTimeout(() => {
          callback(error, []);
        }, 500);
      }

      return this;
    }
  };

  return router;
}

// Calculate distance between two points in kilometers using the Haversine formula
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  return distance;
}

function deg2rad(deg: number): number {
  return deg * (Math.PI/180);
}
