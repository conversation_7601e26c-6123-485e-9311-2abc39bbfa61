# WebSocket Bus Tracking Testing Guide

This guide provides step-by-step instructions for testing the WebSocket-based real-time bus tracking system.

## Prerequisites

Before you begin testing, make sure you have:

1. Node.js and npm installed
2. The bus-app-backend server running
3. MongoDB connected and working
4. At least one bus created in the database
5. At least one driver user account
6. At least 3 passenger user accounts for testing

## Step 1: Install Dependencies for Test Scripts

The test scripts require axios for making HTTP requests:

```bash
npm install axios
```

## Step 2: Obtain Authentication Tokens

You'll need JWT tokens for authentication. You can get these by logging in with your test accounts:

1. **Driver Token**: Log in with a driver account
2. **Passenger Tokens**: Log in with passenger accounts

You can use the following API endpoint to log in and get tokens:

```bash
POST /api/users/login
{
  "email": "<EMAIL>",
  "password": "password"
}
```

The response will include a JWT token that you can use for authentication.

## Step 3: Configure Test Scripts

1. Open `examples/test-bus-movement.js` and update the configuration:
   - Set `token` to your driver's JWT token
   - Set `busId` to the ID of the bus you want to simulate
   - Adjust other settings as needed (route points, update interval, etc.)

2. Open `examples/test-passenger-contributions.js` and update the configuration:
   - Set `passengerTokens` to your passenger JWT tokens
   - Adjust the `baseLocation` to match your test area
   - Modify other settings as needed

## Step 4: Start the WebSocket Client

1. Open `examples/websocket-client.html` in your web browser
2. Enter your server URL (e.g., `http://localhost:5000`)
3. Enter a JWT token (can be any of your test tokens)
4. Click "Connect" to establish the WebSocket connection

## Step 5: Test Driver Updates

1. Run the bus movement simulation script:

```bash
node examples/test-bus-movement.js
```

2. In the WebSocket client:
   - Enter the bus ID in the "Bus ID" field
   - Click "Subscribe" to subscribe to updates for this bus
   - Watch the map and event log for real-time updates

The script will simulate a bus moving along the defined route and send location updates to the server. The WebSocket client should receive these updates in real-time and display the bus on the map.

## Step 6: Test Passenger Contributions

1. Stop the bus movement simulation (Ctrl+C)
2. Run the passenger contributions script:

```bash
node examples/test-passenger-contributions.js
```

3. In the WebSocket client:
   - Make sure you're still subscribed to the bus
   - Watch the map and event log for passenger contributions and aggregated bus locations

The script will simulate multiple passengers on the bus sending their location data. The server should process these contributions and create aggregated bus locations when enough reliable contributions are available.

## Step 7: Test Nearby Buses

1. In the WebSocket client:
   - Click "Subscribe to Nearby" to subscribe to buses near your current location
   - Watch the map and event log for updates about nearby buses

This will use your browser's geolocation to find your current position and subscribe to updates for buses near that location.

## Step 8: Test Route Subscriptions

1. In the WebSocket client:
   - Enter a route ID in the "Route ID" field
   - Click "Subscribe" to subscribe to all buses on this route
   - Watch the map and event log for updates about buses on the route

## Step 9: Test WebSocket Reconnection

1. Stop your server
2. Observe the WebSocket client disconnecting
3. Restart your server
4. Observe the WebSocket client automatically reconnecting

## Step 10: Test Multiple Clients

1. Open the WebSocket client in multiple browser windows or tabs
2. Connect each client with different tokens
3. Subscribe to different buses or routes in each client
4. Run the test scripts and observe updates in all clients

## Troubleshooting

### Connection Issues

If you're having trouble connecting to the WebSocket server:

1. Make sure your server is running
2. Check that the server URL is correct
3. Verify that your JWT token is valid
4. Check the browser console for errors
5. Check the server logs for errors

### Missing Updates

If you're not receiving updates:

1. Make sure you're subscribed to the correct bus or route
2. Check that the test scripts are running and sending updates
3. Verify that the WebSocket connection is established
4. Check the server logs for errors processing updates

### Map Issues

If the map is not displaying correctly:

1. Make sure you have an internet connection (required for OpenStreetMap tiles)
2. Check the browser console for errors
3. Verify that the latitude and longitude values are valid

## Advanced Testing

### Testing with Real Devices

For more realistic testing, you can use the WebSocket client on mobile devices:

1. Make sure your server is accessible from your mobile network
2. Open the WebSocket client on your mobile browser
3. Connect and subscribe as usual
4. Allow location access when prompted
5. Move around with your device to test real-world scenarios

### Load Testing

To test how the system handles many concurrent connections:

1. Create a script that opens multiple WebSocket connections
2. Simulate many buses sending location updates
3. Monitor server performance and resource usage

### Failure Recovery Testing

To test how the system recovers from failures:

1. Simulate network interruptions
2. Stop and restart the server during operation
3. Verify that clients reconnect and continue receiving updates

## Conclusion

By following this testing guide, you should be able to thoroughly test all aspects of the WebSocket-based real-time bus tracking system. If you encounter any issues, check the server logs and browser console for error messages that can help identify the problem.
