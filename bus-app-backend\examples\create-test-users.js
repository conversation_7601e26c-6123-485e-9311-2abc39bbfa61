/**
 * <PERSON><PERSON><PERSON> to create test users for WebSocket testing
 */

const axios = require('axios');

// Configuration
const config = {
  serverUrl: 'http://localhost:5000',
  users: [
    {
      name: 'Test Driver',
      email: '<EMAIL>',
      password: 'password123',
      role: 'driver'
    },
    {
      name: 'Test Passenger 1',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user'
    },
    {
      name: 'Test Passenger 2',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user'
    },
    {
      name: 'Test Passenger 3',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user'
    }
  ]
};

async function createUsers() {
  console.log('Creating test users...');
  
  const results = [];
  
  for (const user of config.users) {
    try {
      console.log(`\nCreating user: ${user.name} (${user.email})`);
      
      // Try to register the user
      const response = await axios.post(`${config.serverUrl}/api/users/register`, user);
      
      console.log('✅ User created successfully');
      console.log('User ID:', response.data.id);
      console.log('Token:', response.data.token);
      
      results.push({
        name: user.name,
        email: user.email,
        id: response.data.id,
        token: response.data.token,
        role: user.role
      });
    } catch (error) {
      console.log('❌ Failed to create user');
      
      if (error.response && error.response.data.message.includes('already exists')) {
        console.log('User already exists, trying to log in...');
        
        try {
          // Try to log in
          const loginResponse = await axios.post(`${config.serverUrl}/api/users/login`, {
            email: user.email,
            password: user.password
          });
          
          console.log('✅ Login successful');
          console.log('User ID:', loginResponse.data.id);
          console.log('Token:', loginResponse.data.token);
          
          results.push({
            name: user.name,
            email: user.email,
            id: loginResponse.data.id,
            token: loginResponse.data.token,
            role: loginResponse.data.role
          });
        } catch (loginError) {
          console.error('❌ Login failed:', loginError.message);
          if (loginError.response) {
            console.error('Server response:', loginError.response.data);
          }
        }
      } else {
        console.error('Error details:', error.message);
        if (error.response) {
          console.error('Server response:', error.response.data);
        }
      }
    }
  }
  
  // Print summary
  console.log('\n=== TEST USERS SUMMARY ===');
  for (const result of results) {
    console.log(`\n${result.name} (${result.role})`);
    console.log(`Email: ${result.email}`);
    console.log(`ID: ${result.id}`);
    console.log(`Token: ${result.token.substring(0, 20)}...`);
  }
  
  // Print configuration for test scripts
  console.log('\n=== CONFIGURATION FOR TEST SCRIPTS ===');
  
  // Find driver
  const driver = results.find(user => user.role === 'driver');
  if (driver) {
    console.log('\nFor test-bus-movement.js:');
    console.log(`token: '${driver.token}',`);
  }
  
  // Find passengers
  const passengers = results.filter(user => user.role === 'user');
  if (passengers.length > 0) {
    console.log('\nFor test-passenger-contributions.js:');
    console.log('passengerTokens: [');
    for (const passenger of passengers) {
      console.log(`  '${passenger.token}',`);
    }
    console.log('],');
  }
  
  // For WebSocket connection test
  if (results.length > 0) {
    console.log('\nFor test-websocket-connection.js and test-auth.js:');
    console.log(`token: '${results[0].token}',`);
  }
}

// Run the script
createUsers();
