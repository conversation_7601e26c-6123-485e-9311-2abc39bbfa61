'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { PlusIcon, MapIcon, RefreshCwIcon } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Link from 'next/link';
import { Route, routeService } from '@/services/routeService';
import { toast } from 'sonner';

export default function RoutesManagementPage() {
  const { user } = useAuth();
  const [routes, setRoutes] = useState<Route[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRoutes = async () => {
    try {
      setLoading(true);
      const data = await routeService.getRoutes();
      setRoutes(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching routes:', err);
      setError('Failed to load routes. Please try again.');
      toast.error('Failed to load routes');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoutes();
  }, []);

  return (
    <ProtectedRoute allowedRoles={['admin']}>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Route Management</h1>
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchRoutes} disabled={loading}>
              <RefreshCwIcon className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button asChild>
              <Link href="/admin/dashboard/routes/create">
                <PlusIcon className="mr-2 h-4 w-4" />
                Add New Route
              </Link>
            </Button>
          </div>
        </div>

        {error && (
          <div className="bg-destructive/15 text-destructive p-4 rounded-md mb-6">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        ) : routes.length === 0 ? (
          <div className="bg-card rounded-lg shadow-sm p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">No Routes Found</h2>
            <p className="text-muted-foreground mb-6">
              There are no routes in the system yet. Create your first route to get started.
            </p>
            <Button asChild>
              <Link href="/admin/dashboard/routes/create">
                <PlusIcon className="mr-2 h-4 w-4" />
                Create First Route
              </Link>
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6">
            {routes.map((route) => (
              <div key={route.id} className="bg-card rounded-lg shadow-sm p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-medium">
                      {route.routeNumber ? `${route.routeNumber} - ` : ''}{route.routeName}
                    </h3>
                    <p className="text-muted-foreground mt-1">
                      {route.startPoint} to {route.endPoint}
                    </p>
                    <div className="flex gap-4 mt-2">
                      <span className="text-sm">
                        Distance: {route.distanceKm} km
                      </span>
                      <span className="text-sm">
                        Est. Time: {route.estimatedTime} min
                      </span>
                      <span className="text-sm">
                        Buses: {route._count?.buses || 0}
                      </span>
                    </div>
                    {route.description && (
                      <p className="text-sm mt-2">{route.description}</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" asChild>
                      <Link href={`/admin/dashboard/routes/${route.id}`}>
                        View Details
                      </Link>
                    </Button>
                    <Button variant="outline" asChild>
                      <Link href={`/admin/dashboard/routes/${route.id}/edit`}>
                        Edit
                      </Link>
                    </Button>
                  </div>
                </div>
                {route.polyline ? (
                  <div className="mt-4 flex items-center text-sm text-green-600">
                    <MapIcon className="h-4 w-4 mr-1" />
                    Route path configured
                  </div>
                ) : (
                  <div className="mt-4 flex items-center text-sm text-amber-600">
                    <MapIcon className="h-4 w-4 mr-1" />
                    No route path configured
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
