'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeftIcon, SaveIcon } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { RoutingMap } from '@/components/admin/map/RoutingMap';
import Link from 'next/link';
import { toast } from 'sonner';
import { CreateRouteData, routeService } from '@/services/routeService';

export default function CreateRoutePage() {
  const router = useRouter();
  const { user } = useAuth();

  const [formData, setFormData] = useState<CreateRouteData>({
    routeName: '',
    routeNumber: '',
    startPoint: '',
    endPoint: '',
    distanceKm: 0,
    estimatedTime: 0,
    description: '',
    color: '#3b82f6', // Default blue color
    polyline: '',
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Handle numeric values
    if (name === 'distanceKm' || name === 'estimatedTime') {
      setFormData(prev => ({
        ...prev,
        [name]: value === '' ? 0 : parseFloat(value)
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handlePolylineChange = (polyline: string) => {
    setFormData(prev => ({ ...prev, polyline }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      // Validate form
      if (!formData.routeName || !formData.startPoint || !formData.endPoint ||
          formData.distanceKm <= 0 || formData.estimatedTime <= 0) {
        setError('Please fill in all required fields');
        return;
      }

      // Create route using the service
      const response = await routeService.createRoute(formData);

      toast.success('Route created successfully');

      // Redirect to route details page
      router.push(`/admin/dashboard/routes/${response.id}`);
    } catch (err: any) {
      console.error('Error creating route:', err);
      setError(err.message || 'Failed to create route. Please try again.');
      toast.error('Failed to create route');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute allowedRoles={['admin']}>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center">
            <Button variant="ghost" size="icon" asChild className="mr-2">
              <Link href="/admin/dashboard/routes">
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Create New Route</h1>
          </div>
        </div>

        {error && (
          <div className="bg-destructive/15 text-destructive p-4 rounded-md mb-6">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="routeName">Route Name <span className="text-destructive">*</span></Label>
                <Input
                  id="routeName"
                  name="routeName"
                  value={formData.routeName}
                  onChange={handleChange}
                  placeholder="e.g. Mumbai Central to Andheri"
                  required
                />
              </div>

              <div>
                <Label htmlFor="routeNumber">Route Number</Label>
                <Input
                  id="routeNumber"
                  name="routeNumber"
                  value={formData.routeNumber}
                  onChange={handleChange}
                  placeholder="e.g. R123"
                />
              </div>

              <div>
                <Label htmlFor="startPoint">Start Point <span className="text-destructive">*</span></Label>
                <Input
                  id="startPoint"
                  name="startPoint"
                  value={formData.startPoint}
                  onChange={handleChange}
                  placeholder="e.g. Mumbai Central"
                  required
                />
              </div>

              <div>
                <Label htmlFor="endPoint">End Point <span className="text-destructive">*</span></Label>
                <Input
                  id="endPoint"
                  name="endPoint"
                  value={formData.endPoint}
                  onChange={handleChange}
                  placeholder="e.g. Andheri"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="distanceKm">Distance (km) <span className="text-destructive">*</span></Label>
                  <Input
                    id="distanceKm"
                    name="distanceKm"
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.distanceKm}
                    onChange={handleChange}
                    placeholder="e.g. 15.5"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="estimatedTime">Est. Time (min) <span className="text-destructive">*</span></Label>
                  <Input
                    id="estimatedTime"
                    name="estimatedTime"
                    type="number"
                    min="1"
                    value={formData.estimatedTime}
                    onChange={handleChange}
                    placeholder="e.g. 45"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="color">Route Color</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="color"
                    name="color"
                    type="color"
                    value={formData.color}
                    onChange={handleChange}
                    className="w-12 h-10 p-1"
                  />
                  <Input
                    value={formData.color}
                    onChange={handleChange}
                    name="color"
                    className="flex-1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Enter route description"
                  rows={4}
                />
              </div>
            </div>

            <div className="space-y-4">
              <Label>Route Path</Label>
              <RoutingMap
                onPolylineChange={handlePolylineChange}
                center={[19.0760, 72.8777]} // Mumbai coordinates
                zoom={12}
              />
              <p className="text-sm text-muted-foreground">
                Click on the map to add waypoints. Drag waypoints to adjust the route. The route will automatically follow roads.
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-4">
            <Button
              variant="outline"
              type="button"
              onClick={() => router.push('/admin/dashboard/routes')}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Creating...
                </>
              ) : (
                <>
                  <SaveIcon className="mr-2 h-4 w-4" />
                  Create Route
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </ProtectedRoute>
  );
}
