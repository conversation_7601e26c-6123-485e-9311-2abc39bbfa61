import { apiRequest } from '@/lib/api';

export interface Route {
  id: string;
  routeName: string;
  routeNumber?: string;
  startPoint: string;
  endPoint: string;
  distanceKm: number;
  estimatedTime: number;
  description?: string;
  isActive: boolean;
  color?: string;
  polyline?: string;
  createdAt: string;
  updatedAt: string;
  stops?: Stop[];
  buses?: Bus[];
  _count?: {
    buses: number;
    stops: number;
  };
}

export interface Stop {
  id: string;
  stopName: string;
  stopCode?: string;
  latitude: number;
  longitude: number;
  stopOrder: number;
  isActive: boolean;
  description?: string;
  amenities?: string[];
}

export interface Bus {
  id: string;
  busNumber: string;
  busName: string;
  status: string;
  isActive: boolean;
  currentLocation?: {
    latitude: number;
    longitude: number;
    timestamp: string;
  };
}

export interface CreateRouteData {
  routeName: string;
  routeNumber?: string;
  startPoint: string;
  endPoint: string;
  distanceKm: number;
  estimatedTime: number;
  description?: string;
  color?: string;
  polyline?: string;
}

export interface UpdateRouteData extends CreateRouteData {
  isActive?: boolean;
}

export const routeService = {
  getRoutes: async (): Promise<Route[]> => {
    return apiRequest<Route[]>('/routes');
  },

  getRoute: async (id: string): Promise<Route> => {
    return apiRequest<Route>(`/routes/${id}`);
  },

  createRoute: async (data: CreateRouteData): Promise<Route> => {
    return apiRequest<Route>('/routes', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  updateRoute: async (id: string, data: UpdateRouteData): Promise<Route> => {
    return apiRequest<Route>(`/routes/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  deleteRoute: async (id: string): Promise<void> => {
    return apiRequest<void>(`/routes/${id}`, {
      method: 'DELETE',
    });
  },
};
