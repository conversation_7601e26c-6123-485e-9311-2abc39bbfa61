const { prisma } = require('../config/db');

/**
 * Get all routes
 * @route GET /api/routes
 * @access Private
 */
const getAllRoutes = async (req, res) => {
  try {
    const { isActive } = req.query;

    // Build filter object
    const filter = {};
    if (isActive !== undefined) filter.isActive = isActive === 'true';

    const routes = await prisma.route.findMany({
      where: filter,
      include: {
        stops: {
          select: {
            id: true,
            stopName: true,
            stopCode: true,
            latitude: true,
            longitude: true,
            stopOrder: true,
          },
          orderBy: {
            stopOrder: 'asc',
          },
        },
        _count: {
          select: {
            buses: true,
          },
        },
      },
      orderBy: {
        routeName: 'asc',
      },
    });

    res.json(routes);
  } catch (error) {
    console.error('Get all routes error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get route by ID
 * @route GET /api/routes/:id
 * @access Private
 */
const getRouteById = async (req, res) => {
  try {
    const route = await prisma.route.findUnique({
      where: { id: req.params.id },
      include: {
        stops: {
          orderBy: {
            stopOrder: 'asc',
          },
        },
        buses: {
          select: {
            id: true,
            busNumber: true,
            busName: true,
            status: true,
            isActive: true,
          },
          where: {
            isActive: true,
          },
        },
      },
    });

    if (!route) {
      return res.status(404).json({ message: 'Route not found' });
    }

    // Get active buses on this route with their current locations
    const busesWithLocations = await Promise.all(
      route.buses.map(async (bus) => {
        const latestLocation = await prisma.busLocation.findFirst({
          where: { busId: bus.id },
          orderBy: { timestamp: 'desc' },
        });

        return {
          ...bus,
          currentLocation: latestLocation || null,
        };
      })
    );

    res.json({
      ...route,
      buses: busesWithLocations,
    });
  } catch (error) {
    console.error('Get route by ID error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Create a new route
 * @route POST /api/routes
 * @access Private/Admin
 */
const createRoute = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const {
      routeName,
      routeNumber,
      startPoint,
      endPoint,
      distanceKm,
      estimatedTime,
      description,
      color,
      polyline,
      stops
    } = req.body;

    // Validate required fields
    if (!routeName || !startPoint || !endPoint || !distanceKm || !estimatedTime) {
      return res.status(400).json({
        message: 'Please provide routeName, startPoint, endPoint, distanceKm, and estimatedTime'
      });
    }

    // Check if route number already exists (if provided)
    if (routeNumber) {
      const routeExists = await prisma.route.findUnique({
        where: { routeNumber },
      });

      if (routeExists) {
        return res.status(400).json({ message: 'Route with this number already exists' });
      }
    }

    // Create route
    const route = await prisma.route.create({
      data: {
        routeName,
        routeNumber,
        startPoint,
        endPoint,
        distanceKm: parseFloat(distanceKm),
        estimatedTime: parseInt(estimatedTime),
        description,
        color,
        polyline,
        isActive: true,
      },
    });

    // Create stops if provided
    if (stops && Array.isArray(stops) && stops.length > 0) {
      const stopsData = stops.map((stop, index) => ({
        stopName: stop.stopName,
        stopCode: stop.stopCode,
        latitude: parseFloat(stop.latitude),
        longitude: parseFloat(stop.longitude),
        stopOrder: stop.stopOrder || index + 1,
        isActive: true,
        description: stop.description,
        amenities: stop.amenities ? stop.amenities.split(',').map(a => a.trim()) : [],
        routeId: route.id,
      }));

      await prisma.stop.createMany({
        data: stopsData,
      });

      // Get the created route with stops
      const routeWithStops = await prisma.route.findUnique({
        where: { id: route.id },
        include: {
          stops: {
            orderBy: {
              stopOrder: 'asc',
            },
          },
        },
      });

      return res.status(201).json(routeWithStops);
    }

    res.status(201).json(route);
  } catch (error) {
    console.error('Create route error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update a route
 * @route PUT /api/routes/:id
 * @access Private/Admin
 */
const updateRoute = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const {
      routeName,
      routeNumber,
      startPoint,
      endPoint,
      distanceKm,
      estimatedTime,
      description,
      isActive,
      color,
      polyline
    } = req.body;

    // Check if route exists
    const route = await prisma.route.findUnique({
      where: { id: req.params.id },
    });

    if (!route) {
      return res.status(404).json({ message: 'Route not found' });
    }

    // If routeNumber is changed, check if it's unique
    if (routeNumber && routeNumber !== route.routeNumber) {
      const routeExists = await prisma.route.findUnique({
        where: { routeNumber },
      });

      if (routeExists) {
        return res.status(400).json({ message: 'Route with this number already exists' });
      }
    }

    // Prepare update data
    const updateData = {};
    if (routeName) updateData.routeName = routeName;
    if (routeNumber) updateData.routeNumber = routeNumber;
    if (startPoint) updateData.startPoint = startPoint;
    if (endPoint) updateData.endPoint = endPoint;
    if (distanceKm !== undefined) updateData.distanceKm = parseFloat(distanceKm);
    if (estimatedTime !== undefined) updateData.estimatedTime = parseInt(estimatedTime);
    if (description !== undefined) updateData.description = description;
    if (isActive !== undefined) updateData.isActive = isActive === true || isActive === 'true';
    if (color) updateData.color = color;
    if (polyline !== undefined) updateData.polyline = polyline;

    // Update route
    const updatedRoute = await prisma.route.update({
      where: { id: req.params.id },
      data: updateData,
    });

    res.json(updatedRoute);
  } catch (error) {
    console.error('Update route error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Delete a route
 * @route DELETE /api/routes/:id
 * @access Private/Admin
 */
const deleteRoute = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    // Check if route exists
    const route = await prisma.route.findUnique({
      where: { id: req.params.id },
      include: {
        buses: true,
        stops: true,
      },
    });

    if (!route) {
      return res.status(404).json({ message: 'Route not found' });
    }

    // Check if route has buses assigned
    if (route.buses.length > 0) {
      return res.status(400).json({
        message: 'Cannot delete route with buses assigned. Please reassign or remove buses first.'
      });
    }

    // Delete all stops associated with the route
    if (route.stops.length > 0) {
      await prisma.stop.deleteMany({
        where: { routeId: req.params.id },
      });
    }

    // Delete route
    await prisma.route.delete({
      where: { id: req.params.id },
    });

    res.json({ message: 'Route removed successfully' });
  } catch (error) {
    console.error('Delete route error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get all stops for a route
 * @route GET /api/routes/:id/stops
 * @access Public
 */
const getRouteStops = async (req, res) => {
  try {
    // Check if route exists
    const route = await prisma.route.findUnique({
      where: { id: req.params.id },
    });

    if (!route) {
      return res.status(404).json({ message: 'Route not found' });
    }

    // Get stops
    const stops = await prisma.stop.findMany({
      where: { routeId: req.params.id },
      orderBy: { stopOrder: 'asc' },
    });

    res.json(stops);
  } catch (error) {
    console.error('Get route stops error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Add a stop to a route
 * @route POST /api/routes/:id/stops
 * @access Private/Admin
 */
const addRouteStop = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const {
      stopName,
      stopCode,
      latitude,
      longitude,
      stopOrder,
      description,
      amenities
    } = req.body;

    // Validate required fields
    if (!stopName || !latitude || !longitude) {
      return res.status(400).json({
        message: 'Please provide stopName, latitude, and longitude'
      });
    }

    // Check if route exists
    const route = await prisma.route.findUnique({
      where: { id: req.params.id },
    });

    if (!route) {
      return res.status(404).json({ message: 'Route not found' });
    }

    // If stopOrder is provided, check if we need to reorder other stops
    if (stopOrder) {
      // Get all stops with order >= stopOrder
      const stopsToUpdate = await prisma.stop.findMany({
        where: {
          routeId: req.params.id,
          stopOrder: {
            gte: parseInt(stopOrder),
          },
        },
        orderBy: { stopOrder: 'asc' },
      });

      // Update stop orders
      for (const stop of stopsToUpdate) {
        await prisma.stop.update({
          where: { id: stop.id },
          data: { stopOrder: stop.stopOrder + 1 },
        });
      }
    } else {
      // If no stopOrder provided, get the highest order and add 1
      const lastStop = await prisma.stop.findFirst({
        where: { routeId: req.params.id },
        orderBy: { stopOrder: 'desc' },
      });

      req.body.stopOrder = lastStop ? lastStop.stopOrder + 1 : 1;
    }

    // Create stop
    const stop = await prisma.stop.create({
      data: {
        stopName,
        stopCode,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        stopOrder: parseInt(stopOrder || req.body.stopOrder),
        description,
        amenities: amenities ? amenities.split(',').map(a => a.trim()) : [],
        isActive: true,
        routeId: req.params.id,
      },
    });

    res.status(201).json(stop);
  } catch (error) {
    console.error('Add route stop error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get all buses for a route
 * @route GET /api/routes/:id/buses
 * @access Private
 */
const getRouteBuses = async (req, res) => {
  try {
    // Check if route exists
    const route = await prisma.route.findUnique({
      where: { id: req.params.id },
    });

    if (!route) {
      return res.status(404).json({ message: 'Route not found' });
    }

    // Get buses
    const buses = await prisma.bus.findMany({
      where: {
        routeId: req.params.id,
        isActive: true,
      },
      orderBy: { busNumber: 'asc' },
    });

    // Get current location for each bus
    const busesWithLocations = await Promise.all(
      buses.map(async (bus) => {
        const latestLocation = await prisma.busLocation.findFirst({
          where: { busId: bus.id },
          orderBy: { timestamp: 'desc' },
        });

        return {
          ...bus,
          currentLocation: latestLocation || null,
        };
      })
    );

    res.json(busesWithLocations);
  } catch (error) {
    console.error('Get route buses error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  getAllRoutes,
  getRouteById,
  createRoute,
  updateRoute,
  deleteRoute,
  getRouteStops,
  addRouteStop,
  getRouteBuses,
};
