const { prisma } = require('../config/db');
const locationUtils = require('../utils/locationUtils');
const { emitBusLocationUpdate, emitPassengerContribution } = require('../services/socketService');

/**
 * Update user location
 * @route POST /api/locations/user
 * @access Private
 */
const updateUserLocation = async (req, res) => {
  try {
    const { latitude, longitude, accuracy, altitude, speed, heading } = req.body;

    // Validate required fields
    if (!latitude || !longitude) {
      return res.status(400).json({ message: 'Latitude and longitude are required' });
    }

    // Parse location data
    const parsedLat = parseFloat(latitude);
    const parsedLon = parseFloat(longitude);
    const parsedAccuracy = accuracy ? parseFloat(accuracy) : null;
    const parsedAltitude = altitude ? parseFloat(altitude) : null;
    const parsedSpeed = speed ? parseFloat(speed) : null;
    const parsedHeading = heading ? parseFloat(heading) : null;

    // Check if user location exists
    const existingLocation = await prisma.userLocation.findUnique({
      where: { userId: req.user.id },
    });

    let location;

    if (existingLocation) {
      // Update existing location
      location = await prisma.userLocation.update({
        where: { userId: req.user.id },
        data: {
          latitude: parsedLat,
          longitude: parsedLon,
          accuracy: parsedAccuracy,
          altitude: parsedAltitude,
        },
      });
    } else {
      // Create new location
      location = await prisma.userLocation.create({
        data: {
          userId: req.user.id,
          latitude: parsedLat,
          longitude: parsedLon,
          accuracy: parsedAccuracy,
          altitude: parsedAltitude,
        },
      });
    }

    // If user is not a driver, check if they're on a bus and contribute to bus location tracking
    if (req.user.role !== 'driver') {
      // Find which bus the user is likely on
      const { busId, distance } = await locationUtils.findUserBus(parsedLat, parsedLon);

      // If user is likely on a bus (busId is not null), contribute their location
      if (busId) {
        // Create a passenger location contribution
        const contribution = await prisma.passengerLocationContribution.create({
          data: {
            userId: req.user.id,
            busId,
            latitude: parsedLat,
            longitude: parsedLon,
            speed: parsedSpeed,
            heading: parsedHeading,
            accuracy: parsedAccuracy,
            altitude: parsedAltitude,
            reliability: 0.5, // Default reliability, will be calculated during processing
          },
        });

        // Emit passenger contribution via WebSocket
        emitPassengerContribution(busId, {
          id: contribution.id,
          userId: contribution.userId,
          latitude: contribution.latitude,
          longitude: contribution.longitude,
          speed: contribution.speed,
          heading: contribution.heading,
          accuracy: contribution.accuracy,
          altitude: contribution.altitude,
          reliability: contribution.reliability,
          timestamp: contribution.timestamp,
        });

        // Process passenger contributions for this bus
        const processed = await locationUtils.processPassengerContributions(busId);

        // If passenger data was processed into a new bus location, emit that too
        if (processed) {
          // Get the latest bus location that was just created
          const latestLocation = await prisma.busLocation.findFirst({
            where: { busId },
            orderBy: { timestamp: 'desc' },
          });

          if (latestLocation) {
            // Emit the aggregated location update
            await emitBusLocationUpdate(busId, {
              ...latestLocation,
              source: 'passenger', // Indicate this is from passenger data
            });
          }
        }
      }
    }

    res.json(location);
  } catch (error) {
    console.error('Update user location error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get nearby buses
 * @route GET /api/locations/nearby-buses
 * @access Private
 */
const getNearbyBuses = async (req, res) => {
  try {
    const { latitude, longitude, radius = 1000 } = req.query; // radius in meters, default 1km

    // Validate required fields
    if (!latitude || !longitude) {
      return res.status(400).json({ message: 'Latitude and longitude are required' });
    }

    // Get all active buses
    const buses = await prisma.bus.findMany({
      where: {
        isActive: true,
        status: 'operational',
      },
      select: {
        id: true,
        busNumber: true,
        busName: true,
        routeId: true,
        route: {
          select: {
            id: true,
            routeName: true,
            routeNumber: true,
          },
        },
      },
    });

    // Get the latest location for each bus
    const busesWithLocations = await Promise.all(
      buses.map(async (bus) => {
        // First try to get driver-provided location
        const latestLocation = await prisma.busLocation.findFirst({
          where: { busId: bus.id },
          orderBy: { timestamp: 'desc' },
        });

        if (!latestLocation) {
          return null;
        }

        // Calculate distance
        const distance = locationUtils.calculateDistance(
          parseFloat(latitude),
          parseFloat(longitude),
          latestLocation.latitude,
          latestLocation.longitude
        );

        // Add source information to indicate if this is passenger-aggregated data
        // We can determine this by checking if the location was created within 2 minutes
        // of the most recent driver location update
        const isPassengerData = latestLocation.accuracy === 50; // We set this value for passenger data

        return {
          ...bus,
          currentLocation: {
            ...latestLocation,
            isPassengerData,
          },
          distance, // in meters
        };
      })
    );

    // Filter out buses with no location or outside the radius
    const nearbyBuses = busesWithLocations
      .filter(bus => bus !== null && bus.distance <= parseFloat(radius))
      .sort((a, b) => a.distance - b.distance);

    res.json(nearbyBuses);
  } catch (error) {
    console.error('Get nearby buses error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get nearby stops
 * @route GET /api/locations/nearby-stops
 * @access Private
 */
const getNearbyStops = async (req, res) => {
  try {
    const { latitude, longitude, radius = 1000 } = req.query; // radius in meters, default 1km

    // Validate required fields
    if (!latitude || !longitude) {
      return res.status(400).json({ message: 'Latitude and longitude are required' });
    }

    // Get all active stops
    const stops = await prisma.stop.findMany({
      where: { isActive: true },
      include: {
        route: {
          select: {
            id: true,
            routeName: true,
            routeNumber: true,
          },
        },
      },
    });

    // Calculate distance for each stop
    const stopsWithDistance = stops.map(stop => {
      const distance = locationUtils.calculateDistance(
        parseFloat(latitude),
        parseFloat(longitude),
        stop.latitude,
        stop.longitude
      );

      return {
        ...stop,
        distance, // in meters
      };
    });

    // Filter stops within radius and sort by distance
    const nearbyStops = stopsWithDistance
      .filter(stop => stop.distance <= parseFloat(radius))
      .sort((a, b) => a.distance - b.distance);

    // Get estimated arrivals for each nearby stop
    const stopsWithArrivals = await Promise.all(
      nearbyStops.map(async (stop) => {
        const arrivals = await prisma.busArrivalEstimate.findMany({
          where: {
            stopId: stop.id,
            estimatedArrivalTime: {
              gte: new Date(),
            },
          },
          orderBy: { estimatedArrivalTime: 'asc' },
          take: 5, // Get only the next 5 arrivals
        });

        return {
          ...stop,
          upcomingArrivals: arrivals,
        };
      })
    );

    res.json(stopsWithArrivals);
  } catch (error) {
    console.error('Get nearby stops error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Process passenger contributions for all buses
 * @route POST /api/locations/process-passenger-data
 * @access Private/Admin
 */
const processAllPassengerContributions = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    // Get all active buses
    const buses = await prisma.bus.findMany({
      where: {
        isActive: true,
        status: 'operational',
      },
      select: {
        id: true,
      },
    });

    // Process passenger contributions for each bus
    const results = await Promise.all(
      buses.map(async (bus) => {
        const success = await locationUtils.processPassengerContributions(bus.id);
        return { busId: bus.id, success };
      })
    );

    res.json({
      message: 'Passenger contributions processed',
      results,
    });
  } catch (error) {
    console.error('Process passenger contributions error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get passenger location contributions for a bus
 * @route GET /api/locations/passenger-contributions/:busId
 * @access Private/Admin
 */
const getPassengerContributions = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const { busId } = req.params;
    const { processed, timeframe = 30 } = req.query; // timeframe in minutes, default 30

    // Build filter
    const filter = { busId };

    if (processed !== undefined) {
      filter.isProcessed = processed === 'true';
    }

    filter.timestamp = {
      gte: new Date(Date.now() - timeframe * 60 * 1000)
    };

    // Get contributions
    const contributions = await prisma.passengerLocationContribution.findMany({
      where: filter,
      orderBy: { timestamp: 'desc' },
    });

    res.json(contributions);
  } catch (error) {
    console.error('Get passenger contributions error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  updateUserLocation,
  getNearbyBuses,
  getNearbyStops,
  processAllPassengerContributions,
  getPassengerContributions,
};
