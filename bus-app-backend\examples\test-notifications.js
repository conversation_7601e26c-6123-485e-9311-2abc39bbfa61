const axios = require('axios');

// Configuration
const config = {
  serverUrl: 'http://localhost:5000/api',
  testUser: {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User',
    deviceToken: 'test-device-token-123' // This would be a real FCM token in production
  }
};

// Test functions
async function registerUser() {
  try {
    console.log('\nRegistering test user...');
    const response = await axios.post(`${config.serverUrl}/users/register`, config.testUser);
    console.log('✅ User registered successfully');
    console.log('User ID:', response.data.id);
    console.log('Token:', response.data.token);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data.message.includes('already exists')) {
      console.log('User already exists, trying to log in...');
      return await loginUser();
    }
    console.error('❌ Registration failed:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
    }
    throw error;
  }
}

async function loginUser() {
  try {
    console.log('\nLogging in test user...');
    const response = await axios.post(`${config.serverUrl}/users/login`, {
      email: config.testUser.email,
      password: config.testUser.password,
      deviceToken: config.testUser.deviceToken
    });
    console.log('✅ Login successful');
    console.log('User ID:', response.data.id);
    console.log('Token:', response.data.token);
    return response.data;
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
    }
    throw error;
  }
}

async function getNotificationPreferences(token) {
  try {
    console.log('\nGetting notification preferences...');
    const response = await axios.get(`${config.serverUrl}/notifications/preferences`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Notification preferences retrieved');
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to get notification preferences:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
    }
    throw error;
  }
}

async function updateNotificationPreferences(token, preferences) {
  try {
    console.log('\nUpdating notification preferences...');
    const response = await axios.put(
      `${config.serverUrl}/notifications/preferences`,
      preferences,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    console.log('✅ Notification preferences updated');
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to update notification preferences:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
    }
    throw error;
  }
}

async function sendTestNotification(token) {
  try {
    console.log('\nSending test notification...');
    const response = await axios.post(
      `${config.serverUrl}/notifications/test`,
      {},
      { headers: { Authorization: `Bearer ${token}` } }
    );
    console.log('✅ Test notification sent');
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to send test notification:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
    }
    throw error;
  }
}

async function getNotifications(token) {
  try {
    console.log('\nGetting notifications...');
    const response = await axios.get(`${config.serverUrl}/notifications`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Notifications retrieved');
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to get notifications:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
    }
    throw error;
  }
}

// Main test function
async function runTests() {
  try {
    // Register or login user
    const userData = await registerUser();
    const token = userData.token;

    // Get notification preferences
    await getNotificationPreferences(token);

    // Update notification preferences
    await updateNotificationPreferences(token, {
      enabled: true,
      loginAlerts: true,
      welcomeMessage: true,
      serviceUpdates: true,
      busDelays: true,
      promotions: false
    });

    // Send test notification
    await sendTestNotification(token);

    // Get notifications
    await getNotifications(token);

    console.log('\n✅ All tests completed successfully');
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
  }
}

// Run the tests
runTests();
