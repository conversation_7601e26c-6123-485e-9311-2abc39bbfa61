import { Skeleton } from "@/components/ui/skeleton";

export default function FindBusLoading() {
  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-10 w-40" />
          <div className="flex space-x-2">
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-32" />
          </div>
        </div>

        {/* Map Skeleton */}
        <Skeleton className="h-[400px] w-full rounded-xl" />

        {/* Bus Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array(6)
            .fill(0)
            .map((_, index) => (
              <Skeleton key={index} className="h-40 rounded-xl" />
            ))}
        </div>
      </div>
    </div>
  );
}
