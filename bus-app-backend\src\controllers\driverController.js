const { prisma } = require('../config/db');
const { hashPassword } = require('../utils/password');
const { generateToken } = require('../utils/jwt');
const firebaseService = require('../services/firebaseService');

/**
 * Get all drivers
 * @route GET /api/drivers
 * @access Private/Admin
 */
const getAllDrivers = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const { status } = req.query;

    // Build filter object
    const filter = {};
    if (status) filter.status = status;

    const drivers = await prisma.driver.findMany({
      where: filter,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            profilePicture: true,
            isActive: true,
          },
        },
        bus: {
          select: {
            id: true,
            busNumber: true,
            busName: true,
            status: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    res.json(drivers);
  } catch (error) {
    console.error('Get all drivers error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get driver by ID
 * @route GET /api/drivers/:id
 * @access Private/Admin
 */
const getDriverById = async (req, res) => {
  try {
    // Check if user is admin or the driver themselves
    const driver = await prisma.driver.findUnique({
      where: { id: req.params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            profilePicture: true,
            isActive: true,
            lastLoginAt: true,
          },
        },
        bus: {
          select: {
            id: true,
            busNumber: true,
            busName: true,
            status: true,
            routeId: true,
            route: {
              select: {
                id: true,
                routeName: true,
                routeNumber: true,
                startPoint: true,
                endPoint: true,
              },
            },
          },
        },
      },
    });

    if (!driver) {
      return res.status(404).json({ message: 'Driver not found' });
    }

    // Check if user is admin or the driver themselves
    if (req.user.role !== 'admin' && req.user.id !== driver.userId) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    res.json(driver);
  } catch (error) {
    console.error('Get driver by ID error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Create a new driver
 * @route POST /api/drivers
 * @access Private/Admin
 */
const createDriver = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const {
      name,
      email,
      password,
      phone,
      licenseNo,
      licenseExpiry,
      experience,
      assignedBusId
    } = req.body;

    // Validate required fields
    if (!name || !email || !password || !phone || !licenseNo) {
      return res.status(400).json({
        message: 'Please provide name, email, password, phone, and licenseNo'
      });
    }

    // Check if email already exists
    const userExists = await prisma.user.findUnique({
      where: { email },
    });

    if (userExists) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }

    // If assignedBusId is provided, check if bus exists
    if (assignedBusId) {
      const busExists = await prisma.bus.findUnique({
        where: { id: assignedBusId },
      });

      if (!busExists) {
        return res.status(400).json({ message: 'Bus not found' });
      }
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user with driver role
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        phone,
        role: 'driver',
        isActive: true,
        lastLoginAt: new Date(),
      },
    });

    // Create driver
    const driver = await prisma.driver.create({
      data: {
        userId: user.id,
        licenseNo,
        phone,
        licenseExpiry: licenseExpiry ? new Date(licenseExpiry) : null,
        experience: experience ? parseInt(experience) : null,
        status: assignedBusId ? 'on_duty' : 'available',
        assignedBusId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        bus: {
          select: {
            id: true,
            busNumber: true,
            busName: true,
          },
        },
      },
    });

    // Create default notification preferences
    await prisma.notificationPreference.create({
      data: {
        userId: user.id,
        enabled: true,
        loginAlerts: true,
        welcomeMessage: true,
        serviceUpdates: true,
        busDelays: true,
        promotions: true,
      },
    });

    // Send welcome notification if device token is provided
    if (req.body.deviceToken) {
      // Update user with device token
      await prisma.user.update({
        where: { id: user.id },
        data: { deviceToken: req.body.deviceToken },
      });

      // Send welcome notification
      await firebaseService.sendNotificationToUser(
        user.id,
        {
          title: 'Welcome to Bus App!',
          body: `Hello ${name}! You have been registered as a driver.`
        },
        {
          type: 'welcome',
          role: 'driver'
        }
      );
    }

    res.status(201).json({
      ...driver,
      token: generateToken(user.id),
    });
  } catch (error) {
    console.error('Create driver error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update a driver
 * @route PUT /api/drivers/:id
 * @access Private/Admin
 */
const updateDriver = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    const {
      licenseNo,
      phone,
      licenseExpiry,
      experience,
      rating,
      status,
      assignedBusId
    } = req.body;

    // Check if driver exists
    const driver = await prisma.driver.findUnique({
      where: { id: req.params.id },
    });

    if (!driver) {
      return res.status(404).json({ message: 'Driver not found' });
    }

    // If assignedBusId is provided, check if bus exists
    if (assignedBusId && assignedBusId !== driver.assignedBusId) {
      const busExists = await prisma.bus.findUnique({
        where: { id: assignedBusId },
      });

      if (!busExists) {
        return res.status(400).json({ message: 'Bus not found' });
      }
    }

    // Prepare update data
    const updateData = {};
    if (licenseNo) updateData.licenseNo = licenseNo;
    if (phone) updateData.phone = phone;
    if (licenseExpiry) updateData.licenseExpiry = new Date(licenseExpiry);
    if (experience !== undefined) updateData.experience = parseInt(experience);
    if (rating !== undefined) updateData.rating = parseFloat(rating);
    if (status) updateData.status = status;
    if (assignedBusId !== undefined) {
      updateData.assignedBusId = assignedBusId || null;

      // If assigning a bus, update status to on_duty
      if (assignedBusId) {
        updateData.status = 'on_duty';
      }
      // If removing a bus, update status to available
      else if (driver.status === 'on_duty') {
        updateData.status = 'available';
      }
    }

    // Update driver
    const updatedDriver = await prisma.driver.update({
      where: { id: req.params.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        bus: {
          select: {
            id: true,
            busNumber: true,
            busName: true,
          },
        },
      },
    });

    res.json(updatedDriver);
  } catch (error) {
    console.error('Update driver error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Delete a driver
 * @route DELETE /api/drivers/:id
 * @access Private/Admin
 */
const deleteDriver = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized as an admin' });
    }

    // Check if driver exists
    const driver = await prisma.driver.findUnique({
      where: { id: req.params.id },
    });

    if (!driver) {
      return res.status(404).json({ message: 'Driver not found' });
    }

    // Delete driver
    await prisma.driver.delete({
      where: { id: req.params.id },
    });

    // Update user role to 'user'
    await prisma.user.update({
      where: { id: driver.userId },
      data: { role: 'user' },
    });

    res.json({ message: 'Driver removed successfully' });
  } catch (error) {
    console.error('Delete driver error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get current bus location
 * @route GET /api/drivers/:id/bus-location
 * @access Private
 */
const getCurrentBusLocation = async (req, res) => {
  try {
    // Check if driver exists
    const driver = await prisma.driver.findUnique({
      where: { id: req.params.id },
    });

    if (!driver) {
      return res.status(404).json({ message: 'Driver not found' });
    }

    // Check if user is admin or the driver themselves
    if (req.user.role !== 'admin' && req.user.id !== driver.userId) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    // Check if driver has an assigned bus
    if (!driver.assignedBusId) {
      return res.status(404).json({ message: 'Driver has no assigned bus' });
    }

    // Get the latest location
    const location = await prisma.busLocation.findFirst({
      where: { busId: driver.assignedBusId },
      orderBy: { timestamp: 'desc' },
    });

    if (!location) {
      return res.status(404).json({ message: 'Location not found for this bus' });
    }

    res.json(location);
  } catch (error) {
    console.error('Get current bus location error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update driver status
 * @route PUT /api/drivers/:id/status
 * @access Private
 */
const updateDriverStatus = async (req, res) => {
  try {
    const { status } = req.body;

    // Validate required fields
    if (!status) {
      return res.status(400).json({ message: 'Status is required' });
    }

    // Check if status is valid
    const validStatuses = ['available', 'on_duty', 'on_leave', 'inactive'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        message: `Status must be one of: ${validStatuses.join(', ')}`
      });
    }

    // Check if driver exists
    const driver = await prisma.driver.findUnique({
      where: { id: req.params.id },
    });

    if (!driver) {
      return res.status(404).json({ message: 'Driver not found' });
    }

    // Check if user is admin or the driver themselves
    if (req.user.role !== 'admin' && req.user.id !== driver.userId) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    // Update driver status
    const updatedDriver = await prisma.driver.update({
      where: { id: req.params.id },
      data: { status },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        bus: {
          select: {
            id: true,
            busNumber: true,
            busName: true,
          },
        },
      },
    });

    res.json(updatedDriver);
  } catch (error) {
    console.error('Update driver status error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  getAllDrivers,
  getDriverById,
  createDriver,
  updateDriver,
  deleteDriver,
  getCurrentBusLocation,
  updateDriverStatus,
};
