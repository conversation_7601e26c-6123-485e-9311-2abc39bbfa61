// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Admin {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  email     String   @unique
  password  String
  name      String?
  phone     String?
  role      String   @default("admin") // 'admin'
  isActive  Boolean  @default(true)
  lastLoginAt DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model User {
  id        String         @id @default(auto()) @map("_id") @db.ObjectId
  email     String         @unique
  password  String
  name      String?
  phone     String?
  profilePicture String?
  role      String         @default("user") // 'user', 'admin', 'driver'
  isActive  Boolean        @default(true)
  deviceToken String?      // For push notifications
  lastLoginAt DateTime?
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt

  location  UserLocation?
  driver    Driver?
  passengerLogs BusPassengerLog[]
  favoriteRoutes FavoriteRoute[]
  notifications Notification[]
  notificationPreference NotificationPreference?
}


model UserLocation {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @unique @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])
  latitude  Float
  longitude Float
  accuracy  Float?   // GPS accuracy in meters
  altitude  Float?   // in meters
  updatedAt DateTime @updatedAt
}


model Driver {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  userId        String   @unique @db.ObjectId
  user          User     @relation(fields: [userId], references: [id])
  licenseNo     String
  phone         String
  licenseExpiry DateTime?
  experience    Int?     // in years
  rating        Float?   // driver rating
  status        String   @default("available") // available, on_duty, on_leave, inactive
  assignedBusId String?  @db.ObjectId
  bus           Bus?     @relation(fields: [assignedBusId], references: [id], onDelete: SetNull)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}


model Bus {
  id                     String   @id @default(auto()) @map("_id") @db.ObjectId
  busNumber              String   @unique
  busName                String
  totalSeats             Int
  currentSeatsAvailable  Int
  currentPassengerCount  Int      @default(0)
  isCrowded              Boolean  @default(false)
  isActive               Boolean  @default(true)
  status                 String   @default("operational") // operational, maintenance, out-of-service
  busType                String?  // regular, express, etc.
  features               String[] // wifi, ac, wheelchair, etc.
  fuelType               String?
  lastMaintenanceDate    DateTime?
  routeId                String?  @db.ObjectId
  route                  Route?   @relation(fields: [routeId], references: [id])
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt

  locations              BusLocation[]
  schedules              Schedule[]
  passengerLogs          BusPassengerLog[]
  drivers                Driver[]
  incidents              Incident[]
}


model Route {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  routeName   String
  routeNumber String?  @unique
  startPoint  String
  endPoint    String
  distanceKm  Float
  estimatedTime Int    // in minutes
  description String?
  isActive    Boolean  @default(true)
  color       String?  // for UI representation
  polyline    String?  // encoded route path for maps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  stops       Stop[]
  buses       Bus[]
  favoriteRoutes FavoriteRoute[]
}


model Stop {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  stopName    String
  stopCode    String?
  latitude    Float
  longitude   Float
  stopOrder   Int      // order in the route
  isActive    Boolean  @default(true)
  description String?
  amenities   String[] // shelter, bench, etc.
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  routeId     String   @db.ObjectId
  route       Route    @relation(fields: [routeId], references: [id])

  // For real-time estimated arrival times
  estimatedArrivals BusArrivalEstimate[]
}


model BusLocation {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  busId     String   @db.ObjectId
  bus       Bus      @relation(fields: [busId], references: [id])
  latitude  Float
  longitude Float
  speed     Float?   // in km/h
  heading   Float?   // in degrees, 0-360
  accuracy  Float?   // GPS accuracy in meters
  altitude  Float?   // in meters
  timestamp DateTime @default(now())

  // Optional: nearest stop information
  nearestStopId String? @db.ObjectId
  distanceToStop Float?  // in meters
}


model Schedule {
  id              String   @id @default(auto()) @map("_id") @db.ObjectId
  busId           String   @db.ObjectId
  bus             Bus      @relation(fields: [busId], references: [id])
  startTime       DateTime
  endTime         DateTime
  daysOfWeek      String[] // e.g., ["Monday", "Wednesday", "Friday"]
  frequencyMinutes Int
  isActive        Boolean  @default(true)
  seasonType      String?  // regular, holiday, summer, etc.
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}


model BusPassengerLog {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  busId     String   @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])
  bus       Bus      @relation(fields: [busId], references: [id])
  action    String   // "boarded" or "left"
  stopId    String?  @db.ObjectId // where the action happened
  latitude  Float?
  longitude Float?
  timestamp DateTime @default(now())
}

// New models for enhanced functionality

model FavoriteRoute {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  routeId   String   @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])
  route     Route    @relation(fields: [routeId], references: [id])
  createdAt DateTime @default(now())

  @@unique([userId, routeId])
}

model Notification {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])
  title     String
  message   String
  type      String   // "delay", "service_change", "alert", etc.
  isRead    Boolean  @default(false)
  data      Json?    // Additional data related to the notification
  createdAt DateTime @default(now())
}

model BusArrivalEstimate {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  busId     String   @db.ObjectId
  stopId    String   @db.ObjectId
  stop      Stop     @relation(fields: [stopId], references: [id])
  estimatedArrivalTime DateTime
  actualArrivalTime   DateTime?
  status    String   @default("scheduled") // scheduled, delayed, arrived, cancelled
  delayMinutes Int?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Incident {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  busId       String   @db.ObjectId
  bus         Bus      @relation(fields: [busId], references: [id])
  type        String   // "breakdown", "accident", "delay", etc.
  description String
  latitude    Float?
  longitude   Float?
  status      String   @default("reported") // reported, investigating, resolved
  reportedBy  String?
  resolvedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Feedback {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String?  @db.ObjectId
  busId     String?  @db.ObjectId
  routeId   String?  @db.ObjectId
  type      String   // "complaint", "suggestion", "praise"
  subject   String
  message   String
  rating    Int?     // 1-5 stars
  status    String   @default("submitted") // submitted, under_review, resolved
  response  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AppSettings {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  key       String   @unique
  value     String
  type      String   // "string", "number", "boolean", "json"
  category  String   // "general", "notification", "map", etc.
  updatedAt DateTime @updatedAt
}

model PassengerLocationContribution {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  busId     String   @db.ObjectId
  latitude  Float
  longitude Float
  speed     Float?   // in km/h
  heading   Float?   // in degrees, 0-360
  accuracy  Float?   // GPS accuracy in meters
  altitude  Float?   // in meters
  reliability Float   @default(0.5) // 0.0 to 1.0 reliability score
  isProcessed Boolean @default(false) // Whether this contribution has been processed
  timestamp DateTime @default(now())
}

model NotificationPreference {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @unique @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])
  enabled   Boolean  @default(true)
  loginAlerts Boolean @default(true)
  welcomeMessage Boolean @default(true)
  serviceUpdates Boolean @default(true)
  busDelays Boolean @default(true)
  promotions Boolean @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
