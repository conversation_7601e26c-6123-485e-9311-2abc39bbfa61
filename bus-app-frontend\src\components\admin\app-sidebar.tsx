"use client"

import type * as React from "react"
import {
  BarChartIcon,
  BusIcon,
  ClipboardListIcon,
  LayoutDashboardIcon,
  ListIcon,
  MapIcon,
  SettingsIcon,
  UsersIcon,
} from "lucide-react"

import { NavMain } from "./nav-main"
import { NavSecondary } from "./nav-secondary"
import { NavUser } from "./nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import Link from "next/link"

const data = {
  user: {
    name: "Admin",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/admin/dashboard",
      icon: LayoutDashboardIcon,
    },
    {
      title: "Users",
      url: "/admin/dashboard/users",
      icon: UsersIcon,
    },
    {
      title: "Buses",
      url: "/admin/dashboard/buses",
      icon: BusIcon,
    },
    {
      title: "Routes",
      url: "/admin/dashboard/routes",
      icon: MapIcon,
    },
    {
      title: "Schedules",
      url: "/admin/dashboard/schedules",
      icon: ClipboardListIcon,
    },
    {
      title: "Analytics",
      url: "/admin/dashboard/analytics",
      icon: BarChartIcon,
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "/admin/dashboard/settings",
      icon: SettingsIcon,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <Link href="/admin/dashboard">
                <BusIcon className="h-5 w-5" />
                <span className="text-base font-semibold">Bus Admin</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
