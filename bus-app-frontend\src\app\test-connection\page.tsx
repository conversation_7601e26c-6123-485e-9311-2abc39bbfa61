'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { authApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthToken } from '@/lib/auth';
import { toast } from 'sonner';

export default function TestConnectionPage() {
  const [apiStatus, setApiStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [apiResponse, setApiResponse] = useState<any>(null);
  const { isAuthenticated, user } = useAuth();

  const testApiConnection = async () => {
    setApiStatus('loading');
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'}/test/ping`);
      const data = await response.json();
      setApiResponse(data);
      setApiStatus('success');
      toast.success('Successfully connected to the API!');
    } catch (error) {
      console.error('API connection error:', error);
      setApiStatus('error');
      toast.error('Failed to connect to the API. Check the console for details.');
    }
  };

  const testAuthConnection = async () => {
    if (!isAuthenticated) {
      toast.error('You need to be logged in to test authentication.');
      return;
    }

    setApiStatus('loading');
    try {
      const response = await authApi.testAuth();
      setApiResponse(response);
      setApiStatus('success');
      toast.success('Authentication successful!');
    } catch (error) {
      console.error('Authentication test error:', error);
      setApiStatus('error');
      toast.error('Authentication test failed. Check the console for details.');
    }
  };

  const checkAuthHeader = async () => {
    if (!isAuthenticated) {
      toast.error('You need to be logged in to test authentication.');
      return;
    }

    setApiStatus('loading');
    try {
      // Make a request to the backend with the token and check the headers
      const token = getAuthToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'}/test/auth`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setApiResponse({
        message: 'Auth header check successful',
        token: token ? `${token.substring(0, 20)}...` : 'No token',
        response: data
      });
      setApiStatus('success');
      toast.success('Auth header check successful!');
    } catch (error) {
      console.error('Auth header check error:', error);
      setApiStatus('error');
      toast.error('Auth header check failed. Check the console for details.');
    }
  };

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Backend Connection Test</CardTitle>
          <CardDescription>
            Test the connection to your backend API
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">API Status</h3>
            <div className="flex items-center space-x-2">
              <div
                className={`h-3 w-3 rounded-full ${
                  apiStatus === 'idle'
                    ? 'bg-gray-300'
                    : apiStatus === 'loading'
                    ? 'bg-yellow-500'
                    : apiStatus === 'success'
                    ? 'bg-green-500'
                    : 'bg-red-500'
                }`}
              />
              <span className="text-sm">
                {apiStatus === 'idle'
                  ? 'Not tested'
                  : apiStatus === 'loading'
                  ? 'Testing...'
                  : apiStatus === 'success'
                  ? 'Connected'
                  : 'Failed'}
              </span>
            </div>
          </div>

          {isAuthenticated && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Authentication Status</h3>
              <div className="flex items-center space-x-2">
                <div className="h-3 w-3 rounded-full bg-green-500" />
                <span className="text-sm">
                  Logged in as {user?.name} ({user?.email})
                </span>
              </div>
              <div className="mt-2">
                <h4 className="text-xs font-medium">Auth Token:</h4>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-20 mt-1">
                  {getAuthToken() ? getAuthToken()?.substring(0, 20) + '...' : 'No token found'}
                </pre>
              </div>
            </div>
          )}

          {apiResponse && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">API Response</h3>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(apiResponse, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2 justify-between">
          <Button
            variant="outline"
            onClick={testApiConnection}
            disabled={apiStatus === 'loading'}
          >
            Test API Connection
          </Button>
          <div className="flex gap-2">
            <Button
              onClick={testAuthConnection}
              disabled={apiStatus === 'loading' || !isAuthenticated}
              variant="outline"
            >
              Test Auth API
            </Button>
            <Button
              onClick={checkAuthHeader}
              disabled={apiStatus === 'loading' || !isAuthenticated}
            >
              Check Auth Header
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
