const express = require('express');
const router = express.Router();

// Import route modules
const userRoutes = require('./userRoutes');
const busRoutes = require('./busRoutes');
const routeRoutes = require('./routeRoutes');
const stopRoutes = require('./stopRoutes');
const driverRoutes = require('./driverRoutes');
const locationRoutes = require('./locationRoutes');
const adminRoutes = require('./adminRoutes');
const socketRoutes = require('./socketRoutes');
const testRoutes = require('./testRoutes');
const notificationRoutes = require('./notificationRoutes');

// Use route modules
router.use('/users', userRoutes);
router.use('/buses', busRoutes);
router.use('/routes', routeRoutes);
router.use('/stops', stopRoutes);
router.use('/drivers', driverRoutes);
router.use('/locations', locationRoutes);
router.use('/admin', adminRoutes);
router.use('/socket', socketRoutes);
router.use('/test', testRoutes);
router.use('/notifications', notificationRoutes);

module.exports = router;
