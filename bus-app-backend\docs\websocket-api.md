# WebSocket API Documentation

This document describes the WebSocket API for real-time bus tracking in the Bus App.

## Overview

The WebSocket API allows clients to receive real-time updates about bus locations and passenger contributions. It uses Socket.IO for WebSocket communication, which provides reliable real-time bidirectional event-based communication.

## Authentication

All WebSocket connections require authentication using a JWT token. The token can be provided in two ways:

1. In the connection query parameters: `?token=your_jwt_token`
2. In the auth object: `{ auth: { token: 'your_jwt_token' } }`

Example:
```javascript
const socket = io('http://your-server.com', {
  auth: { token: 'your_jwt_token' }
});
```

## Namespaces

The WebSocket API uses the following namespaces:

### Main Namespace (`/`)

The main namespace is used for general connections and system-wide events.

### Bus Locations Namespace (`/bus-locations`)

This namespace is dedicated to bus location updates and related events.

## Events

### Connection Events

- `connect`: Fired when the connection is established
- `disconnect`: Fired when the connection is closed
- `connect_error`: Fired when a connection error occurs

### Bus Location Events

- `bus-location-update`: Received when a bus location is updated
  ```javascript
  {
    id: 'location_id',
    busId: 'bus_id',
    latitude: 37.7749,
    longitude: -122.4194,
    speed: 30,
    heading: 90,
    accuracy: 10,
    altitude: 100,
    timestamp: '2023-04-27T10:30:00Z',
    nearestStopId: 'stop_id',
    distanceToStop: 150,
    source: 'driver', // 'driver' or 'passenger'
    bus: {
      id: 'bus_id',
      busNumber: 'B123',
      busName: 'Express Downtown',
      routeId: 'route_id'
    }
  }
  ```

- `passenger-contribution`: Received when a passenger contributes location data
  ```javascript
  {
    id: 'contribution_id',
    userId: 'user_id',
    busId: 'bus_id',
    latitude: 37.7749,
    longitude: -122.4194,
    speed: 30,
    heading: 90,
    accuracy: 10,
    altitude: 100,
    reliability: 0.8,
    timestamp: '2023-04-27T10:30:00Z'
  }
  ```

## Subscriptions

Clients can subscribe to specific buses, routes, or nearby buses to receive targeted updates.

### Subscribe to a Specific Bus

```javascript
socket.emit('subscribe-bus', 'bus_id');
```

### Unsubscribe from a Specific Bus

```javascript
socket.emit('unsubscribe-bus', 'bus_id');
```

### Subscribe to a Route (All Buses on a Route)

```javascript
socket.emit('subscribe-route', 'route_id');
```

### Unsubscribe from a Route

```javascript
socket.emit('unsubscribe-route', 'route_id');
```

### Subscribe to Nearby Buses

```javascript
socket.emit('subscribe-nearby', {
  latitude: 37.7749,
  longitude: -122.4194,
  radius: 1000 // in meters, default is 1000
});
```

### Update User Location for Nearby Calculations

```javascript
socket.emit('update-location', {
  latitude: 37.7749,
  longitude: -122.4194,
  radius: 1000 // optional, in meters
});
```

## REST API Endpoints for WebSocket Support

The following REST API endpoints are available to support WebSocket functionality:

### Authenticate for WebSocket Connection

```
GET /api/socket/auth
```

**Headers:**
- `Authorization: Bearer your_jwt_token`

**Response:**
```json
{
  "authenticated": true,
  "userId": "user_id",
  "role": "user"
}
```

### Get WebSocket Server Status

```
GET /api/socket/status
```

**Response:**
```json
{
  "status": "online",
  "connections": {
    "main": 10,
    "busLocations": 8
  }
}
```

## Example Client Implementation

See the `examples/websocket-client.html` file for a complete example of a WebSocket client implementation.

## Best Practices

1. Always handle connection errors and reconnection
2. Subscribe only to the buses or routes you need
3. Unsubscribe when you no longer need updates
4. Update your location regularly when using nearby subscriptions
5. Handle disconnections gracefully

## Error Handling

Socket.IO will automatically attempt to reconnect when the connection is lost. You should listen for the `connect_error` event to handle connection errors:

```javascript
socket.on('connect_error', (error) => {
  console.error('Connection error:', error.message);
});
```

## Rate Limiting

To prevent abuse, the WebSocket server implements rate limiting on certain events. If you exceed the rate limits, you may receive an error event or be disconnected.

## Security Considerations

- Always use HTTPS in production
- Keep your JWT tokens secure
- Validate all input data
- Be aware of the data you're sending and receiving
