/**
 * Test script to send debug messages and test WebSocket connections
 */

const axios = require('axios');

// Configuration
const config = {
  serverUrl: 'http://localhost:5000',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MGUzNjIzYTBmN2MxMTM2MDM3ODk3YiIsImlhdCI6MTc0NTc2MTgyNywiZXhwIjoxNzQ4MzUzODI3fQ.IRsNREQZhNO0C2zz3QW-zLc1vuRmHTjulXccjBn1BJE', // Add your JWT token here
  busId: '680e3664f08ee02ce773c1da', // Add the bus ID here
};

// Set up axios instance with authorization header
const api = axios.create({
  baseURL: config.serverUrl,
  headers: {
    'Authorization': `Bearer ${config.token}`,
    'Content-Type': 'application/json'
  }
});

// Send a debug message
async function sendDebugMessage(message, busId) {
  try {
    const response = await api.get('/api/test/debug', {
      params: {
        message,
        busId
      }
    });
    
    console.log('Debug message sent:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error sending debug message:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
    }
    throw error;
  }
}

// Send a test bus location update
async function sendTestBusLocation(busId, latitude, longitude) {
  try {
    const locationData = {
      latitude: latitude || 37.7749 + (Math.random() - 0.5) * 0.01,
      longitude: longitude || -122.4194 + (Math.random() - 0.5) * 0.01,
      speed: Math.random() * 50,
      heading: Math.random() * 360,
      accuracy: 10
    };
    
    const response = await api.post(`/api/test/bus-location/${busId}`, locationData);
    console.log(`Location update sent for bus ${busId}:`, locationData);
    console.log(`Server response: ${response.status} ${response.statusText}`);
    return response.data;
  } catch (error) {
    console.error('Error sending location update:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
    }
    throw error;
  }
}

// Main function
async function main() {
  try {
    // Send a debug message
    await sendDebugMessage('Test debug message from test-debug.js', config.busId);
    
    // Wait 2 seconds
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Send a test bus location update
    await sendTestBusLocation(config.busId);
    
    // Wait 2 seconds
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Send another debug message
    await sendDebugMessage('Second test message from test-debug.js', config.busId);
    
    console.log('All test messages sent successfully');
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the main function
main();
