// const app = require('./app');
// const http = require('http');
// const dotenv = require('dotenv');
// const { initializeSocketIO } = require('./services/socketService');

// // Load environment variables
// dotenv.config();

// const PORT = process.env.PORT || 5000;

// // Create HTTP server
// const server = http.createServer(app);

// // Initialize Socket.IO
// initializeSocketIO(server);

// // Start server
// server.listen(PORT, () => {
//   console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
// });

const app = require('./app');
const http = require('http');
const dotenv = require('dotenv');
const { initializeSocketIO } = require('./services/socketService');

// Load environment variables
dotenv.config();

const PORT = process.env.PORT || 5000;

// Create HTTP server
const server = http.createServer(app);

// Initialize Socket.IO
initializeSocketIO(server);

// Start server - LISTEN ON ALL INTERFACES
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Server running in ${process.env.NODE_ENV} mode on http://0.0.0.0:${PORT}`);
});
