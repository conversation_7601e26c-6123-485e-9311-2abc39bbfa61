/**
 * Utility functions for location processing
 */
const { prisma } = require('../config/db');
const { emitBusLocationUpdate } = require('../services/socketService');

/**
 * Calculate distance between two coordinates in meters
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} - Distance in meters
 */
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371e3; // Earth radius in meters
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lon2 - lon1) * Math.PI / 180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c; // Distance in meters
};

/**
 * Check if a user is likely on a specific bus based on their location
 * @param {number} userLat - User latitude
 * @param {number} userLon - User longitude
 * @param {string} busId - Bus ID to check
 * @returns {Promise<{isOnBus: boolean, distance: number}>} - Result with isOnBus flag and distance
 */
const isUserOnBus = async (userLat, userLon, busId) => {
  try {
    // Get the latest location for the bus
    const latestBusLocation = await prisma.busLocation.findFirst({
      where: { busId },
      orderBy: { timestamp: 'desc' },
    });

    if (!latestBusLocation) {
      return { isOnBus: false, distance: Infinity };
    }

    // Calculate distance between user and bus
    const distance = calculateDistance(
      userLat,
      userLon,
      latestBusLocation.latitude,
      latestBusLocation.longitude
    );

    // If distance is less than 50 meters, user is likely on the bus
    // This threshold can be adjusted based on GPS accuracy considerations
    return {
      isOnBus: distance < 50,
      distance,
      busLocation: latestBusLocation
    };
  } catch (error) {
    console.error('Error checking if user is on bus:', error);
    return { isOnBus: false, distance: Infinity };
  }
};

/**
 * Find which bus a user is likely on based on their location
 * @param {number} userLat - User latitude
 * @param {number} userLon - User longitude
 * @returns {Promise<{busId: string|null, distance: number}>} - Result with busId and distance
 */
const findUserBus = async (userLat, userLon) => {
  try {
    // Get all active buses
    const buses = await prisma.bus.findMany({
      where: {
        isActive: true,
        status: 'operational',
      },
      select: {
        id: true,
      },
    });

    // Check each bus
    const busDistances = await Promise.all(
      buses.map(async (bus) => {
        const { isOnBus, distance, busLocation } = await isUserOnBus(userLat, userLon, bus.id);
        return {
          busId: bus.id,
          distance,
          isOnBus,
          busLocation
        };
      })
    );

    // Find the closest bus that the user is likely on
    const closestBus = busDistances
      .filter(bus => bus.isOnBus)
      .sort((a, b) => a.distance - b.distance)[0];

    if (closestBus) {
      return {
        busId: closestBus.busId,
        distance: closestBus.distance,
        busLocation: closestBus.busLocation
      };
    }

    return { busId: null, distance: Infinity };
  } catch (error) {
    console.error('Error finding user bus:', error);
    return { busId: null, distance: Infinity };
  }
};

/**
 * Calculate reliability score for a passenger location contribution
 * @param {Object} contribution - The location contribution
 * @param {Object} busLocation - The latest known bus location
 * @returns {number} - Reliability score between 0 and 1
 */
const calculateReliability = (contribution, busLocation) => {
  let reliability = 0.5; // Base reliability

  // If accuracy is good, increase reliability
  if (contribution.accuracy && contribution.accuracy < 20) {
    reliability += 0.2;
  } else if (contribution.accuracy && contribution.accuracy > 50) {
    reliability -= 0.2;
  }

  // If we have a recent bus location to compare with
  if (busLocation) {
    const timeDiff = Math.abs(new Date(contribution.timestamp) - new Date(busLocation.timestamp));
    const timeDiffMinutes = timeDiff / (1000 * 60);

    // If bus location is recent (less than 5 minutes old)
    if (timeDiffMinutes < 5) {
      const distance = calculateDistance(
        contribution.latitude,
        contribution.longitude,
        busLocation.latitude,
        busLocation.longitude
      );

      // If passenger location is close to last known bus location
      if (distance < 100) {
        reliability += 0.2;
      } else if (distance > 500) {
        reliability -= 0.3;
      }
    }
  }

  // Ensure reliability is between 0 and 1
  return Math.max(0, Math.min(1, reliability));
};

/**
 * Process passenger location contributions to update bus location
 * @param {string} busId - The bus ID
 * @returns {Promise<boolean>} - Success flag
 */
const processPassengerContributions = async (busId) => {
  try {
    // Get the latest driver-provided location
    const latestDriverLocation = await prisma.busLocation.findFirst({
      where: { busId },
      orderBy: { timestamp: 'desc' },
    });

    // Get recent unprocessed passenger contributions for this bus
    const recentContributions = await prisma.passengerLocationContribution.findMany({
      where: {
        busId,
        isProcessed: false,
        timestamp: {
          // Only consider contributions from the last 5 minutes
          gte: new Date(Date.now() - 5 * 60 * 1000)
        }
      },
      orderBy: { timestamp: 'desc' },
    });

    if (recentContributions.length === 0) {
      return false;
    }

    // If we have a recent driver location (less than 2 minutes old), don't use passenger data
    if (latestDriverLocation &&
        (new Date() - new Date(latestDriverLocation.timestamp)) < 2 * 60 * 1000) {

      // Mark contributions as processed
      await prisma.passengerLocationContribution.updateMany({
        where: {
          id: {
            in: recentContributions.map(c => c.id)
          }
        },
        data: { isProcessed: true }
      });

      return false;
    }

    // Calculate reliability for each contribution
    const contributionsWithReliability = recentContributions.map(contribution => ({
      ...contribution,
      reliability: calculateReliability(contribution, latestDriverLocation)
    }));

    // Filter out low reliability contributions
    const reliableContributions = contributionsWithReliability
      .filter(c => c.reliability > 0.3)
      .sort((a, b) => b.reliability - a.reliability);

    if (reliableContributions.length === 0) {
      return false;
    }

    // Calculate weighted average location
    let totalWeight = 0;
    let weightedLat = 0;
    let weightedLon = 0;
    let weightedSpeed = 0;
    let weightedHeading = 0;
    let speedCount = 0;
    let headingCount = 0;

    reliableContributions.forEach(contribution => {
      const weight = contribution.reliability;
      totalWeight += weight;
      weightedLat += contribution.latitude * weight;
      weightedLon += contribution.longitude * weight;

      if (contribution.speed !== null && contribution.speed !== undefined) {
        weightedSpeed += contribution.speed * weight;
        speedCount++;
      }

      if (contribution.heading !== null && contribution.heading !== undefined) {
        weightedHeading += contribution.heading * weight;
        headingCount++;
      }
    });

    const avgLat = weightedLat / totalWeight;
    const avgLon = weightedLon / totalWeight;
    const avgSpeed = speedCount > 0 ? weightedSpeed / totalWeight : null;
    const avgHeading = headingCount > 0 ? weightedHeading / totalWeight : null;

    // Find nearest stop if the bus has a route
    const bus = await prisma.bus.findUnique({
      where: { id: busId },
      select: { routeId: true }
    });

    let nearestStopId = null;
    let distanceToStop = null;

    if (bus && bus.routeId) {
      const stops = await prisma.stop.findMany({
        where: { routeId: bus.routeId },
      });

      if (stops.length > 0) {
        // Calculate distance to each stop
        const distances = stops.map(stop => {
          const distance = calculateDistance(
            avgLat,
            avgLon,
            stop.latitude,
            stop.longitude
          );
          return { stopId: stop.id, distance };
        });

        // Find the nearest stop
        const nearest = distances.reduce((min, current) =>
          current.distance < min.distance ? current : min, distances[0]);

        nearestStopId = nearest.stopId;
        distanceToStop = nearest.distance;
      }
    }

    // Create a new bus location entry
    const newLocation = await prisma.busLocation.create({
      data: {
        busId,
        latitude: avgLat,
        longitude: avgLon,
        speed: avgSpeed,
        heading: avgHeading,
        accuracy: 50, // Default accuracy for passenger-aggregated data
        nearestStopId,
        distanceToStop,
      },
    });

    // Mark all contributions as processed
    await prisma.passengerLocationContribution.updateMany({
      where: {
        id: {
          in: recentContributions.map(c => c.id)
        }
      },
      data: { isProcessed: true }
    });

    // Emit the new location via WebSocket
    await emitBusLocationUpdate(busId, {
      ...newLocation,
      source: 'passenger', // Indicate this is from passenger data
      contributionsCount: reliableContributions.length,
    });

    return true;
  } catch (error) {
    console.error('Error processing passenger contributions:', error);
    return false;
  }
};

module.exports = {
  calculateDistance,
  isUserOnBus,
  findUserBus,
  calculateReliability,
  processPassengerContributions
};
