const admin = require('firebase-admin');
const path = require('path');
const { prisma } = require('../config/db');

// Initialize Firebase Admin SDK
const serviceAccount = require('../config/bus-app-service-account.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

/**
 * Send a push notification to a specific user
 * @param {String} userId - User ID
 * @param {Object} notification - Notification data
 * @param {String} notification.title - Notification title
 * @param {String} notification.body - Notification body
 * @param {Object} data - Additional data to send with the notification
 * @returns {Promise<Object>} - Firebase messaging response
 */
const sendNotificationToUser = async (userId, notification, data = {}) => {
  try {
    // Get user's device token
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { deviceToken: true, id: true }
    });

    if (!user || !user.deviceToken) {
      console.log(`No device token found for user ${userId}`);
      return null;
    }

    // Check notification preferences
    const notificationPreference = await prisma.notificationPreference.findUnique({
      where: { userId: userId }
    });

    // If preferences exist and notifications are disabled, don't send
    if (notificationPreference && !notificationPreference.enabled) {
      console.log(`Notifications disabled for user ${userId}`);
      return null;
    }

    // Create notification record in database
    await prisma.notification.create({
      data: {
        userId: userId,
        title: notification.title,
        message: notification.body,
        type: data.type || 'general',
        data: data
      }
    });

    // Prepare message
    const message = {
      notification,
      data: Object.keys(data).reduce((acc, key) => {
        // Firebase only accepts string values in the data object
        acc[key] = String(data[key]);
        return acc;
      }, {}),
      token: user.deviceToken
    };

    // Send message
    const response = await admin.messaging().send(message);
    console.log(`Successfully sent notification to user ${userId}:`, response);
    return response;
  } catch (error) {
    console.error(`Error sending notification to user ${userId}:`, error);
    return null;
  }
};

/**
 * Send a notification to multiple users
 * @param {Array<String>} userIds - Array of user IDs
 * @param {Object} notification - Notification data
 * @param {Object} data - Additional data
 * @returns {Promise<Array>} - Array of responses
 */
const sendNotificationToMultipleUsers = async (userIds, notification, data = {}) => {
  try {
    const responses = await Promise.all(
      userIds.map(userId => sendNotificationToUser(userId, notification, data))
    );
    return responses.filter(Boolean);
  } catch (error) {
    console.error('Error sending notifications to multiple users:', error);
    return [];
  }
};

/**
 * Send a notification to all users with a specific role
 * @param {String} role - User role ('user', 'driver', 'admin')
 * @param {Object} notification - Notification data
 * @param {Object} data - Additional data
 * @returns {Promise<Array>} - Array of responses
 */
const sendNotificationByRole = async (role, notification, data = {}) => {
  try {
    // Get all users with the specified role and a device token
    const users = await prisma.user.findMany({
      where: {
        role,
        deviceToken: { not: null }
      },
      select: { id: true }
    });

    const userIds = users.map(user => user.id);
    return await sendNotificationToMultipleUsers(userIds, notification, data);
  } catch (error) {
    console.error(`Error sending notifications to ${role}s:`, error);
    return [];
  }
};

/**
 * Send a welcome notification to a user
 * @param {String} userId - User ID
 * @param {String} name - User's name
 * @returns {Promise<Object>} - Firebase messaging response
 */
const sendWelcomeNotification = async (userId, name) => {
  const notification = {
    title: 'Welcome to Bus App!',
    body: `Hello ${name || 'there'}! Thank you for registering with our bus tracking service.`
  };

  const data = {
    type: 'welcome',
    action: 'open_app'
  };

  return await sendNotificationToUser(userId, notification, data);
};

/**
 * Send a login detected notification to a user
 * @param {String} userId - User ID
 * @param {String} deviceInfo - Device information
 * @returns {Promise<Object>} - Firebase messaging response
 */
const sendLoginNotification = async (userId, deviceInfo = 'a new device') => {
  const notification = {
    title: 'New Login Detected',
    body: `Your account was just accessed from ${deviceInfo}.`
  };

  const data = {
    type: 'security',
    action: 'review_login'
  };

  return await sendNotificationToUser(userId, notification, data);
};

module.exports = {
  admin,
  sendNotificationToUser,
  sendNotificationToMultipleUsers,
  sendNotificationByRole,
  sendWelcomeNotification,
  sendLoginNotification
};
