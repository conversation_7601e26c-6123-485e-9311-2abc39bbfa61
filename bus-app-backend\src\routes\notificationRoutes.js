const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const { authMiddleware } = require('../middleware/authMiddleware');

/**
 * @route   GET /api/notifications
 * @desc    Get all notifications for the current user
 * @access  Private
 */
router.get('/', authMiddleware, notificationController.getUserNotifications);

/**
 * @route   PUT /api/notifications/:id/read
 * @desc    Mark a notification as read
 * @access  Private
 */
router.put('/:id/read', authMiddleware, notificationController.markNotificationAsRead);

/**
 * @route   PUT /api/notifications/read-all
 * @desc    Mark all notifications as read for the current user
 * @access  Private
 */
router.put('/read-all', authMiddleware, notificationController.markAllNotificationsAsRead);

/**
 * @route   DELETE /api/notifications/:id
 * @desc    Delete a notification
 * @access  Private
 */
router.delete('/:id', authMiddleware, notificationController.deleteNotification);

/**
 * @route   GET /api/notifications/preferences
 * @desc    Get notification preferences for the current user
 * @access  Private
 */
router.get('/preferences', authMiddleware, notificationController.getNotificationPreferences);

/**
 * @route   PUT /api/notifications/preferences
 * @desc    Update notification preferences for the current user
 * @access  Private
 */
router.put('/preferences', authMiddleware, notificationController.updateNotificationPreferences);

/**
 * @route   POST /api/notifications/test
 * @desc    Send a test notification to the current user
 * @access  Private
 */
router.post('/test', authMiddleware, notificationController.sendTestNotification);

module.exports = router;
