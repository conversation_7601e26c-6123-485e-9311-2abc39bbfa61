const express = require('express');
const router = express.Router();
const { authMiddleware } = require('../middleware/authMiddleware');

/**
 * @route   GET /api/socket/auth
 * @desc    Authenticate for WebSocket connection
 * @access  Private
 */
router.get('/auth', authMiddleware, (req, res) => {
  // This route is just for authentication
  // The client will use the token to connect to WebSocket
  res.json({ 
    authenticated: true, 
    userId: req.user.id,
    role: req.user.role
  });
});

/**
 * @route   GET /api/socket/status
 * @desc    Get WebSocket server status
 * @access  Public
 */
router.get('/status', (req, res) => {
  try {
    const { getIO } = require('../services/socketService');
    const io = getIO();
    
    // Get connected clients count
    const mainNamespace = io.of('/');
    const busLocationsNamespace = io.of('/bus-locations');
    
    res.json({
      status: 'online',
      connections: {
        main: mainNamespace.sockets.size,
        busLocations: busLocationsNamespace.sockets.size
      }
    });
  } catch (error) {
    console.error('Socket status error:', error);
    res.status(500).json({ 
      status: 'error',
      message: error.message
    });
  }
});

module.exports = router;
