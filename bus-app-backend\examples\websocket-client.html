<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bus Tracking WebSocket Client</title>
  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
     integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
     crossorigin=""/>
  <!-- Leaflet JavaScript -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
     integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
     crossorigin=""></script>
  <!-- Leaflet Rotated Marker Plugin -->
  <script src="https://cdn.jsdelivr.net/npm/leaflet-rotatedmarker@0.2.0/leaflet.rotatedMarker.min.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input, button {
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
      padding: 10px 15px;
    }
    button:hover {
      background-color: #45a049;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
    }
    .connected {
      background-color: #d4edda;
      color: #155724;
    }
    .disconnected {
      background-color: #f8d7da;
      color: #721c24;
    }
    .log {
      height: 300px;
      overflow-y: auto;
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-family: monospace;
    }
    .map-container {
      height: 400px;
      margin-top: 20px;
    }
    #map {
      height: 100%;
      border-radius: 8px;
    }
    /* Bus marker styles */
    .bus-marker {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: #4285F4;
      border-radius: 50%;
      color: white;
      font-weight: bold;
      border: 2px solid white;
      box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    }
    .bus-marker.passenger-data {
      background-color: #FFA500;
    }
    /* Make sure the marker content is visible */
    .bus-marker div {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Bus Tracking WebSocket Client</h1>

    <div class="card">
      <h2>Connection</h2>
      <div id="connectionStatus" class="status disconnected">Disconnected</div>

      <div class="form-group">
        <label for="serverUrl">Server URL:</label>
        <input type="text" id="serverUrl" value="http://localhost:5000" style="width: 300px;">
      </div>

      <div class="form-group">
        <label for="token">Authentication Token:</label>
        <input type="text" id="token" placeholder="Enter your JWT token" style="width: 300px;">
      </div>

      <button id="connectBtn">Connect</button>
      <button id="disconnectBtn" disabled>Disconnect</button>
    </div>

    <div class="card">
      <h2>Bus Subscriptions</h2>
      <div class="form-group">
        <label for="busId">Bus ID:</label>
        <input type="text" id="busId" placeholder="Enter bus ID">
        <button id="subscribeBusBtn" disabled>Subscribe</button>
        <button id="unsubscribeBusBtn" disabled>Unsubscribe</button>
      </div>

      <div class="form-group">
        <label for="routeId">Route ID:</label>
        <input type="text" id="routeId" placeholder="Enter route ID">
        <button id="subscribeRouteBtn" disabled>Subscribe</button>
        <button id="unsubscribeRouteBtn" disabled>Unsubscribe</button>
      </div>

      <div class="form-group">
        <label>Nearby Buses:</label>
        <button id="subscribeNearbyBtn" disabled>Subscribe to Nearby</button>
        <button id="updateLocationBtn" disabled>Update My Location</button>
      </div>
    </div>

    <div class="card">
      <h2>Event Log</h2>
      <div id="log" class="log"></div>
    </div>

    <div class="card">
      <h2>Map</h2>
      <div class="map-container">
        <div id="map"></div>
      </div>
    </div>
  </div>

  <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
  <script>
    // DOM Elements
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const serverUrlInput = document.getElementById('serverUrl');
    const tokenInput = document.getElementById('token');
    const connectionStatus = document.getElementById('connectionStatus');
    const logElement = document.getElementById('log');
    const busIdInput = document.getElementById('busId');
    const routeIdInput = document.getElementById('routeId');
    const subscribeBusBtn = document.getElementById('subscribeBusBtn');
    const unsubscribeBusBtn = document.getElementById('unsubscribeBusBtn');
    const subscribeRouteBtn = document.getElementById('subscribeRouteBtn');
    const unsubscribeRouteBtn = document.getElementById('unsubscribeRouteBtn');
    const subscribeNearbyBtn = document.getElementById('subscribeNearbyBtn');
    const updateLocationBtn = document.getElementById('updateLocationBtn');

    // Socket.IO instance
    let socket = null;
    let busLocationsSocket = null;

    // Map and markers
    let map = null;
    let markers = {};

    // Connect to WebSocket server
    connectBtn.addEventListener('click', () => {
      const serverUrl = serverUrlInput.value;
      const token = tokenInput.value;

      if (!token) {
        logMessage('Error: Authentication token is required');
        return;
      }

      try {
        // Connect to main namespace
        socket = io(serverUrl, {
          auth: { token }
        });

        // Connect to bus-locations namespace
        busLocationsSocket = io(`${serverUrl}/bus-locations`, {
          auth: { token }
        });

        // Main socket events
        socket.on('connect', () => {
          setConnected(true);
          logMessage('Connected to main namespace');
        });

        socket.on('disconnect', () => {
          setConnected(false);
          logMessage('Disconnected from main namespace');
        });

        socket.on('connect_error', (error) => {
          logMessage(`Connection error: ${error.message}`);
          setConnected(false);
        });

        // Bus locations socket events
        busLocationsSocket.on('connect', () => {
          logMessage('Connected to bus-locations namespace');
          enableSubscriptionButtons(true);
        });

        busLocationsSocket.on('disconnect', () => {
          logMessage('Disconnected from bus-locations namespace');
          enableSubscriptionButtons(false);
        });

        busLocationsSocket.on('connect_error', (error) => {
          logMessage(`Bus locations connection error: ${error.message}`);
          enableSubscriptionButtons(false);
        });

        // Listen for bus location updates
        busLocationsSocket.on('bus-location-update', (data) => {
          const busInfo = data.bus ? `${data.bus.busNumber}` : data.busId.substring(0, 5);
          logMessage(`Bus location update: ${busInfo} at ${data.latitude.toFixed(6)}, ${data.longitude.toFixed(6)}`);
          console.log('Bus location data:', data);
          updateBusMarker(data);
        });

        // Listen for passenger contributions
        busLocationsSocket.on('passenger-contribution', (data) => {
          logMessage(`Passenger contribution: ${JSON.stringify(data)}`);
        });
      } catch (error) {
        logMessage(`Error initializing connection: ${error.message}`);
      }
    });

    // Disconnect from WebSocket server
    disconnectBtn.addEventListener('click', () => {
      if (socket) {
        socket.disconnect();
      }

      if (busLocationsSocket) {
        busLocationsSocket.disconnect();
      }

      setConnected(false);
      enableSubscriptionButtons(false);
    });

    // Subscribe to specific bus
    subscribeBusBtn.addEventListener('click', () => {
      const busId = busIdInput.value;
      if (!busId) {
        logMessage('Error: Bus ID is required');
        return;
      }

      busLocationsSocket.emit('subscribe-bus', busId);
      logMessage(`Subscribed to bus: ${busId}`);
    });

    // Unsubscribe from specific bus
    unsubscribeBusBtn.addEventListener('click', () => {
      const busId = busIdInput.value;
      if (!busId) {
        logMessage('Error: Bus ID is required');
        return;
      }

      busLocationsSocket.emit('unsubscribe-bus', busId);
      logMessage(`Unsubscribed from bus: ${busId}`);
    });

    // Subscribe to route
    subscribeRouteBtn.addEventListener('click', () => {
      const routeId = routeIdInput.value;
      if (!routeId) {
        logMessage('Error: Route ID is required');
        return;
      }

      busLocationsSocket.emit('subscribe-route', routeId);
      logMessage(`Subscribed to route: ${routeId}`);
    });

    // Unsubscribe from route
    unsubscribeRouteBtn.addEventListener('click', () => {
      const routeId = routeIdInput.value;
      if (!routeId) {
        logMessage('Error: Route ID is required');
        return;
      }

      busLocationsSocket.emit('unsubscribe-route', routeId);
      logMessage(`Unsubscribed from route: ${routeId}`);
    });

    // Subscribe to nearby buses
    subscribeNearbyBtn.addEventListener('click', () => {
      // Get current location
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;

            busLocationsSocket.emit('subscribe-nearby', {
              latitude,
              longitude,
              radius: 1000 // 1km radius
            });

            logMessage(`Subscribed to nearby buses at ${latitude}, ${longitude}`);
          },
          (error) => {
            logMessage(`Geolocation error: ${error.message}`);
          }
        );
      } else {
        logMessage('Error: Geolocation is not supported by this browser');
      }
    });

    // Update user location
    updateLocationBtn.addEventListener('click', () => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;

            busLocationsSocket.emit('update-location', {
              latitude,
              longitude
            });

            logMessage(`Updated location to ${latitude}, ${longitude}`);
          },
          (error) => {
            logMessage(`Geolocation error: ${error.message}`);
          }
        );
      } else {
        logMessage('Error: Geolocation is not supported by this browser');
      }
    });

    // Helper functions
    function setConnected(isConnected) {
      if (isConnected) {
        connectionStatus.textContent = 'Connected';
        connectionStatus.className = 'status connected';
        connectBtn.disabled = true;
        disconnectBtn.disabled = false;
      } else {
        connectionStatus.textContent = 'Disconnected';
        connectionStatus.className = 'status disconnected';
        connectBtn.disabled = false;
        disconnectBtn.disabled = true;
        enableSubscriptionButtons(false);
      }
    }

    function enableSubscriptionButtons(enabled) {
      subscribeBusBtn.disabled = !enabled;
      unsubscribeBusBtn.disabled = !enabled;
      subscribeRouteBtn.disabled = !enabled;
      unsubscribeRouteBtn.disabled = !enabled;
      subscribeNearbyBtn.disabled = !enabled;
      updateLocationBtn.disabled = !enabled;
    }

    function logMessage(message) {
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = document.createElement('div');
      logEntry.textContent = `[${timestamp}] ${message}`;
      logElement.appendChild(logEntry);
      logElement.scrollTop = logElement.scrollHeight;
    }

    function updateBusMarker(data) {
      if (!map) {
        logMessage('Map not initialized yet');
        return;
      }

      try {
        const { busId, latitude, longitude, heading, source } = data;
        const busNumber = data.bus ? data.bus.busNumber : busId.substring(0, 5);

        logMessage(`Processing marker for bus ${busNumber} at ${latitude}, ${longitude}`);

        // If marker exists, update its position
        if (markers[busId]) {
          logMessage(`Updating existing marker for bus ${busNumber}`);
          const latLng = L.latLng(latitude, longitude);
          markers[busId].setLatLng(latLng);

          // Update rotation if heading is available
          if (heading !== undefined && heading !== null) {
            markers[busId].setRotationAngle(heading);
          }

          // Update popup content
          const popupContent = createPopupContent(data);
          markers[busId].getPopup().setContent(popupContent);

          // Try to update marker appearance based on source
          try {
            const markerElement = markers[busId].getElement();
            if (markerElement) {
              if (source === 'passenger' || source === 'test') {
                markerElement.classList.add('passenger-data');
              } else {
                markerElement.classList.remove('passenger-data');
              }
            }
          } catch (e) {
            logMessage(`Warning: Could not update marker appearance: ${e.message}`);
          }
        } else {
          logMessage(`Creating new marker for bus ${busNumber}`);

          // Try using a simpler marker first
          try {
            // Create a custom HTML element for the marker
            const markerIcon = L.divIcon({
              className: `bus-marker ${source === 'passenger' || source === 'test' ? 'passenger-data' : ''}`,
              html: `<div>${busNumber}</div>`,
              iconSize: [30, 30],
              iconAnchor: [15, 15]
            });

            // Create new marker
            const marker = L.marker([latitude, longitude], {
              icon: markerIcon,
              rotationAngle: heading || 0,
              rotationOrigin: 'center center'
            }).addTo(map);

            // Add popup with bus information
            const popupContent = createPopupContent(data);
            marker.bindPopup(popupContent);

            // Store marker reference
            markers[busId] = marker;

            // Center map on first marker
            if (Object.keys(markers).length === 1) {
              map.setView([latitude, longitude], 14);
            }

            logMessage(`Marker created successfully for bus ${busNumber}`);
          } catch (e) {
            logMessage(`Error creating custom marker: ${e.message}. Trying fallback marker.`);

            // Fallback to a simple circle marker if the custom marker fails
            const marker = L.circleMarker([latitude, longitude], {
              radius: 8,
              fillColor: source === 'passenger' || source === 'test' ? '#FFA500' : '#4285F4',
              color: '#fff',
              weight: 2,
              opacity: 1,
              fillOpacity: 0.8
            }).addTo(map);

            // Add popup with bus information
            const popupContent = createPopupContent(data);
            marker.bindPopup(popupContent);

            // Store marker reference
            markers[busId] = marker;

            // Center map on first marker
            if (Object.keys(markers).length === 1) {
              map.setView([latitude, longitude], 14);
            }

            logMessage(`Fallback marker created for bus ${busNumber}`);
          }
        }
      } catch (error) {
        logMessage(`Error updating bus marker: ${error.message}`);
        console.error('Error updating bus marker:', error);
      }
    }

    function createPopupContent(data) {
      const { busId, latitude, longitude, speed, heading, accuracy, timestamp, source } = data;
      const busInfo = data.bus ? `${data.bus.busNumber} - ${data.bus.busName}` : busId;
      const formattedTime = new Date(timestamp).toLocaleTimeString();

      return `
        <div>
          <strong>Bus:</strong> ${busInfo}<br>
          <strong>Position:</strong> ${latitude.toFixed(6)}, ${longitude.toFixed(6)}<br>
          <strong>Speed:</strong> ${speed ? speed.toFixed(1) + ' km/h' : 'N/A'}<br>
          <strong>Heading:</strong> ${heading ? heading.toFixed(1) + '°' : 'N/A'}<br>
          <strong>Accuracy:</strong> ${accuracy ? accuracy.toFixed(1) + ' m' : 'N/A'}<br>
          <strong>Time:</strong> ${formattedTime}<br>
          <strong>Source:</strong> ${source || 'driver'}
        </div>
      `;
    }

    // Initialize Leaflet Map
    function initMap() {
      try {
        logMessage('Initializing map...');

        // Create map
        map = L.map('map').setView([37.7749, -122.4194], 12); // Default to San Francisco

        // Add OpenStreetMap tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19
        }).addTo(map);

        // Add a test marker to verify the map is working
        const testMarker = L.marker([37.7749, -122.4194]).addTo(map);
        testMarker.bindPopup('Test Marker').openPopup();

        // Add a circle to verify the map is working
        const circle = L.circle([37.7749, -122.4194], {
          color: 'red',
          fillColor: '#f03',
          fillOpacity: 0.5,
          radius: 500
        }).addTo(map);

        logMessage('Map initialized successfully');

        // Remove test markers after 5 seconds
        setTimeout(() => {
          map.removeLayer(testMarker);
          map.removeLayer(circle);
          logMessage('Test markers removed');
        }, 5000);
      } catch (error) {
        logMessage(`Error initializing map: ${error.message}`);
        console.error('Error initializing map:', error);
      }
    }

    // Initialize map when page loads
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(initMap, 500); // Slight delay to ensure DOM is fully loaded
    });
  </script>
</body>
</html>
