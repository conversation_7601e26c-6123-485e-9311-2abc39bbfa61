const { prisma } = require('../config/db');
const { hashPassword, comparePassword } = require('../utils/password');
const { generateToken } = require('../utils/jwt');

/**
 * Create a Single Admin ONLY - One-time operation
 * @route POST /api/admin/initialize
 * @access Private - Device-specific
 */
const createAdmin = async (req, res) => {
  try {
    const { email, password, name, phone, deviceId } = req.body;
    const ALLOWED_DEVICE_ID = process.env.ADMIN_CREATION_DEVICE_ID;

    // Validate required fields
    if (!email || !password || !deviceId) {
      return res.status(400).json({ 
        message: 'Email, password, and deviceId are required' 
      });
    }

    // Verify device ID
    if (deviceId !== ALLOWED_DEVICE_ID) {
      return res.status(403).json({ 
        message: 'Unauthorized device' 
      });
    }

    // Check if any admin exists
    const adminCount = await prisma.admin.count();
    if (adminCount > 0) {
      return res.status(403).json({ 
        message: 'Admin already exists. Only one admin account is allowed' 
      });
    }

    // Check if admin email exists
    const adminExists = await prisma.admin.findUnique({
      where: { email },
    });

    if (adminExists) {
      return res.status(400).json({ 
        message: 'Admin already exists' 
      });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create admin
    const admin = await prisma.admin.create({
      data: {
        email,
        password: hashedPassword,
        name: name || null,
        phone: phone || null,
        role: 'admin',
        isActive: true,
        lastLoginAt: new Date(),
      },
    });

    // Remove sensitive data before sending response
    const { password: _, ...adminData } = admin;

    res.status(201).json({
      message: 'Admin created successfully',
      admin: adminData
    });
  } catch (error) {
    console.error('Create admin error:', error);
    res.status(500).json({ 
      message: 'Server error', 
      error: error.message 
    });
  }
};

/**
 * Admin Login
 * @route POST /api/admin/login
 * @access Private
 */
const loginAdmin = async (req, res) => {
  try {
    const { email, password, deviceId } = req.body;
    const ALLOWED_DEVICE_ID = process.env.ADMIN_CREATION_DEVICE_ID;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({ message: 'Email and password are required' });
    }

    // Find admin
    const admin = await prisma.admin.findUnique({
      where: { email },
    });

    if (!admin || !admin.isActive) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // // Verify device ID
    //  if (deviceId !== ALLOWED_DEVICE_ID) {
    //     return res.status(403).json({ 
    //       message: 'Unauthorized device' 
    //     });
    //   }

    // Check password
    const isMatch = await comparePassword(password, admin.password);
    if (!isMatch) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Update last login
    await prisma.admin.update({
      where: { id: admin.id },
      data: { lastLoginAt: new Date() },
    });

    // Remove password from response
    const { password: _, ...adminData } = admin;

    res.json({
      ...adminData,
      token: generateToken(admin.id),
    });
  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  createAdmin,
  loginAdmin
};