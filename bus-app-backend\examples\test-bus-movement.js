/**
 * Test script to simulate bus movement and test WebSocket implementation
 *
 * This script simulates a bus moving along a predefined route and sends
 * location updates to the server using the test endpoint.
 *
 * NOTE: This script uses a special test endpoint (/api/test/bus-location/:id)
 * that bypasses the normal authorization checks. In a production environment,
 * only drivers assigned to a specific bus can update its location.
 */

const axios = require('axios');

// Configuration
const config = {
  serverUrl: 'http://localhost:5000',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MGUzNjIzYTBmN2MxMTM2MDM3ODk3YiIsImlhdCI6MTc0NTc2MTgyNywiZXhwIjoxNzQ4MzUzODI3fQ.IRsNREQZhNO0C2zz3QW-zLc1vuRmHTjulXccjBn1BJE', // Add your JWT token here
  busId: '680e3664f08ee02ce773c1da', // Add the bus ID here
  updateInterval: 5000, // Update interval in milliseconds (5 seconds)
  simulationDuration: 300000, // Simulation duration in milliseconds (5 minutes)
  routePoints: [
    // Example route points (latitude, longitude)
    // Replace with actual route points for your test
      { lat: 19.051375, lng: 72.839127 }, // Bandra center
      { lat: 19.054321, lng: 72.835678 }, // Nearby point 1
      { lat: 19.048765, lng: 72.841234 }, // Nearby point 2
      { lat: 19.052468, lng: 72.843579 }, // Nearby point 3
      { lat: 19.049876, lng: 72.837654 }, // Nearby point 4
      { lat: 19.051375, lng: 72.839127 }, // Back to Bandra center
  ],
  speed: 30, // Speed in km/h
  heading: 90, // Initial heading in degrees
  accuracy: 10, // GPS accuracy in meters
};

// Helper function to calculate heading between two points
function calculateHeading(lat1, lng1, lat2, lng2) {
  const y = Math.sin(lng2 - lng1) * Math.cos(lat2);
  const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(lng2 - lng1);
  const heading = Math.atan2(y, x) * 180 / Math.PI;
  return (heading + 360) % 360; // Normalize to 0-360
}

// Helper function to interpolate between two points
function interpolate(point1, point2, fraction) {
  return {
    lat: point1.lat + (point2.lat - point1.lat) * fraction,
    lng: point1.lng + (point2.lng - point1.lng) * fraction
  };
}

// Main simulation function
async function simulateBusMovement() {
  if (!config.token) {
    console.error('Error: JWT token is required. Please add your token to the config.');
    return;
  }

  if (!config.busId) {
    console.error('Error: Bus ID is required. Please add a bus ID to the config.');
    return;
  }

  console.log('Starting bus movement simulation...');
  console.log(`Bus ID: ${config.busId}`);
  console.log(`Server URL: ${config.serverUrl}`);
  console.log(`Update interval: ${config.updateInterval / 1000} seconds`);
  console.log(`Simulation duration: ${config.simulationDuration / 60000} minutes`);
  console.log('Route points:', config.routePoints.length);

  const startTime = Date.now();
  let currentSegment = 0;
  let segmentProgress = 0;

  // Set up axios instance with authorization header
  const api = axios.create({
    baseURL: config.serverUrl,
    headers: {
      'Authorization': `Bearer ${config.token}`,
      'Content-Type': 'application/json'
    }
  });

  // Simulation loop
  const simulationInterval = setInterval(async () => {
    const elapsedTime = Date.now() - startTime;

    // Check if simulation should end
    if (elapsedTime >= config.simulationDuration) {
      clearInterval(simulationInterval);
      console.log('Simulation completed.');
      return;
    }

    // Calculate current position
    const currentPoint = config.routePoints[currentSegment];
    const nextPoint = config.routePoints[currentSegment + 1] || config.routePoints[0];

    // Interpolate between current and next point based on progress
    const position = interpolate(currentPoint, nextPoint, segmentProgress);

    // Calculate heading
    const heading = calculateHeading(
      currentPoint.lat, currentPoint.lng,
      nextPoint.lat, nextPoint.lng
    );

    // Prepare location update data
    const locationData = {
      latitude: position.lat,
      longitude: position.lng,
      speed: config.speed,
      heading: heading,
      accuracy: config.accuracy,
      altitude: null
    };

    try {
      // Send location update to server using the test endpoint
      const response = await api.post(`/api/test/bus-location/${config.busId}`, locationData);
      console.log(`Location update sent: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);
      console.log(`Server response: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.error('Error sending location update:', error.message);
      if (error.response) {
        console.error('Server response:', error.response.data);
      }
    }

    // Update segment progress
    segmentProgress += 0.1; // Move 10% along the segment each update

    // Move to next segment if current segment is complete
    if (segmentProgress >= 1) {
      currentSegment = (currentSegment + 1) % (config.routePoints.length - 1);
      segmentProgress = 0;
    }
  }, config.updateInterval);
}

// Start simulation
simulateBusMovement();
