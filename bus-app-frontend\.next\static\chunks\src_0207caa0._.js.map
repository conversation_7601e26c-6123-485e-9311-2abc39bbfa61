{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Asif/Projects/BUS%20APP/bus-app-frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Asif/Projects/BUS%20APP/bus-app-frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Asif/Projects/BUS%20APP/bus-app-frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["// src/components/auth/ProtectedRoute.tsx\n'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  allowedRoles?: ('user' | 'driver' | 'admin')[];\n}\n\nexport default function ProtectedRoute({ \n  children, \n  allowedRoles \n}: ProtectedRouteProps) {\n  const { user, isLoading, isAuthenticated } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    // If not loading and not authenticated, redirect to login\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login');\n      return;\n    }\n\n    // If authenticated but role is not allowed, redirect to appropriate page\n    if (\n      !isLoading && \n      isAuthenticated && \n      user && \n      allowedRoles && \n      !allowedRoles.includes(user.role)\n    ) {\n      if (user.role === 'admin') {\n        router.push('/admin/dashboard');\n      } else {\n        router.push('/dashboard');\n      }\n    }\n  }, [isLoading, isAuthenticated, user, router, allowedRoles]);\n\n  // Show nothing while loading\n  if (isLoading) {\n    return <div className=\"flex items-center justify-center min-h-screen\">Loading...</div>;\n  }\n\n  // If not authenticated, don't render children\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  // If role check is required and user doesn't have the required role, don't render children\n  if (allowedRoles && user && !allowedRoles.includes(user.role)) {\n    return null;\n  }\n\n  // Otherwise, render children\n  return <>{children}</>;\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;;;AAJA;;;;AAWe,SAAS,eAAe,EACrC,QAAQ,EACR,YAAY,EACQ;;IACpB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,0DAA0D;YAC1D,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,yEAAyE;YACzE,IACE,CAAC,aACD,mBACA,QACA,gBACA,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAChC;gBACA,IAAI,KAAK,IAAI,KAAK,SAAS;oBACzB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;mCAAG;QAAC;QAAW;QAAiB;QAAM;QAAQ;KAAa;IAE3D,6BAA6B;IAC7B,IAAI,WAAW;QACb,qBAAO,6LAAC;YAAI,WAAU;sBAAgD;;;;;;IACxE;IAEA,8CAA8C;IAC9C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,2FAA2F;IAC3F,IAAI,gBAAgB,QAAQ,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QAC7D,OAAO;IACT;IAEA,6BAA6B;IAC7B,qBAAO;kBAAG;;AACZ;GA/CwB;;QAIuB,kIAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Asif/Projects/BUS%20APP/bus-app-frontend/src/components/admin/map/RoutingMap.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useRef, useState } from \"react\"\nimport { MapIcon } from \"lucide-react\"\nimport { createFallbackRouter } from \"./fallback-router\"\nimport \"@/styles/leaflet-custom.css\"\n\n// Add type declarations for Leaflet Routing Machine\ndeclare global {\n  namespace L {\n    namespace Routing {\n      function control(options: any): any\n      function osrmv1(options: any): any\n\n      interface Waypoint {\n        latLng: L.LatLng\n      }\n    }\n  }\n}\n\n// Polyline encoding/decoding utility\nconst PolylineUtil = {\n  encode: function (points: any[], precision?: number) {\n    if (!points.length) return \"\"\n\n    const factor = Math.pow(10, precision !== undefined ? precision : 5)\n\n    let output = this.encodePoint(points[0].lat || points[0][0], points[0].lng || points[0][1], factor)\n\n    for (let i = 1; i < points.length; i++) {\n      const a = points[i - 1]\n      const b = points[i]\n      output += this.encodePoint(b.lat || b[0], b.lng || b[1], factor, a.lat || a[0], a.lng || a[1])\n    }\n\n    return output\n  },\n\n  encodePoint: function (lat: number, lng: number, factor: number, prevLat?: number, prevLng?: number) {\n    // Round to the nearest factor\n    lat = Math.round(lat * factor)\n    lng = Math.round(lng * factor)\n\n    // Delta encode\n    const deltaLat = lat - (prevLat !== undefined ? Math.round(prevLat * factor) : 0)\n    const deltaLng = lng - (prevLng !== undefined ? Math.round(prevLng * factor) : 0)\n\n    return this.encodeSignedNumber(deltaLat) + this.encodeSignedNumber(deltaLng)\n  },\n\n  encodeSignedNumber: function (num: number) {\n    let sgn_num = num << 1\n    if (num < 0) {\n      sgn_num = ~sgn_num\n    }\n    return this.encodeNumber(sgn_num)\n  },\n\n  encodeNumber: (num: number) => {\n    let encodeString = \"\"\n    while (num >= 0x20) {\n      encodeString += String.fromCharCode((0x20 | (num & 0x1f)) + 63)\n      num >>= 5\n    }\n    encodeString += String.fromCharCode(num + 63)\n    return encodeString\n  },\n\n  decode: (encoded: string, precision?: number) => {\n    if (!encoded.length) return []\n\n    const factor = Math.pow(10, precision !== undefined ? precision : 5)\n    const len = encoded.length\n    let index = 0\n    let lat = 0\n    let lng = 0\n    const points = []\n\n    while (index < len) {\n      let b\n      let shift = 0\n      let result = 0\n\n      do {\n        b = encoded.charCodeAt(index++) - 63\n        result |= (b & 0x1f) << shift\n        shift += 5\n      } while (b >= 0x20)\n\n      const deltaLat = result & 1 ? ~(result >> 1) : result >> 1\n      lat += deltaLat\n\n      shift = 0\n      result = 0\n\n      do {\n        b = encoded.charCodeAt(index++) - 63\n        result |= (b & 0x1f) << shift\n        shift += 5\n      } while (b >= 0x20)\n\n      const deltaLng = result & 1 ? ~(result >> 1) : result >> 1\n      lng += deltaLng\n\n      points.push([lat / factor, lng / factor])\n    }\n\n    return points\n  },\n}\n\ninterface RoutingMapProps {\n  initialPolyline?: string\n  onPolylineChange: (polyline: string) => void\n  center?: [number, number] // [latitude, longitude]\n  zoom?: number\n  readOnly?: boolean\n}\n\nexport function RoutingMap({\n  initialPolyline,\n  onPolylineChange,\n  center = [19.076, 72.8777], // Default center at Mumbai\n  zoom = 12,\n  readOnly = false,\n}: RoutingMapProps) {\n  // Extend HTMLDivElement to include Leaflet properties\n  interface MapDiv extends HTMLDivElement {\n    _leaflet_id?: number\n  }\n\n  const mapRef = useRef<MapDiv>(null)\n  const mapInstanceRef = useRef<any>(null)\n  const routingControlRef = useRef<any>(null)\n  const mapContainerKey = useRef<string>(Math.random().toString(36).substring(2, 11))\n\n  // Track if component is mounted\n  const isMounted = useRef(true)\n  const [mapReady, setMapReady] = useState(false)\n  const [routingStatus, setRoutingStatus] = useState<'normal' | 'rate-limited' | 'fallback'>('normal')\n\n  // Effect to check if map container is ready\n  useEffect(() => {\n    if (typeof window === \"undefined\" || !mapRef.current) return\n\n    // Check if map container has dimensions\n    const checkMapContainer = () => {\n      if (mapRef.current && mapRef.current.clientWidth > 0 && mapRef.current.clientHeight > 0) {\n        setMapReady(true)\n      } else {\n        // Try again in a moment\n        setTimeout(checkMapContainer, 100)\n      }\n    }\n\n    checkMapContainer()\n\n    return () => {\n      setMapReady(false)\n    }\n  }, [])\n\n  // Effect to handle component mount/unmount\n  useEffect(() => {\n    // Set mounted flag\n    isMounted.current = true\n\n    return () => {\n      // Set unmounted flag\n      isMounted.current = false\n\n      // Clean up map resources\n      if (mapInstanceRef.current) {\n        try {\n          if (routingControlRef.current) {\n            mapInstanceRef.current.removeControl(routingControlRef.current)\n            routingControlRef.current = null\n          }\n          mapInstanceRef.current.remove()\n          mapInstanceRef.current = null\n        } catch (e) {\n          console.warn(\"Error cleaning up map on unmount:\", e)\n        }\n      }\n    }\n  }, [])\n\n  // Initialize the map\n  useEffect(() => {\n    if (typeof window === \"undefined\" || !mapRef.current || !mapReady) return\n\n    let map: any = null\n    let routing: any = null\n\n    // Dynamically import Leaflet\n    const initMap = async () => {\n      try {\n        console.log(\"Initializing Leaflet map with routing...\")\n\n        // Check if map is already initialized and clean it up first\n        if (mapInstanceRef.current) {\n          console.log(\"Map already exists, cleaning up first...\")\n          try {\n            if (routingControlRef.current) {\n              mapInstanceRef.current.removeControl(routingControlRef.current)\n              routingControlRef.current = null\n            }\n            mapInstanceRef.current.remove()\n            mapInstanceRef.current = null\n          } catch (e) {\n            console.warn(\"Error cleaning up existing map:\", e)\n          }\n        }\n\n        // Clear any previous content\n        if (mapRef.current) {\n          mapRef.current.innerHTML = \"\"\n          // Remove any Leaflet-specific attributes\n          if (mapRef.current._leaflet_id) {\n            delete mapRef.current._leaflet_id\n          }\n        }\n\n        // Import libraries\n        const L = (await import(\"leaflet\")).default\n        console.log(\"Leaflet loaded successfully\")\n\n        // Handle CSS imports\n        try {\n          // @ts-ignore - CSS imports\n          await import(\"leaflet/dist/leaflet.css\")\n          // @ts-ignore - CSS imports\n          await import(\"leaflet-routing-machine/dist/leaflet-routing-machine.css\")\n          console.log(\"Leaflet CSS loaded\")\n\n          // Fix Leaflet's default icon paths\n          delete (L.Icon.Default.prototype as any)._getIconUrl\n          L.Icon.Default.mergeOptions({\n            iconRetinaUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n            iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n            shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\",\n          })\n        } catch (e) {\n          console.error(\"CSS imports failed:\", e)\n          if (mapRef.current) {\n            mapRef.current.innerHTML =\n              '<div style=\"padding: 20px; color: red;\">Error loading map styles. Please check console.</div>'\n          }\n          return\n        }\n\n        // Make sure the map container is ready and has dimensions\n        if (!mapRef.current || !mapRef.current.clientWidth || !mapRef.current.clientHeight) {\n          console.error(\"Map container not ready or has zero dimensions\")\n          return\n        }\n\n        // Create map with additional safeguards\n        map = L.map(mapRef.current, {\n          center: center,\n          zoom: zoom,\n          zoomControl: true,\n          attributionControl: true,\n          fadeAnimation: false,\n          markerZoomAnimation: false,\n          preferCanvas: true,\n        })\n\n        // Store map instance in ref\n        mapInstanceRef.current = map\n        console.log(\"Map created successfully\")\n\n        // Force a resize event after a short delay to ensure the map renders correctly\n        setTimeout(() => {\n          if (isMounted.current && map) {\n            map.invalidateSize()\n            console.log(\"Map size invalidated\")\n          }\n        }, 300)\n\n        // Add tile layer\n        L.tileLayer(\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\", {\n          attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors',\n          maxZoom: 19,\n        }).addTo(map)\n\n        // Import Leaflet Routing Machine\n        try {\n          await import(\"leaflet-routing-machine\")\n          console.log(\"Leaflet Routing Machine loaded\")\n        } catch (error) {\n          console.error(\"Error loading Leaflet Routing Machine:\", error)\n          if (mapRef.current) {\n            mapRef.current.innerHTML =\n              '<div style=\"padding: 20px; color: red;\">Error loading routing engine. Please check console.</div>'\n          }\n          return\n        }\n\n        // Initialize waypoints\n        let initialWaypoints: any[] = []\n\n        // If initial polyline is provided, decode it to waypoints\n        if (initialPolyline && initialPolyline.length > 0) {\n          try {\n            const decodedPath = PolylineUtil.decode(initialPolyline)\n            // Convert to waypoints (use first, last, and some points in between)\n            if (decodedPath.length > 0) {\n              // Always include first and last points\n              initialWaypoints.push(L.latLng(decodedPath[0][0], decodedPath[0][1]))\n\n              // Add some intermediate points if there are many\n              if (decodedPath.length > 10) {\n                const step = Math.floor(decodedPath.length / 5)\n                for (let i = step; i < decodedPath.length - step; i += step) {\n                  initialWaypoints.push(L.latLng(decodedPath[i][0], decodedPath[i][1]))\n                }\n              }\n\n              // Add last point\n              if (decodedPath.length > 1) {\n                initialWaypoints.push(\n                  L.latLng(decodedPath[decodedPath.length - 1][0], decodedPath[decodedPath.length - 1][1]),\n                )\n              }\n            }\n          } catch (error) {\n            console.error(\"Error decoding polyline:\", error)\n            // If decoding fails, start with empty waypoints\n            initialWaypoints = []\n          }\n        }\n\n        // If no waypoints from polyline, start with empty or default waypoints\n        if (initialWaypoints.length === 0 && !readOnly) {\n          // For new routes, start with two points in Mumbai\n          initialWaypoints = [\n            L.latLng(19.076, 72.8777), // Mumbai Central\n            L.latLng(19.1136, 72.9023), // Bandra East\n          ]\n        }\n\n        // Create a fallback router\n        const fallbackRouter = createFallbackRouter()\n\n        // Ensure waypoints have valid coordinates\n        initialWaypoints = initialWaypoints.filter((wp) => {\n          return (\n            wp &&\n            wp.lat &&\n            wp.lng &&\n            typeof wp.lat === \"number\" &&\n            typeof wp.lng === \"number\" &&\n            !isNaN(wp.lat) &&\n            !isNaN(wp.lng)\n          )\n        })\n\n        // If we lost waypoints in filtering, add default ones\n        if (initialWaypoints.length < 2 && !readOnly) {\n          initialWaypoints = [\n            L.latLng(19.076, 72.8777), // Mumbai Central\n            L.latLng(19.1136, 72.9023), // Bandra East\n          ]\n        }\n\n        // Create a throttled OSRM router with rate limiting\n        let router\n        let lastRequestTime = 0\n        const MIN_REQUEST_INTERVAL = 2000 // Minimum 2 seconds between requests\n        let rateLimitHit = false\n\n        try {\n          // Create a custom router that wraps OSRM with rate limiting\n          const osrmRouter = L.Routing.osrmv1({\n            serviceUrl: \"https://router.project-osrm.org/route/v1\",\n            profile: \"driving\",\n            timeout: 10 * 1000, // Reduced timeout to 10 seconds\n            suppressDemoServerWarning: true,\n          })\n\n          // Wrap the original route method with throttling\n          const originalRoute = osrmRouter.route\n          router = {\n            ...osrmRouter,\n            route: function(waypoints: any[], callback: Function, context?: any) {\n              const now = Date.now()\n\n              // If we hit rate limit recently, use fallback\n              if (rateLimitHit) {\n                console.log(\"Using fallback router due to recent rate limit\")\n                setRoutingStatus('rate-limited')\n                return fallbackRouter.route(waypoints, callback)\n              }\n\n              // Check if enough time has passed since last request\n              const timeSinceLastRequest = now - lastRequestTime\n              if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {\n                const delay = MIN_REQUEST_INTERVAL - timeSinceLastRequest\n                console.log(`Throttling request, waiting ${delay}ms`)\n\n                setTimeout(() => {\n                  lastRequestTime = Date.now()\n                  originalRoute.call(this, waypoints, function(error: any, routes: any) {\n                    if (error && (error.status === 429 || error.message?.includes('429'))) {\n                      console.warn(\"Rate limit hit, switching to fallback router\")\n                      rateLimitHit = true\n                      setRoutingStatus('rate-limited')\n                      // Reset rate limit flag after 5 minutes\n                      setTimeout(() => {\n                        rateLimitHit = false\n                        setRoutingStatus('normal')\n                        console.log(\"Rate limit cooldown complete\")\n                      }, 5 * 60 * 1000)\n\n                      return fallbackRouter.route(waypoints, callback)\n                    }\n                    setRoutingStatus('normal')\n                    callback(error, routes)\n                  })\n                }, delay)\n                return\n              }\n\n              lastRequestTime = now\n              originalRoute.call(this, waypoints, function(error: any, routes: any) {\n                if (error && (error.status === 429 || error.message?.includes('429'))) {\n                  console.warn(\"Rate limit hit, switching to fallback router\")\n                  rateLimitHit = true\n                  setRoutingStatus('rate-limited')\n                  // Reset rate limit flag after 5 minutes\n                  setTimeout(() => {\n                    rateLimitHit = false\n                    setRoutingStatus('normal')\n                    console.log(\"Rate limit cooldown complete\")\n                  }, 5 * 60 * 1000)\n\n                  return fallbackRouter.route(waypoints, callback)\n                }\n                setRoutingStatus('normal')\n                callback(error, routes)\n              })\n            }\n          }\n        } catch (error) {\n          console.warn(\"Failed to create OSRM router, using fallback:\", error)\n          router = fallbackRouter\n        }\n\n        // Create a simpler routing control without custom markers\n        // @ts-ignore - TypeScript definitions for Leaflet Routing Machine are incomplete\n        routing = L.Routing.control({\n          waypoints: initialWaypoints,\n          routeWhileDragging: false, // Disabled to prevent excessive API calls\n          showAlternatives: false,\n          fitSelectedRoutes: true,\n          lineOptions: {\n            styles: [{ color: \"#3b82f6\", weight: 5 }],\n            extendToWaypoints: true,\n            missingRouteTolerance: 0,\n          },\n          addWaypoints: !readOnly,\n          draggableWaypoints: !readOnly,\n          useZoomParameter: false, // Disabled to reduce request complexity\n          router: router,\n          createMarker: (i: number, wp: any, n: number) => {\n            // Create custom markers for start, end, and intermediate points\n            let icon\n\n            if (i === 0) {\n              // Start point (green)\n              icon = L.divIcon({\n                className: \"custom-waypoint-icon start-icon\",\n                html: '<div style=\"background-color: #10b981; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);\"></div>',\n                iconSize: [16, 16],\n                iconAnchor: [8, 8],\n              })\n            } else if (i === n - 1) {\n              // End point (red)\n              icon = L.divIcon({\n                className: \"custom-waypoint-icon end-icon\",\n                html: '<div style=\"background-color: #ef4444; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);\"></div>',\n                iconSize: [16, 16],\n                iconAnchor: [8, 8],\n              })\n            } else {\n              // Intermediate points (blue)\n              icon = L.divIcon({\n                className: \"custom-waypoint-icon\",\n                html: '<div style=\"background-color: #3b82f6; width: 10px; height: 10px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);\"></div>',\n                iconSize: [14, 14],\n                iconAnchor: [7, 7],\n              })\n            }\n\n            return L.marker(wp.latLng, {\n              draggable: !readOnly,\n              icon: icon,\n            })\n          },\n        })\n\n        // Only add the routing control to the map if the map exists\n        if (map) {\n          routing.addTo(map)\n          // Store routing control in ref for cleanup\n          routingControlRef.current = routing\n        } else {\n          console.error(\"Cannot add routing control - map is null\")\n          return\n        }\n\n        // Handle route changes\n        routing.on(\"routesfound\", (e: any) => {\n          if (readOnly || !isMounted.current) return\n\n          const routes = e.routes\n          if (routes && routes.length > 0) {\n            const selectedRoute = routes[0] // Use the first (best) route\n            const coordinates = selectedRoute.coordinates\n\n            // Convert coordinates to format for encoding\n            const points = coordinates.map((coord: any) => [coord.lat, coord.lng])\n\n            // Encode the polyline\n            const encoded = PolylineUtil.encode(points)\n            onPolylineChange(encoded)\n          }\n        })\n\n        // Add click handler to the map for adding waypoints manually\n        if (!readOnly && map) {\n          // Add custom controls to help users\n\n          // Help button\n          const helpControl = L.Control.extend({\n            options: {\n              position: \"topright\",\n            },\n\n            onAdd: () => {\n              const container = L.DomUtil.create(\"div\", \"leaflet-bar leaflet-control leaflet-control-custom\")\n              container.style.backgroundColor = \"white\"\n              container.style.padding = \"6px 8px\"\n              container.style.fontSize = \"14px\"\n              container.style.cursor = \"pointer\"\n              container.style.borderRadius = \"4px\"\n              container.style.boxShadow = \"0 1px 5px rgba(0,0,0,0.4)\"\n              container.style.marginBottom = \"10px\"\n              container.innerHTML =\n                '<div style=\"display: flex; align-items: center;\"><span style=\"background-color: #3b82f6; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 5px;\">?</span> Help</div>'\n\n              container.onclick = () => {\n                alert(\n                  \"How to create a route:\\n\\n1. Click anywhere on the map to add waypoints\\n2. Drag waypoints to adjust the route\\n3. The route will automatically follow roads\\n4. If road routing fails or rate limits are hit, a direct line will be shown\\n5. Rate limiting prevents excessive API calls - wait a few minutes if you see the yellow warning\",\n                )\n              }\n\n              return container\n            },\n          })\n\n          // Direct route button (for when routing fails)\n          const directRouteControl = L.Control.extend({\n            options: {\n              position: \"topright\",\n            },\n\n            onAdd: () => {\n              const container = L.DomUtil.create(\"div\", \"leaflet-bar leaflet-control leaflet-control-custom\")\n              container.style.backgroundColor = \"white\"\n              container.style.padding = \"6px 8px\"\n              container.style.fontSize = \"14px\"\n              container.style.cursor = \"pointer\"\n              container.style.borderRadius = \"4px\"\n              container.style.boxShadow = \"0 1px 5px rgba(0,0,0,0.4)\"\n              container.innerHTML =\n                '<div style=\"display: flex; align-items: center;\"><span style=\"background-color: #ef4444; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 5px;\">↔</span> Direct Route</div>'\n\n              container.onclick = () => {\n                // Create a direct route between waypoints\n                if (!routing) return\n\n                const waypoints = routing\n                  .getWaypoints()\n                  .filter(\n                    (wp: any) =>\n                      wp && wp.latLng && typeof wp.latLng.lat === \"number\" && typeof wp.latLng.lng === \"number\",\n                  )\n\n                if (waypoints.length >= 2) {\n                  // Create a direct polyline between waypoints\n                  if (map) {\n                    L.polyline(\n                      waypoints.map((wp: any) => [wp.latLng.lat, wp.latLng.lng]),\n                      {\n                        color: \"#ef4444\",\n                        weight: 5,\n                        opacity: 0.7,\n                        dashArray: \"10, 10\", // Make it dashed to indicate it's a direct route\n                        lineCap: \"round\",\n                      },\n                    ).addTo(map)\n                  }\n\n                  // Convert waypoints to format for encoding\n                  const points = waypoints.map((wp: any) => [wp.latLng.lat, wp.latLng.lng])\n\n                  // Encode the polyline\n                  const encoded = PolylineUtil.encode(points)\n                  onPolylineChange(encoded)\n\n                  alert(\"Created direct route between waypoints (not following roads)\")\n                } else {\n                  alert(\"Please add at least 2 waypoints first\")\n                }\n              }\n\n              return container\n            },\n          })\n\n          try {\n            new helpControl().addTo(map)\n            new directRouteControl().addTo(map)\n          } catch (e) {\n            console.warn(\"Could not add custom controls:\", e)\n          }\n\n          // Add debounced route update mechanism\n          let routeUpdateTimeout: NodeJS.Timeout | null = null\n          const debouncedRouteUpdate = () => {\n            if (routeUpdateTimeout) {\n              clearTimeout(routeUpdateTimeout)\n            }\n            routeUpdateTimeout = setTimeout(() => {\n              if (routing && isMounted.current) {\n                try {\n                  // Trigger route calculation manually\n                  const waypoints = routing.getWaypoints()\n                  if (waypoints.length >= 2) {\n                    routing.route()\n                  }\n                } catch (error) {\n                  console.error(\"Error updating route:\", error)\n                }\n              }\n            }, 1000) // Wait 1 second after last waypoint change\n          }\n\n          // Add click handler to the map\n          map.on(\"click\", (e: any) => {\n            if (!isMounted.current || !routing) return\n\n            try {\n              const waypoints = routing.getWaypoints()\n              const newWaypoint = L.Routing.waypoint(e.latlng, \"Waypoint \" + (waypoints.length + 1))\n\n              // Add the new waypoint to the end of the route\n              routing.spliceWaypoints(waypoints.length, 0, newWaypoint)\n\n              // Trigger debounced route update\n              debouncedRouteUpdate()\n            } catch (error) {\n              console.error(\"Error handling map click:\", error)\n            }\n          })\n\n          // Add waypoint drag end handler for debounced updates\n          if (routing) {\n            routing.on('waypointdrag', () => {\n              // Don't update route while dragging\n            })\n\n            routing.on('waypointdragend', () => {\n              // Update route after drag ends\n              debouncedRouteUpdate()\n            })\n          }\n        }\n\n        // Handle routing errors\n        if (routing) {\n          routing.on(\"routingerror\", (e: any) => {\n            if (!isMounted.current || !map) return\n\n            console.error(\"Routing error:\", e.error)\n\n            // Create a simple direct route between waypoints\n            try {\n              // Get current waypoints\n              if (!routing) return\n\n              const currentWaypoints = routing\n                .getWaypoints()\n                .filter(\n                  (wp: any) =>\n                    wp && wp.latLng && typeof wp.latLng.lat === \"number\" && typeof wp.latLng.lng === \"number\",\n                )\n\n              if (currentWaypoints.length >= 2) {\n                // Create a direct polyline between waypoints\n                L.polyline(\n                  currentWaypoints.map((wp: any) => [wp.latLng.lat, wp.latLng.lng]),\n                  {\n                    color: \"#3b82f6\",\n                    weight: 5,\n                    opacity: 0.7,\n                    dashArray: \"10, 10\", // Make it dashed to indicate it's a direct route\n                    lineCap: \"round\",\n                  },\n                ).addTo(map)\n\n                // Add a note to the map - calculate center manually to avoid bounds error\n                let centerLat = 0,\n                  centerLng = 0\n                currentWaypoints.forEach((wp: any) => {\n                  centerLat += wp.latLng.lat\n                  centerLng += wp.latLng.lng\n                })\n                centerLat /= currentWaypoints.length\n                centerLng /= currentWaypoints.length\n                const center = L.latLng(centerLat, centerLng)\n\n                L.marker(center, {\n                  icon: L.divIcon({\n                    className: \"route-note\",\n                    html: '<div style=\"background-color: rgba(255,255,255,0.8); color: #333; padding: 5px; border-radius: 4px; font-size: 12px;\">Direct route (not following roads)</div>',\n                    iconSize: [200, 30],\n                    iconAnchor: [100, 15],\n                  }),\n                }).addTo(map)\n\n                // Convert waypoints to format for encoding\n                const points = currentWaypoints.map((wp: any) => [wp.latLng.lat, wp.latLng.lng])\n\n                // Encode the polyline\n                const encoded = PolylineUtil.encode(points)\n                onPolylineChange(encoded)\n              }\n            } catch (error) {\n              console.error(\"Error creating direct route:\", error)\n            }\n          })\n        }\n      } catch (error) {\n        console.error(\"Error initializing map:\", error)\n        if (mapRef.current) {\n          mapRef.current.innerHTML =\n            '<div style=\"padding: 20px; color: red;\">Error initializing map. Please check console.</div>'\n        }\n      }\n    }\n\n    // Initialize map only once\n    initMap()\n\n    // Cleanup function when component unmounts\n    return () => {\n      // Set unmounted flag\n      isMounted.current = false\n\n      // Cleanup function to properly dispose of map resources\n      const cleanupMap = () => {\n        try {\n          console.log(\"Cleaning up map resources...\")\n\n          // First remove routing control if it exists\n          if (routingControlRef.current && mapInstanceRef.current) {\n            console.log(\"Removing routing control\")\n            try {\n              mapInstanceRef.current.removeControl(routingControlRef.current)\n            } catch (e) {\n              console.warn(\"Error removing routing control:\", e)\n            }\n            routingControlRef.current = null\n          }\n\n          // Then remove the map if it exists\n          if (mapInstanceRef.current) {\n            console.log(\"Removing map instance\")\n            try {\n              mapInstanceRef.current.remove()\n            } catch (e) {\n              console.warn(\"Error removing map:\", e)\n            }\n            mapInstanceRef.current = null\n          }\n\n          // Clean up the DOM element\n          if (mapRef.current) {\n            mapRef.current.innerHTML = \"\"\n            // Remove any Leaflet-specific attributes\n            if (mapRef.current._leaflet_id) {\n              delete mapRef.current._leaflet_id\n            }\n          }\n        } catch (error) {\n          console.error(\"Error during map cleanup:\", error)\n        }\n      }\n\n      // Execute cleanup immediately to prevent issues with fast re-renders\n      cleanupMap()\n    }\n  }, [center, zoom, initialPolyline, onPolylineChange, readOnly, mapReady])\n\n  // Add this function if it's not already defined in your component\n  const createFallbackRouter = () => {\n    // This is a simple fallback router that just creates straight lines between waypoints\n    return {\n      route: (waypoints: any, callback: Function) => {\n        try {\n          // Create a simple route with straight lines\n          const coordinates: any[] = []\n\n          waypoints.forEach((wp: any) => {\n            if (wp && wp.latLng) {\n              coordinates.push({\n                lat: wp.latLng.lat,\n                lng: wp.latLng.lng,\n              })\n            }\n          })\n\n          // Create a simple route object\n          const route = {\n            name: \"Direct route\",\n            coordinates: coordinates,\n            summary: {\n              totalDistance: 0,\n              totalTime: 0,\n            },\n            inputWaypoints: waypoints,\n            waypoints: waypoints,\n          }\n\n          // Calculate simple distance\n          for (let i = 1; i < coordinates.length; i++) {\n            const p1 = coordinates[i - 1]\n            const p2 = coordinates[i]\n\n            // Simple Euclidean distance (not accurate for real-world distances)\n            const dx = p2.lng - p1.lng\n            const dy = p2.lat - p1.lat\n            const distance = Math.sqrt(dx * dx + dy * dy) * 111000 // Rough conversion to meters\n\n            route.summary.totalDistance += distance\n            route.summary.totalTime += distance / 50 // Assume 50 m/s speed\n          }\n\n          callback(null, [route])\n        } catch (error) {\n          callback(error, null)\n        }\n      },\n    }\n  }\n\n  // Reset the map container key when props change to force a complete re-render\n  useEffect(() => {\n    mapContainerKey.current = Math.random().toString(36).substring(2, 11)\n    // Force cleanup of any existing map\n    if (mapInstanceRef.current) {\n      try {\n        if (routingControlRef.current) {\n          mapInstanceRef.current.removeControl(routingControlRef.current)\n          routingControlRef.current = null\n        }\n        mapInstanceRef.current.remove()\n        mapInstanceRef.current = null\n      } catch (e) {\n        console.warn(\"Error cleaning up map on key change:\", e)\n      }\n    }\n    // Reset map ready state\n    setMapReady(false)\n    // Check container dimensions after a short delay\n    setTimeout(() => {\n      if (mapRef.current && mapRef.current.clientWidth > 0 && mapRef.current.clientHeight > 0) {\n        setMapReady(true)\n      }\n    }, 100)\n  }, [center, zoom, initialPolyline, readOnly])\n\n  return (\n    <div className=\"relative\">\n      <div\n        key={mapContainerKey.current}\n        ref={mapRef}\n        className=\"h-[500px] w-full rounded-md border\"\n      />\n\n      {/* Rate limiting status notification */}\n      {routingStatus === 'rate-limited' && (\n        <div className=\"absolute top-2 left-2 right-2 bg-yellow-100 border border-yellow-400 text-yellow-800 px-3 py-2 rounded-md text-sm z-[1001]\">\n          <div className=\"flex items-center\">\n            <span className=\"font-medium\">⚠️ Rate Limited:</span>\n            <span className=\"ml-2\">Using direct routing due to API limits. Routes may not follow roads exactly.</span>\n          </div>\n        </div>\n      )}\n\n      {!readOnly && (\n        <>\n          <div className={`absolute ${routingStatus === 'rate-limited' ? 'top-16' : 'top-4'} left-0 right-0 mx-auto w-fit bg-white p-3 rounded-md shadow-md text-sm z-[1000]`}>\n            <div className=\"flex items-center font-medium text-center\">\n              <MapIcon className=\"h-5 w-5 mr-2 text-blue-500\" />\n              <span>Click on the map to add waypoints. Drag waypoints to adjust the route.</span>\n            </div>\n          </div>\n          <div className=\"absolute bottom-4 left-4 bg-white p-3 rounded-md shadow-md text-sm\">\n            <div className=\"font-medium mb-1 text-gray-700\">How to create a route:</div>\n            <div className=\"flex items-center\">\n              <span className=\"bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2\">\n                1\n              </span>\n              <span>Click to add waypoints</span>\n            </div>\n            <div className=\"flex items-center mt-1\">\n              <span className=\"bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2\">\n                2\n              </span>\n              <span>Drag markers to adjust</span>\n            </div>\n            <div className=\"flex items-center mt-1\">\n              <span className=\"bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2\">\n                3\n              </span>\n              <span>Use \"Direct Route\" button if routing fails</span>\n            </div>\n            {routingStatus === 'rate-limited' && (\n              <div className=\"flex items-center mt-1\">\n                <span className=\"bg-yellow-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-2\">\n                  ⚠\n                </span>\n                <span>Rate limited - using direct routes</span>\n              </div>\n            )}\n          </div>\n        </>\n      )}\n    </div>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;;AAqBA,qCAAqC;AACrC,MAAM,eAAe;IACnB,QAAQ,SAAU,MAAa,EAAE,SAAkB;QACjD,IAAI,CAAC,OAAO,MAAM,EAAE,OAAO;QAE3B,MAAM,SAAS,KAAK,GAAG,CAAC,IAAI,cAAc,YAAY,YAAY;QAElE,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;QAE5F,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE;YACvB,MAAM,IAAI,MAAM,CAAC,EAAE;YACnB,UAAU,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE;QAC/F;QAEA,OAAO;IACT;IAEA,aAAa,SAAU,GAAW,EAAE,GAAW,EAAE,MAAc,EAAE,OAAgB,EAAE,OAAgB;QACjG,8BAA8B;QAC9B,MAAM,KAAK,KAAK,CAAC,MAAM;QACvB,MAAM,KAAK,KAAK,CAAC,MAAM;QAEvB,eAAe;QACf,MAAM,WAAW,MAAM,CAAC,YAAY,YAAY,KAAK,KAAK,CAAC,UAAU,UAAU,CAAC;QAChF,MAAM,WAAW,MAAM,CAAC,YAAY,YAAY,KAAK,KAAK,CAAC,UAAU,UAAU,CAAC;QAEhF,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,IAAI,CAAC,kBAAkB,CAAC;IACrE;IAEA,oBAAoB,SAAU,GAAW;QACvC,IAAI,UAAU,OAAO;QACrB,IAAI,MAAM,GAAG;YACX,UAAU,CAAC;QACb;QACA,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B;IAEA,cAAc,CAAC;QACb,IAAI,eAAe;QACnB,MAAO,OAAO,KAAM;YAClB,gBAAgB,OAAO,YAAY,CAAC,CAAC,OAAQ,MAAM,IAAK,IAAI;YAC5D,QAAQ;QACV;QACA,gBAAgB,OAAO,YAAY,CAAC,MAAM;QAC1C,OAAO;IACT;IAEA,QAAQ,CAAC,SAAiB;QACxB,IAAI,CAAC,QAAQ,MAAM,EAAE,OAAO,EAAE;QAE9B,MAAM,SAAS,KAAK,GAAG,CAAC,IAAI,cAAc,YAAY,YAAY;QAClE,MAAM,MAAM,QAAQ,MAAM;QAC1B,IAAI,QAAQ;QACZ,IAAI,MAAM;QACV,IAAI,MAAM;QACV,MAAM,SAAS,EAAE;QAEjB,MAAO,QAAQ,IAAK;YAClB,IAAI;YACJ,IAAI,QAAQ;YACZ,IAAI,SAAS;YAEb,GAAG;gBACD,IAAI,QAAQ,UAAU,CAAC,WAAW;gBAClC,UAAU,CAAC,IAAI,IAAI,KAAK;gBACxB,SAAS;YACX,QAAS,KAAK,KAAK;YAEnB,MAAM,WAAW,SAAS,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,UAAU;YACzD,OAAO;YAEP,QAAQ;YACR,SAAS;YAET,GAAG;gBACD,IAAI,QAAQ,UAAU,CAAC,WAAW;gBAClC,UAAU,CAAC,IAAI,IAAI,KAAK;gBACxB,SAAS;YACX,QAAS,KAAK,KAAK;YAEnB,MAAM,WAAW,SAAS,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,UAAU;YACzD,OAAO;YAEP,OAAO,IAAI,CAAC;gBAAC,MAAM;gBAAQ,MAAM;aAAO;QAC1C;QAEA,OAAO;IACT;AACF;AAUO,SAAS,WAAW,EACzB,eAAe,EACf,gBAAgB,EAChB,SAAS;IAAC;IAAQ;CAAQ,EAC1B,OAAO,EAAE,EACT,WAAW,KAAK,EACA;;IAMhB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAC9B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IACnC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IACtC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAE/E,gCAAgC;IAChC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0C;IAE3F,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,aAAkB,eAAe,CAAC,OAAO,OAAO,EAAE;YAEtD,wCAAwC;YACxC,MAAM;0DAAoB;oBACxB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,WAAW,GAAG,KAAK,OAAO,OAAO,CAAC,YAAY,GAAG,GAAG;wBACvF,YAAY;oBACd,OAAO;wBACL,wBAAwB;wBACxB,WAAW,mBAAmB;oBAChC;gBACF;;YAEA;YAEA;wCAAO;oBACL,YAAY;gBACd;;QACF;+BAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,mBAAmB;YACnB,UAAU,OAAO,GAAG;YAEpB;wCAAO;oBACL,qBAAqB;oBACrB,UAAU,OAAO,GAAG;oBAEpB,yBAAyB;oBACzB,IAAI,eAAe,OAAO,EAAE;wBAC1B,IAAI;4BACF,IAAI,kBAAkB,OAAO,EAAE;gCAC7B,eAAe,OAAO,CAAC,aAAa,CAAC,kBAAkB,OAAO;gCAC9D,kBAAkB,OAAO,GAAG;4BAC9B;4BACA,eAAe,OAAO,CAAC,MAAM;4BAC7B,eAAe,OAAO,GAAG;wBAC3B,EAAE,OAAO,GAAG;4BACV,QAAQ,IAAI,CAAC,qCAAqC;wBACpD;oBACF;gBACF;;QACF;+BAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,aAAkB,eAAe,CAAC,OAAO,OAAO,IAAI,CAAC,UAAU;YAEnE,IAAI,MAAW;YACf,IAAI,UAAe;YAEnB,6BAA6B;YAC7B,MAAM;gDAAU;oBACd,IAAI;wBACF,QAAQ,GAAG,CAAC;wBAEZ,4DAA4D;wBAC5D,IAAI,eAAe,OAAO,EAAE;4BAC1B,QAAQ,GAAG,CAAC;4BACZ,IAAI;gCACF,IAAI,kBAAkB,OAAO,EAAE;oCAC7B,eAAe,OAAO,CAAC,aAAa,CAAC,kBAAkB,OAAO;oCAC9D,kBAAkB,OAAO,GAAG;gCAC9B;gCACA,eAAe,OAAO,CAAC,MAAM;gCAC7B,eAAe,OAAO,GAAG;4BAC3B,EAAE,OAAO,GAAG;gCACV,QAAQ,IAAI,CAAC,mCAAmC;4BAClD;wBACF;wBAEA,6BAA6B;wBAC7B,IAAI,OAAO,OAAO,EAAE;4BAClB,OAAO,OAAO,CAAC,SAAS,GAAG;4BAC3B,yCAAyC;4BACzC,IAAI,OAAO,OAAO,CAAC,WAAW,EAAE;gCAC9B,OAAO,OAAO,OAAO,CAAC,WAAW;4BACnC;wBACF;wBAEA,mBAAmB;wBACnB,MAAM,IAAI,CAAC,oJAAuB,EAAE,OAAO;wBAC3C,QAAQ,GAAG,CAAC;wBAEZ,qBAAqB;wBACrB,IAAI;4BACF,2BAA2B;4BAC3B;4BACA,2BAA2B;4BAC3B;4BACA,QAAQ,GAAG,CAAC;4BAEZ,mCAAmC;4BACnC,OAAO,AAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAS,WAAW;4BACpD,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;gCAC1B,eAAe;gCACf,SAAS;gCACT,WAAW;4BACb;wBACF,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,uBAAuB;4BACrC,IAAI,OAAO,OAAO,EAAE;gCAClB,OAAO,OAAO,CAAC,SAAS,GACtB;4BACJ;4BACA;wBACF;wBAEA,0DAA0D;wBAC1D,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,OAAO,CAAC,YAAY,EAAE;4BAClF,QAAQ,KAAK,CAAC;4BACd;wBACF;wBAEA,wCAAwC;wBACxC,MAAM,EAAE,GAAG,CAAC,OAAO,OAAO,EAAE;4BAC1B,QAAQ;4BACR,MAAM;4BACN,aAAa;4BACb,oBAAoB;4BACpB,eAAe;4BACf,qBAAqB;4BACrB,cAAc;wBAChB;wBAEA,4BAA4B;wBAC5B,eAAe,OAAO,GAAG;wBACzB,QAAQ,GAAG,CAAC;wBAEZ,+EAA+E;wBAC/E;4DAAW;gCACT,IAAI,UAAU,OAAO,IAAI,KAAK;oCAC5B,IAAI,cAAc;oCAClB,QAAQ,GAAG,CAAC;gCACd;4BACF;2DAAG;wBAEH,iBAAiB;wBACjB,EAAE,SAAS,CAAC,sDAAsD;4BAChE,aAAa;4BACb,SAAS;wBACX,GAAG,KAAK,CAAC;wBAET,iCAAiC;wBACjC,IAAI;4BACF;4BACA,QAAQ,GAAG,CAAC;wBACd,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,0CAA0C;4BACxD,IAAI,OAAO,OAAO,EAAE;gCAClB,OAAO,OAAO,CAAC,SAAS,GACtB;4BACJ;4BACA;wBACF;wBAEA,uBAAuB;wBACvB,IAAI,mBAA0B,EAAE;wBAEhC,0DAA0D;wBAC1D,IAAI,mBAAmB,gBAAgB,MAAM,GAAG,GAAG;4BACjD,IAAI;gCACF,MAAM,cAAc,aAAa,MAAM,CAAC;gCACxC,qEAAqE;gCACrE,IAAI,YAAY,MAAM,GAAG,GAAG;oCAC1B,uCAAuC;oCACvC,iBAAiB,IAAI,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE;oCAEnE,iDAAiD;oCACjD,IAAI,YAAY,MAAM,GAAG,IAAI;wCAC3B,MAAM,OAAO,KAAK,KAAK,CAAC,YAAY,MAAM,GAAG;wCAC7C,IAAK,IAAI,IAAI,MAAM,IAAI,YAAY,MAAM,GAAG,MAAM,KAAK,KAAM;4CAC3D,iBAAiB,IAAI,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE;wCACrE;oCACF;oCAEA,iBAAiB;oCACjB,IAAI,YAAY,MAAM,GAAG,GAAG;wCAC1B,iBAAiB,IAAI,CACnB,EAAE,MAAM,CAAC,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,EAAE,EAAE,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,EAAE;oCAE3F;gCACF;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,4BAA4B;gCAC1C,gDAAgD;gCAChD,mBAAmB,EAAE;4BACvB;wBACF;wBAEA,uEAAuE;wBACvE,IAAI,iBAAiB,MAAM,KAAK,KAAK,CAAC,UAAU;4BAC9C,kDAAkD;4BAClD,mBAAmB;gCACjB,EAAE,MAAM,CAAC,QAAQ;gCACjB,EAAE,MAAM,CAAC,SAAS;6BACnB;wBACH;wBAEA,2BAA2B;wBAC3B,MAAM,iBAAiB;wBAEvB,0CAA0C;wBAC1C,mBAAmB,iBAAiB,MAAM;4DAAC,CAAC;gCAC1C,OACE,MACA,GAAG,GAAG,IACN,GAAG,GAAG,IACN,OAAO,GAAG,GAAG,KAAK,YAClB,OAAO,GAAG,GAAG,KAAK,YAClB,CAAC,MAAM,GAAG,GAAG,KACb,CAAC,MAAM,GAAG,GAAG;4BAEjB;;wBAEA,sDAAsD;wBACtD,IAAI,iBAAiB,MAAM,GAAG,KAAK,CAAC,UAAU;4BAC5C,mBAAmB;gCACjB,EAAE,MAAM,CAAC,QAAQ;gCACjB,EAAE,MAAM,CAAC,SAAS;6BACnB;wBACH;wBAEA,oDAAoD;wBACpD,IAAI;wBACJ,IAAI,kBAAkB;wBACtB,MAAM,uBAAuB,KAAK,qCAAqC;;wBACvE,IAAI,eAAe;wBAEnB,IAAI;4BACF,4DAA4D;4BAC5D,MAAM,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC;gCAClC,YAAY;gCACZ,SAAS;gCACT,SAAS,KAAK;gCACd,2BAA2B;4BAC7B;4BAEA,iDAAiD;4BACjD,MAAM,gBAAgB,WAAW,KAAK;4BACtC,SAAS;gCACP,GAAG,UAAU;gCACb,KAAK;oEAAE,SAAS,SAAgB,EAAE,QAAkB,EAAE,OAAa;wCACjE,MAAM,MAAM,KAAK,GAAG;wCAEpB,8CAA8C;wCAC9C,IAAI,cAAc;4CAChB,QAAQ,GAAG,CAAC;4CACZ,iBAAiB;4CACjB,OAAO,eAAe,KAAK,CAAC,WAAW;wCACzC;wCAEA,qDAAqD;wCACrD,MAAM,uBAAuB,MAAM;wCACnC,IAAI,uBAAuB,sBAAsB;4CAC/C,MAAM,QAAQ,uBAAuB;4CACrC,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,MAAM,EAAE,CAAC;4CAEpD;gFAAW;oDACT,kBAAkB,KAAK,GAAG;oDAC1B,cAAc,IAAI,CAAC,IAAI,EAAE;wFAAW,SAAS,KAAU,EAAE,MAAW;4DAClE,IAAI,SAAS,CAAC,MAAM,MAAM,KAAK,OAAO,MAAM,OAAO,EAAE,SAAS,MAAM,GAAG;gEACrE,QAAQ,IAAI,CAAC;gEACb,eAAe;gEACf,iBAAiB;gEACjB,wCAAwC;gEACxC;oGAAW;wEACT,eAAe;wEACf,iBAAiB;wEACjB,QAAQ,GAAG,CAAC;oEACd;mGAAG,IAAI,KAAK;gEAEZ,OAAO,eAAe,KAAK,CAAC,WAAW;4DACzC;4DACA,iBAAiB;4DACjB,SAAS,OAAO;wDAClB;;gDACF;+EAAG;4CACH;wCACF;wCAEA,kBAAkB;wCAClB,cAAc,IAAI,CAAC,IAAI,EAAE;4EAAW,SAAS,KAAU,EAAE,MAAW;gDAClE,IAAI,SAAS,CAAC,MAAM,MAAM,KAAK,OAAO,MAAM,OAAO,EAAE,SAAS,MAAM,GAAG;oDACrE,QAAQ,IAAI,CAAC;oDACb,eAAe;oDACf,iBAAiB;oDACjB,wCAAwC;oDACxC;wFAAW;4DACT,eAAe;4DACf,iBAAiB;4DACjB,QAAQ,GAAG,CAAC;wDACd;uFAAG,IAAI,KAAK;oDAEZ,OAAO,eAAe,KAAK,CAAC,WAAW;gDACzC;gDACA,iBAAiB;gDACjB,SAAS,OAAO;4CAClB;;oCACF;;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,IAAI,CAAC,iDAAiD;4BAC9D,SAAS;wBACX;wBAEA,0DAA0D;wBAC1D,iFAAiF;wBACjF,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC;4BAC1B,WAAW;4BACX,oBAAoB;4BACpB,kBAAkB;4BAClB,mBAAmB;4BACnB,aAAa;gCACX,QAAQ;oCAAC;wCAAE,OAAO;wCAAW,QAAQ;oCAAE;iCAAE;gCACzC,mBAAmB;gCACnB,uBAAuB;4BACzB;4BACA,cAAc,CAAC;4BACf,oBAAoB,CAAC;4BACrB,kBAAkB;4BAClB,QAAQ;4BACR,YAAY;gEAAE,CAAC,GAAW,IAAS;oCACjC,gEAAgE;oCAChE,IAAI;oCAEJ,IAAI,MAAM,GAAG;wCACX,sBAAsB;wCACtB,OAAO,EAAE,OAAO,CAAC;4CACf,WAAW;4CACX,MAAM;4CACN,UAAU;gDAAC;gDAAI;6CAAG;4CAClB,YAAY;gDAAC;gDAAG;6CAAE;wCACpB;oCACF,OAAO,IAAI,MAAM,IAAI,GAAG;wCACtB,kBAAkB;wCAClB,OAAO,EAAE,OAAO,CAAC;4CACf,WAAW;4CACX,MAAM;4CACN,UAAU;gDAAC;gDAAI;6CAAG;4CAClB,YAAY;gDAAC;gDAAG;6CAAE;wCACpB;oCACF,OAAO;wCACL,6BAA6B;wCAC7B,OAAO,EAAE,OAAO,CAAC;4CACf,WAAW;4CACX,MAAM;4CACN,UAAU;gDAAC;gDAAI;6CAAG;4CAClB,YAAY;gDAAC;gDAAG;6CAAE;wCACpB;oCACF;oCAEA,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,EAAE;wCACzB,WAAW,CAAC;wCACZ,MAAM;oCACR;gCACF;;wBACF;wBAEA,4DAA4D;wBAC5D,IAAI,KAAK;4BACP,QAAQ,KAAK,CAAC;4BACd,2CAA2C;4BAC3C,kBAAkB,OAAO,GAAG;wBAC9B,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd;wBACF;wBAEA,uBAAuB;wBACvB,QAAQ,EAAE,CAAC;4DAAe,CAAC;gCACzB,IAAI,YAAY,CAAC,UAAU,OAAO,EAAE;gCAEpC,MAAM,SAAS,EAAE,MAAM;gCACvB,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;oCAC/B,MAAM,gBAAgB,MAAM,CAAC,EAAE,CAAC,6BAA6B;;oCAC7D,MAAM,cAAc,cAAc,WAAW;oCAE7C,6CAA6C;oCAC7C,MAAM,SAAS,YAAY,GAAG;+EAAC,CAAC,QAAe;gDAAC,MAAM,GAAG;gDAAE,MAAM,GAAG;6CAAC;;oCAErE,sBAAsB;oCACtB,MAAM,UAAU,aAAa,MAAM,CAAC;oCACpC,iBAAiB;gCACnB;4BACF;;wBAEA,6DAA6D;wBAC7D,IAAI,CAAC,YAAY,KAAK;4BACpB,oCAAoC;4BAEpC,cAAc;4BACd,MAAM,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC;gCACnC,SAAS;oCACP,UAAU;gCACZ;gCAEA,KAAK;gFAAE;wCACL,MAAM,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO;wCAC1C,UAAU,KAAK,CAAC,eAAe,GAAG;wCAClC,UAAU,KAAK,CAAC,OAAO,GAAG;wCAC1B,UAAU,KAAK,CAAC,QAAQ,GAAG;wCAC3B,UAAU,KAAK,CAAC,MAAM,GAAG;wCACzB,UAAU,KAAK,CAAC,YAAY,GAAG;wCAC/B,UAAU,KAAK,CAAC,SAAS,GAAG;wCAC5B,UAAU,KAAK,CAAC,YAAY,GAAG;wCAC/B,UAAU,SAAS,GACjB;wCAEF,UAAU,OAAO;wFAAG;gDAClB,MACE;4CAEJ;;wCAEA,OAAO;oCACT;;4BACF;4BAEA,+CAA+C;4BAC/C,MAAM,qBAAqB,EAAE,OAAO,CAAC,MAAM,CAAC;gCAC1C,SAAS;oCACP,UAAU;gCACZ;gCAEA,KAAK;uFAAE;wCACL,MAAM,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO;wCAC1C,UAAU,KAAK,CAAC,eAAe,GAAG;wCAClC,UAAU,KAAK,CAAC,OAAO,GAAG;wCAC1B,UAAU,KAAK,CAAC,QAAQ,GAAG;wCAC3B,UAAU,KAAK,CAAC,MAAM,GAAG;wCACzB,UAAU,KAAK,CAAC,YAAY,GAAG;wCAC/B,UAAU,KAAK,CAAC,SAAS,GAAG;wCAC5B,UAAU,SAAS,GACjB;wCAEF,UAAU,OAAO;+FAAG;gDAClB,0CAA0C;gDAC1C,IAAI,CAAC,SAAS;gDAEd,MAAM,YAAY,QACf,YAAY,GACZ,MAAM;iHACL,CAAC,KACC,MAAM,GAAG,MAAM,IAAI,OAAO,GAAG,MAAM,CAAC,GAAG,KAAK,YAAY,OAAO,GAAG,MAAM,CAAC,GAAG,KAAK;;gDAGvF,IAAI,UAAU,MAAM,IAAI,GAAG;oDACzB,6CAA6C;oDAC7C,IAAI,KAAK;wDACP,EAAE,QAAQ,CACR,UAAU,GAAG;+GAAC,CAAC,KAAY;oEAAC,GAAG,MAAM,CAAC,GAAG;oEAAE,GAAG,MAAM,CAAC,GAAG;iEAAC;+GACzD;4DACE,OAAO;4DACP,QAAQ;4DACR,SAAS;4DACT,WAAW;4DACX,SAAS;wDACX,GACA,KAAK,CAAC;oDACV;oDAEA,2CAA2C;oDAC3C,MAAM,SAAS,UAAU,GAAG;kHAAC,CAAC,KAAY;gEAAC,GAAG,MAAM,CAAC,GAAG;gEAAE,GAAG,MAAM,CAAC,GAAG;6DAAC;;oDAExE,sBAAsB;oDACtB,MAAM,UAAU,aAAa,MAAM,CAAC;oDACpC,iBAAiB;oDAEjB,MAAM;gDACR,OAAO;oDACL,MAAM;gDACR;4CACF;;wCAEA,OAAO;oCACT;;4BACF;4BAEA,IAAI;gCACF,IAAI,cAAc,KAAK,CAAC;gCACxB,IAAI,qBAAqB,KAAK,CAAC;4BACjC,EAAE,OAAO,GAAG;gCACV,QAAQ,IAAI,CAAC,kCAAkC;4BACjD;4BAEA,uCAAuC;4BACvC,IAAI,qBAA4C;4BAChD,MAAM;qFAAuB;oCAC3B,IAAI,oBAAoB;wCACtB,aAAa;oCACf;oCACA,qBAAqB;6FAAW;4CAC9B,IAAI,WAAW,UAAU,OAAO,EAAE;gDAChC,IAAI;oDACF,qCAAqC;oDACrC,MAAM,YAAY,QAAQ,YAAY;oDACtC,IAAI,UAAU,MAAM,IAAI,GAAG;wDACzB,QAAQ,KAAK;oDACf;gDACF,EAAE,OAAO,OAAO;oDACd,QAAQ,KAAK,CAAC,yBAAyB;gDACzC;4CACF;wCACF;4FAAG,MAAM,2CAA2C;;gCACtD;;4BAEA,+BAA+B;4BAC/B,IAAI,EAAE,CAAC;gEAAS,CAAC;oCACf,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,SAAS;oCAEpC,IAAI;wCACF,MAAM,YAAY,QAAQ,YAAY;wCACtC,MAAM,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,cAAc,CAAC,UAAU,MAAM,GAAG,CAAC;wCAEpF,+CAA+C;wCAC/C,QAAQ,eAAe,CAAC,UAAU,MAAM,EAAE,GAAG;wCAE7C,iCAAiC;wCACjC;oCACF,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,6BAA6B;oCAC7C;gCACF;;4BAEA,sDAAsD;4BACtD,IAAI,SAAS;gCACX,QAAQ,EAAE,CAAC;oEAAgB;oCACzB,oCAAoC;oCACtC;;gCAEA,QAAQ,EAAE,CAAC;oEAAmB;wCAC5B,+BAA+B;wCAC/B;oCACF;;4BACF;wBACF;wBAEA,wBAAwB;wBACxB,IAAI,SAAS;4BACX,QAAQ,EAAE,CAAC;gEAAgB,CAAC;oCAC1B,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,KAAK;oCAEhC,QAAQ,KAAK,CAAC,kBAAkB,EAAE,KAAK;oCAEvC,iDAAiD;oCACjD,IAAI;wCACF,wBAAwB;wCACxB,IAAI,CAAC,SAAS;wCAEd,MAAM,mBAAmB,QACtB,YAAY,GACZ,MAAM;6FACL,CAAC,KACC,MAAM,GAAG,MAAM,IAAI,OAAO,GAAG,MAAM,CAAC,GAAG,KAAK,YAAY,OAAO,GAAG,MAAM,CAAC,GAAG,KAAK;;wCAGvF,IAAI,iBAAiB,MAAM,IAAI,GAAG;4CAChC,6CAA6C;4CAC7C,EAAE,QAAQ,CACR,iBAAiB,GAAG;gFAAC,CAAC,KAAY;wDAAC,GAAG,MAAM,CAAC,GAAG;wDAAE,GAAG,MAAM,CAAC,GAAG;qDAAC;gFAChE;gDACE,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,WAAW;gDACX,SAAS;4CACX,GACA,KAAK,CAAC;4CAER,0EAA0E;4CAC1E,IAAI,YAAY,GACd,YAAY;4CACd,iBAAiB,OAAO;gFAAC,CAAC;oDACxB,aAAa,GAAG,MAAM,CAAC,GAAG;oDAC1B,aAAa,GAAG,MAAM,CAAC,GAAG;gDAC5B;;4CACA,aAAa,iBAAiB,MAAM;4CACpC,aAAa,iBAAiB,MAAM;4CACpC,MAAM,SAAS,EAAE,MAAM,CAAC,WAAW;4CAEnC,EAAE,MAAM,CAAC,QAAQ;gDACf,MAAM,EAAE,OAAO,CAAC;oDACd,WAAW;oDACX,MAAM;oDACN,UAAU;wDAAC;wDAAK;qDAAG;oDACnB,YAAY;wDAAC;wDAAK;qDAAG;gDACvB;4CACF,GAAG,KAAK,CAAC;4CAET,2CAA2C;4CAC3C,MAAM,SAAS,iBAAiB,GAAG;uFAAC,CAAC,KAAY;wDAAC,GAAG,MAAM,CAAC,GAAG;wDAAE,GAAG,MAAM,CAAC,GAAG;qDAAC;;4CAE/E,sBAAsB;4CACtB,MAAM,UAAU,aAAa,MAAM,CAAC;4CACpC,iBAAiB;wCACnB;oCACF,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,gCAAgC;oCAChD;gCACF;;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,IAAI,OAAO,OAAO,EAAE;4BAClB,OAAO,OAAO,CAAC,SAAS,GACtB;wBACJ;oBACF;gBACF;;YAEA,2BAA2B;YAC3B;YAEA,2CAA2C;YAC3C;wCAAO;oBACL,qBAAqB;oBACrB,UAAU,OAAO,GAAG;oBAEpB,wDAAwD;oBACxD,MAAM;2DAAa;4BACjB,IAAI;gCACF,QAAQ,GAAG,CAAC;gCAEZ,4CAA4C;gCAC5C,IAAI,kBAAkB,OAAO,IAAI,eAAe,OAAO,EAAE;oCACvD,QAAQ,GAAG,CAAC;oCACZ,IAAI;wCACF,eAAe,OAAO,CAAC,aAAa,CAAC,kBAAkB,OAAO;oCAChE,EAAE,OAAO,GAAG;wCACV,QAAQ,IAAI,CAAC,mCAAmC;oCAClD;oCACA,kBAAkB,OAAO,GAAG;gCAC9B;gCAEA,mCAAmC;gCACnC,IAAI,eAAe,OAAO,EAAE;oCAC1B,QAAQ,GAAG,CAAC;oCACZ,IAAI;wCACF,eAAe,OAAO,CAAC,MAAM;oCAC/B,EAAE,OAAO,GAAG;wCACV,QAAQ,IAAI,CAAC,uBAAuB;oCACtC;oCACA,eAAe,OAAO,GAAG;gCAC3B;gCAEA,2BAA2B;gCAC3B,IAAI,OAAO,OAAO,EAAE;oCAClB,OAAO,OAAO,CAAC,SAAS,GAAG;oCAC3B,yCAAyC;oCACzC,IAAI,OAAO,OAAO,CAAC,WAAW,EAAE;wCAC9B,OAAO,OAAO,OAAO,CAAC,WAAW;oCACnC;gCACF;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,6BAA6B;4BAC7C;wBACF;;oBAEA,qEAAqE;oBACrE;gBACF;;QACF;+BAAG;QAAC;QAAQ;QAAM;QAAiB;QAAkB;QAAU;KAAS;IAExE,kEAAkE;IAClE,MAAM,uBAAuB;QAC3B,sFAAsF;QACtF,OAAO;YACL,OAAO,CAAC,WAAgB;gBACtB,IAAI;oBACF,4CAA4C;oBAC5C,MAAM,cAAqB,EAAE;oBAE7B,UAAU,OAAO,CAAC,CAAC;wBACjB,IAAI,MAAM,GAAG,MAAM,EAAE;4BACnB,YAAY,IAAI,CAAC;gCACf,KAAK,GAAG,MAAM,CAAC,GAAG;gCAClB,KAAK,GAAG,MAAM,CAAC,GAAG;4BACpB;wBACF;oBACF;oBAEA,+BAA+B;oBAC/B,MAAM,QAAQ;wBACZ,MAAM;wBACN,aAAa;wBACb,SAAS;4BACP,eAAe;4BACf,WAAW;wBACb;wBACA,gBAAgB;wBAChB,WAAW;oBACb;oBAEA,4BAA4B;oBAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;wBAC3C,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE;wBAC7B,MAAM,KAAK,WAAW,CAAC,EAAE;wBAEzB,oEAAoE;wBACpE,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;wBAC1B,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;wBAC1B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,MAAM,OAAO,6BAA6B;;wBAEpF,MAAM,OAAO,CAAC,aAAa,IAAI;wBAC/B,MAAM,OAAO,CAAC,SAAS,IAAI,WAAW,GAAG,sBAAsB;;oBACjE;oBAEA,SAAS,MAAM;wBAAC;qBAAM;gBACxB,EAAE,OAAO,OAAO;oBACd,SAAS,OAAO;gBAClB;YACF;QACF;IACF;IAEA,8EAA8E;IAC9E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,gBAAgB,OAAO,GAAG,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YAClE,oCAAoC;YACpC,IAAI,eAAe,OAAO,EAAE;gBAC1B,IAAI;oBACF,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,eAAe,OAAO,CAAC,aAAa,CAAC,kBAAkB,OAAO;wBAC9D,kBAAkB,OAAO,GAAG;oBAC9B;oBACA,eAAe,OAAO,CAAC,MAAM;oBAC7B,eAAe,OAAO,GAAG;gBAC3B,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,wCAAwC;gBACvD;YACF;YACA,wBAAwB;YACxB,YAAY;YACZ,iDAAiD;YACjD;wCAAW;oBACT,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,WAAW,GAAG,KAAK,OAAO,OAAO,CAAC,YAAY,GAAG,GAAG;wBACvF,YAAY;oBACd;gBACF;uCAAG;QACL;+BAAG;QAAC;QAAQ;QAAM;QAAiB;KAAS;IAE5C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAEC,KAAK;gBACL,WAAU;eAFL,gBAAgB,OAAO;;;;;YAM7B,kBAAkB,gCACjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAc;;;;;;sCAC9B,6LAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;YAK5B,CAAC,0BACA;;kCACE,6LAAC;wBAAI,WAAW,CAAC,SAAS,EAAE,kBAAkB,iBAAiB,WAAW,QAAQ,gFAAgF,CAAC;kCACjK,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAiC;;;;;;0CAChD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAqF;;;;;;kDAGrG,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoF;;;;;;kDAGpG,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAmF;;;;;;kDAGnG,6LAAC;kDAAK;;;;;;;;;;;;4BAEP,kBAAkB,gCACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;kDAGtG,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GAzzBgB;KAAA", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Asif/Projects/BUS%20APP/bus-app-frontend/src/services/routeService.ts"], "sourcesContent": ["import { apiRequest } from '@/lib/api';\n\nexport interface Route {\n  id: string;\n  routeName: string;\n  routeNumber?: string;\n  startPoint: string;\n  endPoint: string;\n  distanceKm: number;\n  estimatedTime: number;\n  description?: string;\n  isActive: boolean;\n  color?: string;\n  polyline?: string;\n  createdAt: string;\n  updatedAt: string;\n  stops?: Stop[];\n  buses?: Bus[];\n  _count?: {\n    buses: number;\n    stops: number;\n  };\n}\n\nexport interface Stop {\n  id: string;\n  stopName: string;\n  stopCode?: string;\n  latitude: number;\n  longitude: number;\n  stopOrder: number;\n  isActive: boolean;\n  description?: string;\n  amenities?: string[];\n}\n\nexport interface Bus {\n  id: string;\n  busNumber: string;\n  busName: string;\n  status: string;\n  isActive: boolean;\n  currentLocation?: {\n    latitude: number;\n    longitude: number;\n    timestamp: string;\n  };\n}\n\nexport interface CreateRouteData {\n  routeName: string;\n  routeNumber?: string;\n  startPoint: string;\n  endPoint: string;\n  distanceKm: number;\n  estimatedTime: number;\n  description?: string;\n  color?: string;\n  polyline?: string;\n}\n\nexport interface UpdateRouteData extends CreateRouteData {\n  isActive?: boolean;\n}\n\nexport const routeService = {\n  getRoutes: async (): Promise<Route[]> => {\n    return apiRequest<Route[]>('/routes');\n  },\n\n  getRoute: async (id: string): Promise<Route> => {\n    return apiRequest<Route>(`/routes/${id}`);\n  },\n\n  createRoute: async (data: CreateRouteData): Promise<Route> => {\n    return apiRequest<Route>('/routes', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  updateRoute: async (id: string, data: UpdateRouteData): Promise<Route> => {\n    return apiRequest<Route>(`/routes/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    });\n  },\n\n  deleteRoute: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/routes/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;;AAiEO,MAAM,eAAe;IAC1B,WAAW;QACT,OAAO,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAW;IAC7B;IAEA,UAAU,OAAO;QACf,OAAO,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAS,CAAC,QAAQ,EAAE,IAAI;IAC1C;IAEA,aAAa,OAAO;QAClB,OAAO,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAS,WAAW;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,aAAa,OAAO,IAAY;QAC9B,OAAO,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAS,CAAC,QAAQ,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,aAAa,OAAO;QAClB,OAAO,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;YACvC,QAAQ;QACV;IACF;AACF", "debugId": null}}, {"offset": {"line": 1262, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Asif/Projects/BUS%20APP/bus-app-frontend/src/app/admin/dashboard/routes/create/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { ArrowLeftIcon, SaveIcon } from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\nimport { RoutingMap } from '@/components/admin/map/RoutingMap';\nimport Link from 'next/link';\nimport { toast } from 'sonner';\nimport { CreateRouteData, routeService } from '@/services/routeService';\n\nexport default function CreateRoutePage() {\n  const router = useRouter();\n  const { user } = useAuth();\n\n  const [formData, setFormData] = useState<CreateRouteData>({\n    routeName: '',\n    routeNumber: '',\n    startPoint: '',\n    endPoint: '',\n    distanceKm: 0,\n    estimatedTime: 0,\n    description: '',\n    color: '#3b82f6', // Default blue color\n    polyline: '',\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n\n    // Handle numeric values\n    if (name === 'distanceKm' || name === 'estimatedTime') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value === '' ? 0 : parseFloat(value)\n      }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n  };\n\n  const handlePolylineChange = (polyline: string) => {\n    setFormData(prev => ({ ...prev, polyline }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Validate form\n      if (!formData.routeName || !formData.startPoint || !formData.endPoint ||\n          formData.distanceKm <= 0 || formData.estimatedTime <= 0) {\n        setError('Please fill in all required fields');\n        return;\n      }\n\n      // Create route using the service\n      const response = await routeService.createRoute(formData);\n\n      toast.success('Route created successfully');\n\n      // Redirect to route details page\n      router.push(`/admin/dashboard/routes/${response.id}`);\n    } catch (err: any) {\n      console.error('Error creating route:', err);\n      setError(err.message || 'Failed to create route. Please try again.');\n      toast.error('Failed to create route');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <ProtectedRoute allowedRoles={['admin']}>\n      <div className=\"container mx-auto py-8\">\n        <div className=\"flex justify-between items-center mb-8\">\n          <div className=\"flex items-center\">\n            <Button variant=\"ghost\" size=\"icon\" asChild className=\"mr-2\">\n              <Link href=\"/admin/dashboard/routes\">\n                <ArrowLeftIcon className=\"h-5 w-5\" />\n              </Link>\n            </Button>\n            <h1 className=\"text-3xl font-bold\">Create New Route</h1>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-destructive/15 text-destructive p-4 rounded-md mb-6\">\n            {error}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <div>\n                <Label htmlFor=\"routeName\">Route Name <span className=\"text-destructive\">*</span></Label>\n                <Input\n                  id=\"routeName\"\n                  name=\"routeName\"\n                  value={formData.routeName}\n                  onChange={handleChange}\n                  placeholder=\"e.g. Mumbai Central to Andheri\"\n                  required\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"routeNumber\">Route Number</Label>\n                <Input\n                  id=\"routeNumber\"\n                  name=\"routeNumber\"\n                  value={formData.routeNumber}\n                  onChange={handleChange}\n                  placeholder=\"e.g. R123\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"startPoint\">Start Point <span className=\"text-destructive\">*</span></Label>\n                <Input\n                  id=\"startPoint\"\n                  name=\"startPoint\"\n                  value={formData.startPoint}\n                  onChange={handleChange}\n                  placeholder=\"e.g. Mumbai Central\"\n                  required\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"endPoint\">End Point <span className=\"text-destructive\">*</span></Label>\n                <Input\n                  id=\"endPoint\"\n                  name=\"endPoint\"\n                  value={formData.endPoint}\n                  onChange={handleChange}\n                  placeholder=\"e.g. Andheri\"\n                  required\n                />\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"distanceKm\">Distance (km) <span className=\"text-destructive\">*</span></Label>\n                  <Input\n                    id=\"distanceKm\"\n                    name=\"distanceKm\"\n                    type=\"number\"\n                    step=\"0.1\"\n                    min=\"0\"\n                    value={formData.distanceKm}\n                    onChange={handleChange}\n                    placeholder=\"e.g. 15.5\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <Label htmlFor=\"estimatedTime\">Est. Time (min) <span className=\"text-destructive\">*</span></Label>\n                  <Input\n                    id=\"estimatedTime\"\n                    name=\"estimatedTime\"\n                    type=\"number\"\n                    min=\"1\"\n                    value={formData.estimatedTime}\n                    onChange={handleChange}\n                    placeholder=\"e.g. 45\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div>\n                <Label htmlFor=\"color\">Route Color</Label>\n                <div className=\"flex items-center gap-2\">\n                  <Input\n                    id=\"color\"\n                    name=\"color\"\n                    type=\"color\"\n                    value={formData.color}\n                    onChange={handleChange}\n                    className=\"w-12 h-10 p-1\"\n                  />\n                  <Input\n                    value={formData.color}\n                    onChange={handleChange}\n                    name=\"color\"\n                    className=\"flex-1\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <Label htmlFor=\"description\">Description</Label>\n                <Textarea\n                  id=\"description\"\n                  name=\"description\"\n                  value={formData.description}\n                  onChange={handleChange}\n                  placeholder=\"Enter route description\"\n                  rows={4}\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <Label>Route Path</Label>\n              <RoutingMap\n                onPolylineChange={handlePolylineChange}\n                center={[19.0760, 72.8777]} // Mumbai coordinates\n                zoom={12}\n              />\n              <p className=\"text-sm text-muted-foreground\">\n                Click on the map to add waypoints. Drag waypoints to adjust the route. The route will automatically follow roads.\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex justify-end gap-4\">\n            <Button\n              variant=\"outline\"\n              type=\"button\"\n              onClick={() => router.push('/admin/dashboard/routes')}\n            >\n              Cancel\n            </Button>\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? (\n                <>\n                  <span className=\"animate-spin mr-2\">⏳</span>\n                  Creating...\n                </>\n              ) : (\n                <>\n                  <SaveIcon className=\"mr-2 h-4 w-4\" />\n                  Create Route\n                </>\n              )}\n            </Button>\n          </div>\n        </form>\n      </div>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;;AAgBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,WAAW;QACX,aAAa;QACb,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,eAAe;QACf,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAEhC,wBAAwB;QACxB,IAAI,SAAS,gBAAgB,SAAS,iBAAiB;YACrD,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE,UAAU,KAAK,IAAI,WAAW;gBACxC,CAAC;QACH,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAM,CAAC;QACjD;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAS,CAAC;IAC5C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI;YACF,WAAW;YACX,SAAS;YAET,gBAAgB;YAChB,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,SAAS,QAAQ,IACjE,SAAS,UAAU,IAAI,KAAK,SAAS,aAAa,IAAI,GAAG;gBAC3D,SAAS;gBACT;YACF;YAEA,iCAAiC;YACjC,MAAM,WAAW,MAAM,kIAAA,CAAA,eAAY,CAAC,WAAW,CAAC;YAEhD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,iCAAiC;YACjC,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,SAAS,EAAE,EAAE;QACtD,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,IAAI,OAAO,IAAI;YACxB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,UAAc;QAAC,cAAc;YAAC;SAAQ;kBACrC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,OAAO;gCAAC,WAAU;0CACpD,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,uNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAG7B,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;;;;;;;;;;;;gBAItC,uBACC,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIL,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDAAY;sEAAW,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;8DACzE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,SAAS;oDACzB,UAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDAAa;sEAAY,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;8DAC3E,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDAAW;sEAAU,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;8DACvE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;;gEAAa;8EAAc,6LAAC;oEAAK,WAAU;8EAAmB;;;;;;;;;;;;sEAC7E,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,MAAK;4DACL,KAAI;4DACJ,OAAO,SAAS,UAAU;4DAC1B,UAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;;gEAAgB;8EAAgB,6LAAC;oEAAK,WAAU;8EAAmB;;;;;;;;;;;;sEAClF,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,KAAI;4DACJ,OAAO,SAAS,aAAa;4DAC7B,UAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;;;;;;;sDAKd,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;;;;;;sEAEZ,6LAAC,oIAAA,CAAA,QAAK;4DACJ,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,MAAK;4DACL,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,aAAY;oDACZ,MAAM;;;;;;;;;;;;;;;;;;8CAKZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,6LAAC,mJAAA,CAAA,aAAU;4CACT,kBAAkB;4CAClB,QAAQ;gDAAC;gDAAS;6CAAQ;4CAC1B,MAAM;;;;;;sDAER,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;8CAC5B;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,wBACC;;0DACE,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;4CAAQ;;qEAI9C;;0DACE,6LAAC,yMAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvD;GAhPwB;;QACP,qIAAA,CAAA,YAAS;QACP,kIAAA,CAAA,UAAO;;;KAFF", "debugId": null}}]}