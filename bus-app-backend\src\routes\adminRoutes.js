const express = require('express');
const router = express.Router();
const { createAdmin, loginAdmin } = require('../controllers/adminController');

/**
 * @route   POST /api/admins/initialize
 * @desc    Initialize first admin (one-time only)
 * @access  Private - Device-specific
 */
router.post('/initialize', createAdmin);

/**
 * @route   POST /api/admins/login
 * @desc    Admin login
 * @access  Public
 */
router.post('/login', loginAdmin);

module.exports = router;