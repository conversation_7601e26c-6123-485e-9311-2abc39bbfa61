const express = require('express');
const router = express.Router();
const stopController = require('../controllers/stopController');
const { authMiddleware } = require('../middleware/authMiddleware');
const { adminMiddleware } = require('../middleware/adminMiddleware');

/**
 * @route   GET /api/stops
 * @desc    Get all stops
 * @access  Private
 */
router.get('/', authMiddleware, stopController.getAllStops);

/**
 * @route   GET /api/stops/:id
 * @desc    Get stop by ID
 * @access  Private
 */
router.get('/:id', authMiddleware, stopController.getStopById);

/**
 * @route   GET /api/stops/:id/arrivals
 * @desc    Get estimated arrivals for a stop
 * @access  Private
 */
router.get('/:id/arrivals', authMiddleware, stopController.getStopArrivals);

/**
 * @route   POST /api/stops/:id/arrivals
 * @desc    Add estimated arrival for a stop
 * @access  Private/Admin/Driver
 */
router.post('/:id/arrivals', authMiddleware, stopController.addStopArrival);

/**
 * @route   PUT /api/stops/:id
 * @desc    Update a stop
 * @access  Private/Admin
 */
router.put('/:id', adminMiddleware, stopController.updateStop);

/**
 * @route   DELETE /api/stops/:id
 * @desc    Delete a stop
 * @access  Private/Admin
 */
router.delete('/:id', adminMiddleware, stopController.deleteStop);

module.exports = router;
