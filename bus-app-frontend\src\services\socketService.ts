// This service handles WebSocket connections for real-time updates
import { io, Socket } from 'socket.io-client';

// Types
interface BusLocation {
  busId: string;
  latitude: number;
  longitude: number;
  heading?: number;
  speed?: number;
  timestamp: string;
  source?: string;
  bus?: {
    busNumber: string;
    busName: string;
    routeId?: string;
  };
}

interface SocketService {
  socket: Socket | null;
  busLocationsSocket: Socket | null;
  connect: (token: string) => Promise<void>;
  disconnect: () => void;
  subscribeToBus: (busId: string) => void;
  unsubscribeFromBus: (busId: string) => void;
  subscribeToRoute: (routeId: string) => void;
  unsubscribeFromRoute: (routeId: string) => void;
  subscribeToNearbyBuses: (latitude: number, longitude: number, radius?: number) => void;
  updateLocation: (latitude: number, longitude: number, radius?: number) => void;
  onBusLocationUpdate: (callback: (data: BusLocation) => void) => void;
  offBusLocationUpdate: (callback?: (data: BusLocation) => void) => void;
}

// Socket.IO server URL
const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:5000';

// Create a singleton instance
let instance: SocketService | null = null;

// Socket service implementation
const createSocketService = (): SocketService => {
  let socket: Socket | null = null;
  let busLocationsSocket: Socket | null = null;
  
  return {
    socket,
    busLocationsSocket,
    
    // Connect to Socket.IO server
    connect: async (token: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        try {
          // Connect to main namespace
          socket = io(SOCKET_URL, {
            auth: { token }
          });
          
          // Connect to bus-locations namespace
          busLocationsSocket = io(`${SOCKET_URL}/bus-locations`, {
            auth: { token }
          });
          
          // Handle main socket connection
          socket.on('connect', () => {
            console.log('Connected to main socket namespace');
          });
          
          socket.on('connect_error', (error) => {
            console.error('Main socket connection error:', error);
            reject(error);
          });
          
          // Handle bus locations socket connection
          busLocationsSocket.on('connect', () => {
            console.log('Connected to bus-locations namespace');
            resolve();
          });
          
          busLocationsSocket.on('connect_error', (error) => {
            console.error('Bus locations socket connection error:', error);
            reject(error);
          });
        } catch (error) {
          console.error('Socket initialization error:', error);
          reject(error);
        }
      });
    },
    
    // Disconnect from Socket.IO server
    disconnect: () => {
      if (socket) {
        socket.disconnect();
        socket = null;
      }
      
      if (busLocationsSocket) {
        busLocationsSocket.disconnect();
        busLocationsSocket = null;
      }
    },
    
    // Subscribe to a specific bus
    subscribeToBus: (busId: string) => {
      if (busLocationsSocket) {
        busLocationsSocket.emit('subscribe-bus', busId);
        console.log(`Subscribed to bus: ${busId}`);
      }
    },
    
    // Unsubscribe from a specific bus
    unsubscribeFromBus: (busId: string) => {
      if (busLocationsSocket) {
        busLocationsSocket.emit('unsubscribe-bus', busId);
        console.log(`Unsubscribed from bus: ${busId}`);
      }
    },
    
    // Subscribe to a specific route
    subscribeToRoute: (routeId: string) => {
      if (busLocationsSocket) {
        busLocationsSocket.emit('subscribe-route', routeId);
        console.log(`Subscribed to route: ${routeId}`);
      }
    },
    
    // Unsubscribe from a specific route
    unsubscribeFromRoute: (routeId: string) => {
      if (busLocationsSocket) {
        busLocationsSocket.emit('unsubscribe-route', routeId);
        console.log(`Unsubscribed from route: ${routeId}`);
      }
    },
    
    // Subscribe to nearby buses
    subscribeToNearbyBuses: (latitude: number, longitude: number, radius = 1000) => {
      if (busLocationsSocket) {
        busLocationsSocket.emit('subscribe-nearby', {
          latitude,
          longitude,
          radius
        });
        console.log(`Subscribed to nearby buses at ${latitude}, ${longitude} with radius ${radius}m`);
      }
    },
    
    // Update user location
    updateLocation: (latitude: number, longitude: number, radius = 1000) => {
      if (busLocationsSocket) {
        busLocationsSocket.emit('update-location', {
          latitude,
          longitude,
          radius
        });
        console.log(`Updated location to ${latitude}, ${longitude}`);
      }
    },
    
    // Listen for bus location updates
    onBusLocationUpdate: (callback: (data: BusLocation) => void) => {
      if (busLocationsSocket) {
        busLocationsSocket.on('bus-location-update', callback);
      }
    },
    
    // Remove bus location update listener
    offBusLocationUpdate: (callback?: (data: BusLocation) => void) => {
      if (busLocationsSocket) {
        if (callback) {
          busLocationsSocket.off('bus-location-update', callback);
        } else {
          busLocationsSocket.off('bus-location-update');
        }
      }
    }
  };
};

// Get the singleton instance
export const getSocketService = (): SocketService => {
  if (!instance) {
    instance = createSocketService();
  }
  return instance;
};

// Reset the instance (useful for testing)
export const resetSocketService = (): void => {
  if (instance) {
    instance.disconnect();
    instance = null;
  }
};
